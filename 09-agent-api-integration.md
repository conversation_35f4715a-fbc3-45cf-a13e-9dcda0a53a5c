# Agent 9: API & Integration Agent PRD

## Agent Overview

**Agent Name**: API & Integration Agent  
**Primary Responsibility**: FastAPI endpoints, API documentation, external service integrations  
**Dependencies**: All other agents (1-8) - Integration layer  
**Deliverables**: Unified API layer, OpenAPI documentation, external integrations, API versioning, rate limiting

## Mission Statement

Provide a cohesive, well-documented API layer that integrates all Project Chronos features while maintaining external service compatibility. Ensure API reliability, security, and ease of use for both internal components and potential third-party integrations.

## Technical Specifications

### Technology Stack
- **API Framework**: FastAPI with automatic OpenAPI generation
- **Documentation**: Swagger UI, ReDoc, custom API documentation
- **Rate Limiting**: Redis-based rate limiting with user-specific quotas
- **API Versioning**: URL-based versioning with backward compatibility
- **External APIs**: Google Calendar, Outlook, Todoist, Notion, Slack integrations
- **Monitoring**: API analytics, performance monitoring, error tracking

### Core Responsibilities

#### 1. Unified API Layer
```python
# app/api/main.py - Main API router and configuration
# app/api/v1/__init__.py - API v1 router aggregation
# app/api/dependencies.py - Shared API dependencies
# app/middleware/api.py - API middleware (CORS, rate limiting, etc.)
```

#### 2. External Service Integrations
```python
# app/integrations/google_calendar.py - Google Calendar integration
# app/integrations/outlook.py - Microsoft Outlook integration
# app/integrations/todoist.py - Todoist task sync
# app/integrations/notion.py - Notion workspace integration
# app/services/integration_service.py - Integration coordination
```

#### 3. API Documentation & Standards
```python
# app/api/docs.py - Custom API documentation
# app/schemas/api.py - API response schemas
# app/utils/api_utils.py - API utilities and helpers
```

## Key Features & User Stories

### Feature 1: Comprehensive API Documentation
**User Story**: "As a developer integrating with Project Chronos, I want comprehensive, interactive API documentation that shows exactly how to use each endpoint with real examples."

**Technical Requirements**:
- Auto-generated OpenAPI 3.0 specification
- Interactive Swagger UI with authentication support
- Code examples in multiple languages (Python, JavaScript, cURL)
- Comprehensive error response documentation
- Rate limiting and authentication guidance

### Feature 2: External Service Integrations
**User Story**: "As a user with existing productivity tools, I want Project Chronos to sync with my Google Calendar, Todoist, and other apps so I don't have to duplicate my work."

**Technical Requirements**:
- Bidirectional sync with Google Calendar and Outlook
- Task import/export with Todoist, Notion, and other task managers
- Slack integration for notifications and body doubling invites
- Webhook support for real-time synchronization
- Conflict resolution for overlapping data

### Feature 3: API Rate Limiting & Security
**User Story**: "As a system administrator, I want robust API security and rate limiting to prevent abuse while ensuring legitimate users have reliable access."

**Technical Requirements**:
- User-specific rate limiting with ADHD-friendly quotas
- API key management for external integrations
- Request/response logging and monitoring
- Error handling with helpful, non-technical messages
- API versioning with deprecation notices

## API Layer Implementation

### Main API Router
```python
# app/api/main.py
from fastapi import FastAPI, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
import time

from app.api.v1 import api_router as api_v1_router
from app.core.config import settings
from app.middleware.rate_limiting import RateLimitMiddleware
from app.middleware.api_monitoring import APIMonitoringMiddleware

def create_application() -> FastAPI:
    """Create and configure FastAPI application."""
    
    app = FastAPI(
        title="Project Chronos API",
        description="ADHD-focused productivity and time management API",
        version="1.0.0",
        docs_url="/docs",
        redoc_url="/redoc",
        openapi_url="/openapi.json"
    )
    
    # Add middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.ALLOWED_HOSTS,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    app.add_middleware(TrustedHostMiddleware, allowed_hosts=settings.ALLOWED_HOSTS)
    app.add_middleware(RateLimitMiddleware)
    app.add_middleware(APIMonitoringMiddleware)
    
    # Include routers
    app.include_router(api_v1_router, prefix="/api/v1")
    
    # Global exception handlers
    @app.exception_handler(HTTPException)
    async def http_exception_handler(request: Request, exc: HTTPException):
        """Handle HTTP exceptions with ADHD-friendly error messages."""
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "error": {
                    "code": exc.status_code,
                    "message": exc.detail,
                    "friendly_message": _get_friendly_error_message(exc.status_code),
                    "timestamp": time.time(),
                    "path": str(request.url)
                }
            }
        )
    
    @app.exception_handler(Exception)
    async def general_exception_handler(request: Request, exc: Exception):
        """Handle unexpected exceptions gracefully."""
        logger.error(f"Unexpected error: {exc}", exc_info=True)
        return JSONResponse(
            status_code=500,
            content={
                "error": {
                    "code": 500,
                    "message": "An unexpected error occurred",
                    "friendly_message": "Something went wrong on our end. Please try again in a moment.",
                    "timestamp": time.time(),
                    "path": str(request.url)
                }
            }
        )
    
    return app

def _get_friendly_error_message(status_code: int) -> str:
    """Get ADHD-friendly error messages."""
    friendly_messages = {
        400: "There seems to be an issue with your request. Please check your input and try again.",
        401: "You need to log in to access this feature.",
        403: "You don't have permission to access this resource.",
        404: "We couldn't find what you're looking for. It might have been moved or deleted.",
        429: "You're making requests too quickly. Please wait a moment and try again.",
        500: "Something went wrong on our end. We're working to fix it!"
    }
    return friendly_messages.get(status_code, "An error occurred. Please try again.")

app = create_application()
```

### API Router Aggregation
```python
# app/api/v1/__init__.py
from fastapi import APIRouter

from app.api.v1 import (
    auth,
    users,
    tasks,
    timeblocks,
    focus,
    body_doubling,
    notifications,
    gamification,
    integrations
)

api_router = APIRouter()

# Include all feature routers
api_router.include_router(auth.router, prefix="/auth", tags=["Authentication"])
api_router.include_router(users.router, prefix="/users", tags=["Users"])
api_router.include_router(tasks.router, prefix="/tasks", tags=["Tasks"])
api_router.include_router(timeblocks.router, prefix="/timeblocks", tags=["Time Blocking"])
api_router.include_router(focus.router, prefix="/focus", tags=["Focus Sessions"])
api_router.include_router(body_doubling.router, prefix="/body-doubling", tags=["Body Doubling"])
api_router.include_router(notifications.router, prefix="/notifications", tags=["Notifications"])
api_router.include_router(gamification.router, prefix="/gamification", tags=["Gamification"])
api_router.include_router(integrations.router, prefix="/integrations", tags=["Integrations"])

# Health check endpoint
@api_router.get("/health", tags=["System"])
async def health_check():
    """Health check endpoint for monitoring."""
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "version": "1.0.0"
    }
```

### External Service Integrations
```python
# app/services/integration_service.py
from typing import Dict, List, Optional, Any
from uuid import UUID
from datetime import datetime

class IntegrationService:
    """Service for managing external service integrations."""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.google_calendar = GoogleCalendarIntegration()
        self.outlook = OutlookIntegration()
        self.todoist = TodoistIntegration()
        self.notion = NotionIntegration()
    
    async def sync_calendar_events(
        self,
        user_id: UUID,
        integration_type: str,
        credentials: Dict[str, Any]
    ) -> SyncResult:
        """
        Sync calendar events from external service.
        
        Args:
            user_id: User performing the sync
            integration_type: Type of integration (google, outlook)
            credentials: Authentication credentials
            
        Returns:
            Sync result with imported events and conflicts
        """
        
        if integration_type == "google":
            events = await self.google_calendar.fetch_events(credentials)
        elif integration_type == "outlook":
            events = await self.outlook.fetch_events(credentials)
        else:
            raise ValueError(f"Unsupported integration type: {integration_type}")
        
        # Convert external events to time blocks
        imported_blocks = []
        conflicts = []
        
        for event in events:
            try:
                time_block = await self._convert_event_to_time_block(
                    user_id, event, integration_type
                )
                
                # Check for conflicts with existing schedule
                existing_conflicts = await self._check_schedule_conflicts(
                    user_id, time_block
                )
                
                if existing_conflicts:
                    conflicts.extend(existing_conflicts)
                else:
                    imported_blocks.append(time_block)
                    
            except Exception as e:
                logger.warning(f"Failed to import event {event.get('id')}: {e}")
        
        # Save imported time blocks
        for block in imported_blocks:
            self.db.add(block)
        
        await self.db.commit()
        
        return SyncResult(
            imported_count=len(imported_blocks),
            conflict_count=len(conflicts),
            conflicts=conflicts,
            last_sync=datetime.utcnow()
        )
    
    async def export_tasks_to_external(
        self,
        user_id: UUID,
        task_ids: List[UUID],
        integration_type: str,
        credentials: Dict[str, Any]
    ) -> ExportResult:
        """Export tasks to external service."""
        
        tasks = await self._get_tasks_for_export(user_id, task_ids)
        
        if integration_type == "todoist":
            result = await self.todoist.export_tasks(tasks, credentials)
        elif integration_type == "notion":
            result = await self.notion.export_tasks(tasks, credentials)
        else:
            raise ValueError(f"Unsupported export integration: {integration_type}")
        
        return result
    
    async def setup_webhook_integration(
        self,
        user_id: UUID,
        integration_type: str,
        webhook_config: Dict[str, Any]
    ) -> WebhookSetup:
        """Set up webhook for real-time synchronization."""
        
        webhook_url = f"{settings.BASE_URL}/api/v1/integrations/webhooks/{integration_type}"
        
        if integration_type == "google":
            webhook_id = await self.google_calendar.setup_webhook(
                webhook_url, webhook_config
            )
        elif integration_type == "slack":
            webhook_id = await self._setup_slack_webhook(webhook_url, webhook_config)
        else:
            raise ValueError(f"Webhook not supported for: {integration_type}")
        
        # Store webhook configuration
        webhook_setup = WebhookSetup(
            user_id=user_id,
            integration_type=integration_type,
            webhook_id=webhook_id,
            webhook_url=webhook_url,
            config=webhook_config
        )
        
        self.db.add(webhook_setup)
        await self.db.commit()
        
        return webhook_setup
```

### Rate Limiting Middleware
```python
# app/middleware/rate_limiting.py
from fastapi import Request, HTTPException
from starlette.middleware.base import BaseHTTPMiddleware
import time
from typing import Dict

class RateLimitMiddleware(BaseHTTPMiddleware):
    """ADHD-friendly rate limiting middleware."""
    
    def __init__(self, app, redis_client=None):
        super().__init__(app)
        self.redis = redis_client or Redis()
        
        # ADHD-friendly rate limits (more generous than typical)
        self.rate_limits = {
            "default": {"requests": 100, "window": 60},  # 100 requests per minute
            "auth": {"requests": 10, "window": 60},      # 10 auth attempts per minute
            "ai": {"requests": 20, "window": 60},        # 20 AI requests per minute
            "sync": {"requests": 5, "window": 300},      # 5 sync operations per 5 minutes
        }
    
    async def dispatch(self, request: Request, call_next):
        """Apply rate limiting based on endpoint and user."""
        
        # Determine rate limit category
        path = request.url.path
        category = self._get_rate_limit_category(path)
        
        # Get user identifier (IP or user ID)
        user_id = await self._get_user_identifier(request)
        
        # Check rate limit
        if not await self._check_rate_limit(user_id, category):
            raise HTTPException(
                status_code=429,
                detail={
                    "message": "Rate limit exceeded",
                    "friendly_message": "You're making requests too quickly. Please take a short break and try again.",
                    "retry_after": self.rate_limits[category]["window"]
                }
            )
        
        # Process request
        response = await call_next(request)
        
        # Record successful request
        await self._record_request(user_id, category)
        
        return response
    
    def _get_rate_limit_category(self, path: str) -> str:
        """Determine rate limit category based on path."""
        if "/auth/" in path:
            return "auth"
        elif "/ai/" in path or "/chunk" in path:
            return "ai"
        elif "/integrations/" in path and "/sync" in path:
            return "sync"
        else:
            return "default"
```

## API Endpoints

### Integration Management Endpoints
```python
# app/api/v1/integrations.py
@router.get("/", response_model=List[IntegrationResponse])
async def get_user_integrations(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get user's configured integrations."""

@router.post("/calendar/sync", response_model=SyncResultResponse)
async def sync_calendar_events(
    sync_request: CalendarSyncRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Sync events from external calendar service."""

@router.post("/tasks/export", response_model=ExportResultResponse)
async def export_tasks(
    export_request: TaskExportRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Export tasks to external service."""

@router.post("/webhooks/{integration_type}")
async def handle_webhook(
    integration_type: str,
    request: Request,
    db: AsyncSession = Depends(get_db)
):
    """Handle incoming webhooks from external services."""

@router.post("/oauth/callback/{integration_type}")
async def oauth_callback(
    integration_type: str,
    code: str,
    state: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Handle OAuth callback from external services."""
```

## Implementation Plan

### Phase 1: Core API Infrastructure (Week 1)
1. Set up FastAPI application with middleware
2. Implement API versioning and documentation
3. Create rate limiting and monitoring systems
4. Set up comprehensive error handling

### Phase 2: External Integrations (Week 2)
1. Implement Google Calendar integration
2. Add Outlook calendar sync
3. Create Todoist task import/export
4. Set up webhook infrastructure

### Phase 3: Advanced Integrations (Week 3)
1. Add Notion workspace integration
2. Implement Slack notifications and body doubling
3. Create conflict resolution for data sync
4. Add real-time webhook processing

### Phase 4: Documentation & Testing (Week 4)
1. Complete comprehensive API documentation
2. Add integration testing for all external services
3. Implement API monitoring and analytics
4. Create developer guides and examples

## Testing Strategy

### Unit Tests
```python
# tests/unit/test_api/test_rate_limiting.py
class TestRateLimiting:
    async def test_rate_limit_enforcement(self):
        """Test rate limiting with ADHD-friendly quotas."""
    
    async def test_rate_limit_categories(self):
        """Test different rate limits for different endpoint types."""

# tests/unit/test_integrations/test_calendar_sync.py
class TestCalendarSync:
    async def test_google_calendar_import(self):
        """Test Google Calendar event import."""
    
    async def test_conflict_resolution(self):
        """Test handling of scheduling conflicts during sync."""
```

### Integration Tests
```python
# tests/integration/test_api/test_external_integrations.py
class TestExternalIntegrations:
    async def test_complete_calendar_sync_flow(self):
        """Test complete calendar synchronization flow."""
    
    async def test_webhook_processing(self):
        """Test real-time webhook processing."""
```

### BDD Scenarios
```gherkin
Feature: External Service Integration
  Scenario: Syncing Google Calendar events
    Given I have connected my Google Calendar
    When I request a calendar sync
    Then my calendar events should be imported as time blocks
    And any conflicts should be clearly identified
    And I should receive a summary of the sync results

Feature: API Rate Limiting
  Scenario: ADHD-friendly rate limiting
    Given I am making API requests
    When I exceed the rate limit
    Then I should receive a helpful error message
    And be told how long to wait before trying again
    And the limits should be generous enough for typical ADHD usage patterns
```

## Quality Standards

### Performance Requirements
- API response times < 200ms for standard endpoints
- External integration sync < 30 seconds
- Rate limiting checks < 10ms
- 99.9% API uptime

### Documentation Requirements
- 100% endpoint documentation coverage
- Interactive examples for all endpoints
- Clear error response documentation
- Integration setup guides

## Success Metrics

### API Usage
- 95% API uptime
- < 1% error rate for API calls
- 80% of users successfully set up at least one integration
- 60% of users regularly sync external data

### Developer Experience
- 90% developer satisfaction with API documentation
- < 5 minutes average integration setup time
- 95% successful OAuth flow completion rate
- Clear, helpful error messages for all failure cases

## Deliverables

1. **Unified API Layer**: Complete FastAPI application with all endpoints
2. **External Integrations**: Google Calendar, Outlook, Todoist, Notion, Slack
3. **API Documentation**: Comprehensive OpenAPI docs with examples
4. **Rate Limiting**: ADHD-friendly rate limiting system
5. **Webhook Infrastructure**: Real-time synchronization support
6. **Test Suite**: Comprehensive API and integration testing
7. **Developer Guides**: Integration setup and usage documentation

## Integration Points

### Provides to Other Agents
- Unified API access to all features
- External service integration capabilities
- API documentation and standards
- Rate limiting and security infrastructure

### Requires from All Other Agents
- Service APIs and business logic
- Data models and schemas
- Authentication and authorization
- Feature-specific endpoints and functionality

## Commit Strategy

API-focused commits with clear integration features:
- `feat(api): Implement unified FastAPI application with comprehensive middleware`
- `feat(integrations): Add Google Calendar and Outlook synchronization`
- `feat(docs): Create comprehensive OpenAPI documentation with examples`
- `feat(rate-limit): Implement ADHD-friendly rate limiting system`
- `test(api): Add comprehensive API and integration test suite`
- `docs(api): Add developer guides and integration documentation`

This API layer will provide a cohesive, well-documented interface to all Project Chronos features while enabling seamless integration with external productivity tools that users with ADHD already rely on.
