"""
Notification models for Project Chronos.

This module defines models for the ADHD-optimized notification system,
including persistent reminders and gentle notification delivery.
"""

from datetime import datetime
from typing import List, Optional
from uuid import UUID

from sqlalchemy import Boolean, Foreign<PERSON>ey, Integer, String, Text, DateTime, ARRAY
from sqlalchemy.dialects.postgresql import UUID as PostgresUUID
from sqlalchemy.orm import Mapped, mapped_column, relationship

from chronos.app.models.base import Base, TimestampMixin, get_table_name


class Notification(Base, TimestampMixin):
    """
    Notification model with ADHD-specific persistence and delivery features.
    
    This model supports persistent reminders, staggered delivery, and
    gentle notification patterns optimized for users with ADHD.
    """
    
    __tablename__ = get_table_name("Notification")
    
    # Foreign keys
    user_id: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="ID of the user who will receive this notification"
    )
    
    task_id: Mapped[Optional[UUID]] = mapped_column(
        PostgresUUID(as_uuid=True),
        ForeignKey("tasks.id", ondelete="CASCADE"),
        nullable=True,
        doc="Associated task (if applicable)"
    )
    
    # Notification content
    type: Mapped[str] = mapped_column(
        String(50),
        nullable=False,
        doc="Notification type: reminder, deadline, focus_break, achievement, etc."
    )
    
    title: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        doc="Notification title"
    )
    
    message: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        doc="Detailed notification message"
    )
    
    # Scheduling and delivery
    scheduled_for: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        nullable=False,
        index=True,
        doc="When the notification should be delivered"
    )
    
    sent_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        doc="When the notification was actually sent"
    )
    
    acknowledged_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        doc="When the user acknowledged the notification"
    )
    
    # ADHD-specific features
    is_persistent: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        nullable=False,
        doc="Whether notification requires explicit acknowledgment"
    )
    
    priority: Mapped[str] = mapped_column(
        String(10),
        default="normal",
        nullable=False,
        doc="Notification priority: low, normal, high, urgent"
    )
    
    delivery_channels: Mapped[List[str]] = mapped_column(
        ARRAY(String),
        default=lambda: ["push"],
        nullable=False,
        doc="Delivery channels: push, email, sms"
    )
    
    # Retry and escalation
    retry_count: Mapped[int] = mapped_column(
        Integer,
        default=0,
        nullable=False,
        doc="Number of delivery attempts"
    )
    
    max_retries: Mapped[int] = mapped_column(
        Integer,
        default=3,
        nullable=False,
        doc="Maximum number of retry attempts"
    )
    
    escalated: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        nullable=False,
        doc="Whether notification has been escalated"
    )
    
    # Relationships
    user: Mapped["User"] = relationship(
        "User",
        back_populates="notifications",
        doc="User who will receive this notification"
    )
    
    task: Mapped[Optional["Task"]] = relationship(
        "Task",
        back_populates="notifications",
        doc="Associated task (if any)"
    )
    
    def __repr__(self) -> str:
        """String representation of Notification."""
        return f"<Notification(id={self.id}, type='{self.type}', title='{self.title}', persistent={self.is_persistent})>"
    
    def is_sent(self) -> bool:
        """
        Check if notification has been sent.
        
        Returns:
            bool: True if notification has been sent
        """
        return self.sent_at is not None
    
    def is_acknowledged(self) -> bool:
        """
        Check if notification has been acknowledged.
        
        Returns:
            bool: True if notification has been acknowledged
        """
        return self.acknowledged_at is not None
    
    def is_overdue(self) -> bool:
        """
        Check if notification is overdue for delivery.
        
        Returns:
            bool: True if notification should have been sent but wasn't
        """
        if self.is_sent():
            return False
        return datetime.utcnow() > self.scheduled_for
    
    def needs_acknowledgment(self) -> bool:
        """
        Check if notification needs acknowledgment.
        
        Returns:
            bool: True if persistent notification hasn't been acknowledged
        """
        return self.is_persistent and self.is_sent() and not self.is_acknowledged()
    
    def can_retry(self) -> bool:
        """
        Check if notification can be retried.
        
        Returns:
            bool: True if retry attempts are available
        """
        return self.retry_count < self.max_retries
    
    def should_escalate(self) -> bool:
        """
        Check if notification should be escalated.
        
        Returns:
            bool: True if notification should be escalated
        """
        if self.escalated or not self.is_persistent:
            return False
        
        # Escalate if persistent notification hasn't been acknowledged after 30 minutes
        if self.is_sent() and not self.is_acknowledged():
            time_since_sent = (datetime.utcnow() - self.sent_at).total_seconds()
            return time_since_sent > 1800  # 30 minutes
        
        return False
    
    def get_delivery_delay(self) -> int:
        """
        Get delivery delay in seconds.
        
        Returns:
            int: Seconds until scheduled delivery (negative if overdue)
        """
        delay = (self.scheduled_for - datetime.utcnow()).total_seconds()
        return int(delay)
    
    def mark_as_sent(self) -> None:
        """Mark notification as sent."""
        self.sent_at = datetime.utcnow()
    
    def acknowledge(self) -> None:
        """Mark notification as acknowledged."""
        self.acknowledged_at = datetime.utcnow()
    
    def increment_retry(self) -> None:
        """Increment retry count."""
        self.retry_count += 1
    
    def escalate(self) -> None:
        """Mark notification as escalated."""
        self.escalated = True
    
    def get_friendly_type_name(self) -> str:
        """
        Get user-friendly notification type name.
        
        Returns:
            str: Friendly type name
        """
        type_names = {
            "task_reminder": "Task Reminder",
            "deadline_warning": "Deadline Warning",
            "focus_break": "Focus Break",
            "body_doubling_invite": "Body Doubling Invitation",
            "achievement": "Achievement Unlocked",
            "daily_review": "Daily Review",
            "hyperfocus_warning": "Hyperfocus Check-in",
            "energy_check": "Energy Level Check",
        }
        return type_names.get(self.type, self.type.replace("_", " ").title())
    
    def get_urgency_level(self) -> int:
        """
        Get urgency level for notification sorting.
        
        Returns:
            int: Urgency level (1-5, higher = more urgent)
        """
        priority_levels = {"low": 1, "normal": 2, "high": 3, "urgent": 4}
        base_urgency = priority_levels.get(self.priority, 2)
        
        # Increase urgency for overdue notifications
        if self.is_overdue():
            base_urgency += 1
        
        # Increase urgency for persistent notifications needing acknowledgment
        if self.needs_acknowledgment():
            base_urgency += 1
        
        return min(5, base_urgency)
    
    def should_respect_focus_mode(self) -> bool:
        """
        Check if notification should respect focus mode.
        
        Returns:
            bool: True if notification should be deferred during focus
        """
        # Urgent notifications and focus breaks should not be deferred
        urgent_types = ["urgent", "focus_break", "hyperfocus_warning"]
        return self.priority not in urgent_types and self.type not in urgent_types
