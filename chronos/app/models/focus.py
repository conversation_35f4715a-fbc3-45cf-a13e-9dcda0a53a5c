"""
Focus session models for Project Chronos.

This module defines models for focus sessions, including Pomodoro timers,
deep work sessions, and hyperfocus tracking for ADHD users.
"""

from datetime import datetime
from typing import Any, Dict, Optional
from uuid import UUID

from sqlalchemy import Boolean, Foreign<PERSON>ey, Integer, String, DateTime
from sqlalchemy.dialects.postgresql import <PERSON><PERSON><PERSON><PERSON>, UUID as PostgresUUID
from sqlalchemy.orm import Mapped, mapped_column, relationship

from chronos.app.models.base import Base, TimestampMixin, get_table_name


class FocusSession(Base, TimestampMixin):
    """
    Focus session model for tracking work sessions and breaks.
    
    This model supports various focus techniques including Pomodoro,
    deep work sessions, and ADHD-specific focus patterns.
    """
    
    __tablename__ = get_table_name("FocusSession")
    
    # Foreign keys
    user_id: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="ID of the user who owns this focus session"
    )
    
    task_id: Mapped[Optional[UUID]] = mapped_column(
        PostgresUUID(as_uuid=True),
        ForeignKey("tasks.id", ondelete="SET NULL"),
        nullable=True,
        doc="Associated task (optional)"
    )
    
    # Session configuration
    session_type: Mapped[str] = mapped_column(
        String(20),
        default="pomodoro",
        nullable=False,
        doc="Session type: pomodoro, deep_work, sprint, custom"
    )
    
    planned_duration: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        doc="Planned session duration in minutes"
    )
    
    actual_duration: Mapped[Optional[int]] = mapped_column(
        Integer,
        nullable=True,
        doc="Actual session duration in minutes"
    )
    
    break_duration: Mapped[int] = mapped_column(
        Integer,
        default=5,
        nullable=False,
        doc="Break duration in minutes"
    )
    
    # Session status
    status: Mapped[str] = mapped_column(
        String(20),
        default="planned",
        nullable=False,
        doc="Session status: planned, active, paused, completed, cancelled"
    )
    
    # ADHD-specific focus mode settings
    focus_mode_settings: Mapped[Dict[str, Any]] = mapped_column(
        JSONB,
        default=dict,
        nullable=False,
        doc="Focus mode configuration and preferences"
    )
    
    # Session timing
    started_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        doc="When the session was started"
    )
    
    completed_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        doc="When the session was completed"
    )
    
    paused_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        doc="When the session was paused (if applicable)"
    )
    
    # Hyperfocus tracking
    hyperfocus_detected: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        nullable=False,
        doc="Whether hyperfocus was detected during this session"
    )
    
    hyperfocus_duration: Mapped[Optional[int]] = mapped_column(
        Integer,
        nullable=True,
        doc="Duration of hyperfocus in minutes"
    )
    
    # Relationships
    user: Mapped["User"] = relationship(
        "User",
        back_populates="focus_sessions",
        doc="User who owns this focus session"
    )
    
    task: Mapped[Optional["Task"]] = relationship(
        "Task",
        back_populates="focus_sessions",
        doc="Associated task (if any)"
    )
    
    def __repr__(self) -> str:
        """String representation of FocusSession."""
        return f"<FocusSession(id={self.id}, type='{self.session_type}', status='{self.status}', duration={self.planned_duration}min)>"
    
    def is_active(self) -> bool:
        """
        Check if session is currently active.
        
        Returns:
            bool: True if session is active or paused
        """
        return self.status in ["active", "paused"]
    
    def is_completed(self) -> bool:
        """
        Check if session is completed.
        
        Returns:
            bool: True if session is completed
        """
        return self.status == "completed"
    
    def get_elapsed_time(self) -> int:
        """
        Get elapsed time in minutes since session started.
        
        Returns:
            int: Elapsed time in minutes, or 0 if not started
        """
        if not self.started_at:
            return 0
        
        end_time = self.completed_at or datetime.utcnow()
        elapsed_seconds = (end_time - self.started_at).total_seconds()
        return int(elapsed_seconds / 60)
    
    def get_remaining_time(self) -> int:
        """
        Get remaining time in minutes.
        
        Returns:
            int: Remaining time in minutes, or 0 if completed/not started
        """
        if not self.started_at or self.is_completed():
            return 0
        
        elapsed = self.get_elapsed_time()
        return max(0, self.planned_duration - elapsed)
    
    def get_progress_percentage(self) -> float:
        """
        Get session progress as percentage.
        
        Returns:
            float: Progress percentage (0.0 to 1.0)
        """
        if not self.started_at:
            return 0.0
        
        if self.is_completed():
            return 1.0
        
        elapsed = self.get_elapsed_time()
        return min(1.0, elapsed / self.planned_duration)
    
    def detect_hyperfocus(self) -> bool:
        """
        Detect if user is in hyperfocus state.
        
        Returns:
            bool: True if hyperfocus is detected
        """
        if not self.started_at:
            return False
        
        # Get hyperfocus threshold from settings (default 2 hours)
        threshold_minutes = self.focus_mode_settings.get("hyperfocus_threshold", 120)
        elapsed = self.get_elapsed_time()
        
        if elapsed >= threshold_minutes and not self.hyperfocus_detected:
            self.hyperfocus_detected = True
            self.hyperfocus_duration = elapsed
            return True
        
        return self.hyperfocus_detected
    
    def get_focus_mode_setting(self, key: str, default: Any = None) -> Any:
        """
        Get a focus mode setting.
        
        Args:
            key: Setting key
            default: Default value if not found
            
        Returns:
            Any: Setting value or default
        """
        return self.focus_mode_settings.get(key, default)
    
    def set_focus_mode_setting(self, key: str, value: Any) -> None:
        """
        Set a focus mode setting.
        
        Args:
            key: Setting key
            value: Setting value
        """
        self.focus_mode_settings[key] = value
    
    def should_show_break_reminder(self) -> bool:
        """
        Check if break reminder should be shown.
        
        Returns:
            bool: True if break reminder should be shown
        """
        if not self.is_active() or not self.started_at:
            return False
        
        # Check if gentle reminders are enabled
        gentle_reminders = self.get_focus_mode_setting("gentle_reminders", True)
        if not gentle_reminders:
            return False
        
        elapsed = self.get_elapsed_time()
        
        # Show reminder if session is complete
        if elapsed >= self.planned_duration:
            return True
        
        # Show gentle reminder at 90% completion for ADHD users
        reminder_threshold = self.get_focus_mode_setting("reminder_threshold", 0.9)
        return elapsed >= (self.planned_duration * reminder_threshold)
    
    def get_session_summary(self) -> Dict[str, Any]:
        """
        Get session summary for analytics.
        
        Returns:
            Dict[str, Any]: Session summary data
        """
        return {
            "session_id": str(self.id),
            "session_type": self.session_type,
            "planned_duration": self.planned_duration,
            "actual_duration": self.actual_duration or self.get_elapsed_time(),
            "status": self.status,
            "hyperfocus_detected": self.hyperfocus_detected,
            "hyperfocus_duration": self.hyperfocus_duration,
            "completion_rate": self.get_progress_percentage(),
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
            "task_id": str(self.task_id) if self.task_id else None,
        }
