"""
Time block models for Project Chronos.

This module defines models for time blocking and scheduling features,
including visual time interfaces and buffer time management for ADHD users.
"""

from datetime import datetime, timed<PERSON>ta
from typing import Optional
from uuid import UUID

from sqlalchemy import <PERSON><PERSON>an, <PERSON><PERSON><PERSON>, <PERSON>teger, String, DateTime
from sqlalchemy.dialects.postgresql import UUID as PostgresUUID
from sqlalchemy.orm import Mapped, mapped_column, relationship

from chronos.app.models.base import Base, TimestampMixin, get_table_name


class TimeBlock(Base, TimestampMixin):
    """
    Time block model for visual time management and scheduling.
    
    This model supports drag-and-drop time blocking, buffer time management,
    and conflict detection for ADHD-friendly scheduling.
    """
    
    __tablename__ = get_table_name("TimeBlock")
    
    # Foreign keys
    user_id: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="ID of the user who owns this time block"
    )
    
    task_id: Mapped[Optional[UUID]] = mapped_column(
        PostgresUUID(as_uuid=True),
        ForeignKey("tasks.id", ondelete="SET NULL"),
        nullable=True,
        doc="Associated task (optional for breaks/events)"
    )
    
    # Time block details
    title: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        doc="Time block title"
    )
    
    description: Mapped[Optional[str]] = mapped_column(
        String(500),
        nullable=True,
        doc="Optional description"
    )
    
    # Timing
    start_time: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        nullable=False,
        index=True,
        doc="Start time of the block"
    )
    
    duration: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        doc="Duration in minutes"
    )
    
    # ADHD-specific features
    block_type: Mapped[str] = mapped_column(
        String(20),
        default="task",
        nullable=False,
        doc="Block type: task, break, buffer, event, focus"
    )
    
    is_flexible: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        nullable=False,
        doc="Whether block can be moved automatically"
    )
    
    buffer_before: Mapped[int] = mapped_column(
        Integer,
        default=0,
        nullable=False,
        doc="Buffer time before block in minutes"
    )
    
    buffer_after: Mapped[int] = mapped_column(
        Integer,
        default=0,
        nullable=False,
        doc="Buffer time after block in minutes"
    )
    
    # Visual properties
    color: Mapped[Optional[str]] = mapped_column(
        String(7),  # Hex color code
        nullable=True,
        doc="Color for visual representation"
    )
    
    # Status
    status: Mapped[str] = mapped_column(
        String(20),
        default="scheduled",
        nullable=False,
        doc="Block status: scheduled, in_progress, completed, cancelled"
    )
    
    # Relationships
    user: Mapped["User"] = relationship(
        "User",
        back_populates="time_blocks",
        doc="User who owns this time block"
    )
    
    task: Mapped[Optional["Task"]] = relationship(
        "Task",
        back_populates="time_blocks",
        doc="Associated task (if any)"
    )
    
    def __repr__(self) -> str:
        """String representation of TimeBlock."""
        return f"<TimeBlock(id={self.id}, title='{self.title}', start='{self.start_time}', duration={self.duration}min)>"
    
    @property
    def end_time(self) -> datetime:
        """
        Calculate end time of the block.
        
        Returns:
            datetime: End time including duration
        """
        return self.start_time + timedelta(minutes=self.duration)
    
    @property
    def total_duration_with_buffers(self) -> int:
        """
        Get total duration including buffer times.
        
        Returns:
            int: Total duration in minutes
        """
        return self.duration + self.buffer_before + self.buffer_after
    
    @property
    def actual_start_time(self) -> datetime:
        """
        Get actual start time including buffer before.
        
        Returns:
            datetime: Start time minus buffer before
        """
        return self.start_time - timedelta(minutes=self.buffer_before)
    
    @property
    def actual_end_time(self) -> datetime:
        """
        Get actual end time including buffer after.
        
        Returns:
            datetime: End time plus buffer after
        """
        return self.end_time + timedelta(minutes=self.buffer_after)
    
    def overlaps_with(self, other: "TimeBlock") -> bool:
        """
        Check if this time block overlaps with another.
        
        Args:
            other: Another time block to check against
            
        Returns:
            bool: True if blocks overlap (including buffers)
        """
        return (
            self.actual_start_time < other.actual_end_time and
            self.actual_end_time > other.actual_start_time
        )
    
    def get_overlap_duration(self, other: "TimeBlock") -> int:
        """
        Get overlap duration with another time block.
        
        Args:
            other: Another time block to check against
            
        Returns:
            int: Overlap duration in minutes, 0 if no overlap
        """
        if not self.overlaps_with(other):
            return 0
        
        overlap_start = max(self.actual_start_time, other.actual_start_time)
        overlap_end = min(self.actual_end_time, other.actual_end_time)
        
        overlap_duration = (overlap_end - overlap_start).total_seconds() / 60
        return int(overlap_duration)
    
    def is_in_progress(self) -> bool:
        """
        Check if time block is currently in progress.
        
        Returns:
            bool: True if block is currently active
        """
        now = datetime.utcnow()
        return (
            self.status == "in_progress" or
            (self.start_time <= now <= self.end_time and self.status == "scheduled")
        )
    
    def is_overdue(self) -> bool:
        """
        Check if time block is overdue.
        
        Returns:
            bool: True if block should have started but hasn't
        """
        return (
            datetime.utcnow() > self.start_time and
            self.status == "scheduled"
        )
    
    def get_time_until_start(self) -> int:
        """
        Get time until block starts.
        
        Returns:
            int: Minutes until start (negative if already started)
        """
        time_diff = (self.start_time - datetime.utcnow()).total_seconds()
        return int(time_diff / 60)
    
    def can_be_moved_to(self, new_start_time: datetime) -> bool:
        """
        Check if block can be moved to a new time.
        
        Args:
            new_start_time: Proposed new start time
            
        Returns:
            bool: True if block can be moved (considering flexibility)
        """
        if not self.is_flexible and self.status != "scheduled":
            return False
        
        # Don't allow moving to the past
        if new_start_time < datetime.utcnow():
            return False
        
        return True
    
    def get_visual_properties(self) -> dict:
        """
        Get properties for visual time interface.
        
        Returns:
            dict: Visual properties for rendering
        """
        # Default colors by block type
        default_colors = {
            "task": "#3B82F6",      # Blue
            "break": "#10B981",     # Green
            "buffer": "#F59E0B",    # Amber
            "event": "#8B5CF6",     # Purple
            "focus": "#EF4444",     # Red
        }
        
        return {
            "color": self.color or default_colors.get(self.block_type, "#6B7280"),
            "start_time": self.start_time.isoformat(),
            "end_time": self.end_time.isoformat(),
            "duration": self.duration,
            "buffer_before": self.buffer_before,
            "buffer_after": self.buffer_after,
            "is_flexible": self.is_flexible,
            "status": self.status,
            "type": self.block_type,
        }
    
    def get_circular_position(self, total_minutes: int = 1440) -> dict:
        """
        Get position for circular clock view.
        
        Args:
            total_minutes: Total minutes in the circle (default 24 hours)
            
        Returns:
            dict: Position data for circular visualization
        """
        # Calculate start angle (0 degrees = midnight)
        start_minutes = (
            self.start_time.hour * 60 + self.start_time.minute
        )
        start_angle = (start_minutes / total_minutes) * 360
        
        # Calculate arc length
        arc_degrees = (self.duration / total_minutes) * 360
        
        return {
            "start_angle": start_angle,
            "arc_degrees": arc_degrees,
            "end_angle": start_angle + arc_degrees,
            "radius_start": 60,  # Inner radius
            "radius_end": 100,   # Outer radius
        }
