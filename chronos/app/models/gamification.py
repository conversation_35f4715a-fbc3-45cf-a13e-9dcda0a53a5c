"""
Gamification models for Project Chronos.

This module defines models for the ADHD-optimized gamification system,
including points, achievements, streaks, and motivation features.
"""

from datetime import datetime, date
from typing import Any, Dict, Optional
from uuid import UUID

from sqlalchemy import <PERSON>olean, Date, ForeignKey, Integer, String, Text, DateTime
from sqlalchemy.dialects.postgresql import JSON<PERSON>, UUID as PostgresUUID
from sqlalchemy.orm import Mapped, mapped_column, relationship

from chronos.app.models.base import Base, TimestampMixin, get_table_name


class UserGamification(Base, TimestampMixin):
    """
    User gamification profile with ADHD-friendly progression.
    
    This model tracks overall gamification progress including points,
    level, and ADHD-specific motivation metrics.
    """
    
    __tablename__ = get_table_name("UserGamification")
    
    # Foreign key
    user_id: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        unique=True,
        index=True,
        doc="ID of the user this gamification profile belongs to"
    )
    
    # Points and progression
    total_points: Mapped[int] = mapped_column(
        Integer,
        default=0,
        nullable=False,
        doc="Total points earned by the user"
    )
    
    level: Mapped[int] = mapped_column(
        Integer,
        default=1,
        nullable=False,
        doc="Current user level"
    )
    
    points_to_next_level: Mapped[int] = mapped_column(
        Integer,
        default=100,
        nullable=False,
        doc="Points needed to reach next level"
    )
    
    # ADHD-specific metrics
    dopamine_menu_uses: Mapped[int] = mapped_column(
        Integer,
        default=0,
        nullable=False,
        doc="Number of times dopamine menu was used"
    )
    
    task_jar_uses: Mapped[int] = mapped_column(
        Integer,
        default=0,
        nullable=False,
        doc="Number of times task jar was used"
    )
    
    hyperfocus_sessions: Mapped[int] = mapped_column(
        Integer,
        default=0,
        nullable=False,
        doc="Number of hyperfocus sessions detected"
    )
    
    # Relationships
    user: Mapped["User"] = relationship(
        "User",
        back_populates="gamification",
        doc="User this gamification profile belongs to"
    )
    
    def __repr__(self) -> str:
        """String representation of UserGamification."""
        return f"<UserGamification(user_id={self.user_id}, level={self.level}, points={self.total_points})>"


class Achievement(Base, TimestampMixin):
    """
    Achievement definition with ADHD-friendly milestones.
    
    This model defines available achievements that users can unlock
    through various ADHD-focused activities and milestones.
    """
    
    __tablename__ = get_table_name("Achievement")
    
    # Achievement details
    name: Mapped[str] = mapped_column(
        String(100),
        nullable=False,
        unique=True,
        doc="Achievement name"
    )
    
    description: Mapped[str] = mapped_column(
        Text,
        nullable=False,
        doc="Achievement description"
    )
    
    category: Mapped[str] = mapped_column(
        String(50),
        nullable=False,
        doc="Achievement category: task_completion, consistency, focus_time, social, milestone"
    )
    
    # Reward
    points_reward: Mapped[int] = mapped_column(
        Integer,
        default=0,
        nullable=False,
        doc="Points awarded for this achievement"
    )
    
    badge_icon: Mapped[Optional[str]] = mapped_column(
        String(10),
        nullable=True,
        doc="Emoji or icon for the badge"
    )
    
    # Achievement criteria
    criteria: Mapped[Dict[str, Any]] = mapped_column(
        JSONB,
        default=dict,
        nullable=False,
        doc="Achievement criteria and requirements"
    )
    
    # Visibility
    is_hidden: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        nullable=False,
        doc="Whether achievement is hidden until unlocked"
    )
    
    def __repr__(self) -> str:
        """String representation of Achievement."""
        return f"<Achievement(id={self.id}, name='{self.name}', category='{self.category}')>"


class UserAchievement(Base, TimestampMixin):
    """
    User achievement unlock record.
    
    This model tracks when users unlock specific achievements
    and provides celebration data.
    """
    
    __tablename__ = get_table_name("UserAchievement")
    
    # Foreign keys
    user_id: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="ID of the user who unlocked the achievement"
    )
    
    achievement_id: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True),
        ForeignKey("achievements.id", ondelete="CASCADE"),
        nullable=False,
        doc="ID of the unlocked achievement"
    )
    
    # Unlock details
    unlocked_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        default=datetime.utcnow,
        nullable=False,
        doc="When the achievement was unlocked"
    )
    
    points_earned: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        doc="Points earned from this achievement"
    )
    
    # Relationships
    user: Mapped["User"] = relationship(
        "User",
        back_populates="achievements",
        doc="User who unlocked the achievement"
    )
    
    achievement: Mapped["Achievement"] = relationship(
        "Achievement",
        doc="The unlocked achievement"
    )
    
    def __repr__(self) -> str:
        """String representation of UserAchievement."""
        return f"<UserAchievement(user_id={self.user_id}, achievement_id={self.achievement_id})>"


class Streak(Base, TimestampMixin):
    """
    User streak tracking with ADHD-friendly flexibility.
    
    This model tracks various types of streaks with forgiveness
    mechanisms for ADHD users.
    """
    
    __tablename__ = get_table_name("Streak")
    
    # Foreign key
    user_id: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="ID of the user this streak belongs to"
    )
    
    # Streak details
    streak_type: Mapped[str] = mapped_column(
        String(50),
        nullable=False,
        doc="Type of streak: daily_tasks, focus_sessions, body_doubling, etc."
    )
    
    current_streak: Mapped[int] = mapped_column(
        Integer,
        default=0,
        nullable=False,
        doc="Current streak count"
    )
    
    longest_streak: Mapped[int] = mapped_column(
        Integer,
        default=0,
        nullable=False,
        doc="Longest streak achieved"
    )
    
    # Tracking
    last_activity_date: Mapped[Optional[date]] = mapped_column(
        Date,
        nullable=True,
        doc="Date of last activity for this streak"
    )
    
    # ADHD-friendly features
    freeze_count: Mapped[int] = mapped_column(
        Integer,
        default=0,
        nullable=False,
        doc="Number of streak freezes used"
    )
    
    max_freezes_per_month: Mapped[int] = mapped_column(
        Integer,
        default=3,
        nullable=False,
        doc="Maximum freezes allowed per month"
    )
    
    # Relationships
    user: Mapped["User"] = relationship(
        "User",
        back_populates="streaks",
        doc="User this streak belongs to"
    )
    
    def __repr__(self) -> str:
        """String representation of Streak."""
        return f"<Streak(user_id={self.user_id}, type='{self.streak_type}', current={self.current_streak})>"
    
    def can_use_freeze(self) -> bool:
        """
        Check if user can use a streak freeze.
        
        Returns:
            bool: True if freeze is available
        """
        return self.freeze_count < self.max_freezes_per_month
    
    def use_freeze(self) -> bool:
        """
        Use a streak freeze to maintain the streak.
        
        Returns:
            bool: True if freeze was successfully used
        """
        if self.can_use_freeze():
            self.freeze_count += 1
            return True
        return False


class PointsAward(Base, TimestampMixin):
    """
    Points award record for tracking point history.
    
    This model tracks all point awards with context for
    analytics and user feedback.
    """
    
    __tablename__ = get_table_name("PointsAward")
    
    # Foreign keys
    user_id: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="ID of the user who received points"
    )
    
    task_id: Mapped[Optional[UUID]] = mapped_column(
        PostgresUUID(as_uuid=True),
        ForeignKey("tasks.id", ondelete="SET NULL"),
        nullable=True,
        doc="Associated task (if applicable)"
    )
    
    # Award details
    points_awarded: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        doc="Number of points awarded"
    )
    
    reason: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        doc="Reason for point award"
    )
    
    multiplier: Mapped[float] = mapped_column(
        Integer,  # Store as integer (multiplied by 100)
        default=100,
        nullable=False,
        doc="Multiplier applied (stored as integer, divide by 100)"
    )
    
    total_points_after: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        doc="User's total points after this award"
    )
    
    # Relationships
    user: Mapped["User"] = relationship(
        "User",
        back_populates="points_awards",
        doc="User who received the points"
    )
    
    task: Mapped[Optional["Task"]] = relationship(
        "Task",
        doc="Associated task (if any)"
    )
    
    def __repr__(self) -> str:
        """String representation of PointsAward."""
        return f"<PointsAward(user_id={self.user_id}, points={self.points_awarded}, reason='{self.reason}')>"
    
    @property
    def actual_multiplier(self) -> float:
        """Get actual multiplier value."""
        return self.multiplier / 100.0
