"""
Base model classes and mixins for Project Chronos.

This module provides common functionality for all database models,
including timestamp tracking and ADHD-specific field patterns.
"""

from datetime import datetime
from typing import Any
from uuid import UUID, uuid4

from sqlalchemy import DateTime, func
from sqlalchemy.dialects.postgresql import UUID as PostgresUUID
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column


class Base(DeclarativeBase):
    """
    Base class for all SQLAlchemy models.
    
    This declarative base provides the foundation for all database
    models in Project Chronos with consistent UUID primary keys.
    """
    
    # Use UUID primary keys for all models
    id: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True),
        primary_key=True,
        default=uuid4,
        doc="Unique identifier for the record"
    )


class TimestampMixin:
    """
    Mixin for adding timestamp fields to models.
    
    This mixin provides created_at and updated_at fields with automatic
    timestamp management for tracking record lifecycle.
    """
    
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        doc="Timestamp when the record was created"
    )
    
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        doc="Timestamp when the record was last updated"
    )


class ADHDMixin:
    """
    Mixin for ADHD-specific fields and functionality.
    
    This mixin provides common ADHD-related fields that can be used
    across multiple models to support neurodivergent users.
    """
    
    def get_energy_level_display(self, energy_level: str) -> str:
        """
        Get user-friendly display text for energy levels.
        
        Args:
            energy_level: Energy level code (low, medium, high)
            
        Returns:
            str: User-friendly energy level description
        """
        energy_displays = {
            "low": "Low energy - Perfect for quick, easy tasks",
            "medium": "Medium energy - Good for regular tasks",
            "high": "High energy - Great for challenging work"
        }
        return energy_displays.get(energy_level, energy_level)
    
    def get_priority_display(self, priority: str) -> str:
        """
        Get user-friendly display text for priority levels.
        
        Args:
            priority: Priority code (low, medium, high, urgent)
            
        Returns:
            str: User-friendly priority description
        """
        priority_displays = {
            "low": "Low priority - Do when you have time",
            "medium": "Medium priority - Important but not urgent",
            "high": "High priority - Should be done soon",
            "urgent": "Urgent - Needs immediate attention"
        }
        return priority_displays.get(priority, priority)


class SoftDeleteMixin:
    """
    Mixin for soft delete functionality.
    
    This mixin allows records to be marked as deleted without actually
    removing them from the database, which is helpful for ADHD users
    who might accidentally delete important items.
    """
    
    deleted_at: Mapped[datetime | None] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        default=None,
        doc="Timestamp when the record was soft deleted"
    )
    
    is_deleted: Mapped[bool] = mapped_column(
        default=False,
        nullable=False,
        doc="Whether the record is soft deleted"
    )
    
    def soft_delete(self) -> None:
        """Mark the record as deleted without removing it."""
        self.is_deleted = True
        self.deleted_at = datetime.utcnow()
    
    def restore(self) -> None:
        """Restore a soft-deleted record."""
        self.is_deleted = False
        self.deleted_at = None


def get_table_name(cls_name: str) -> str:
    """
    Generate table name from class name.
    
    Converts CamelCase class names to snake_case table names
    following PostgreSQL naming conventions.
    
    Args:
        cls_name: Class name in CamelCase
        
    Returns:
        str: Table name in snake_case
        
    Example:
        >>> get_table_name("UserProfile")
        "user_profiles"
    """
    import re
    
    # Convert CamelCase to snake_case
    s1 = re.sub('(.)([A-Z][a-z]+)', r'\1_\2', cls_name)
    snake_case = re.sub('([a-z0-9])([A-Z])', r'\1_\2', s1).lower()
    
    # Pluralize table name
    if snake_case.endswith('y'):
        return snake_case[:-1] + 'ies'
    elif snake_case.endswith(('s', 'sh', 'ch', 'x', 'z')):
        return snake_case + 'es'
    else:
        return snake_case + 's'
