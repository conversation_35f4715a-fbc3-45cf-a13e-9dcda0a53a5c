"""
Focus session utilities for Project Chronos.

This module provides utility functions for focus sessions, including
ADHD-specific calculations and default configurations.
"""

from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional


def calculate_optimal_break_duration(
    session_duration: int,
    session_type: str,
    user_preferences: Optional[Dict[str, Any]] = None
) -> int:
    """
    Calculate optimal break duration based on session length and type.
    
    Uses ADHD-friendly break ratios and user preferences.
    
    Args:
        session_duration: Session duration in minutes
        session_type: Type of session (pomodoro, deep_work, etc.)
        user_preferences: User's break preferences
        
    Returns:
        Optimal break duration in minutes
    """
    user_preferences = user_preferences or {}
    
    # Default break ratios for different session types
    break_ratios = {
        "pomodoro": 0.2,  # 20% of session time (5min for 25min session)
        "deep_work": 0.15,  # 15% for longer sessions
        "sprint": 0.25,  # 25% for short intense sessions
        "custom": 0.2,  # Default ratio
    }
    
    # Get user's preferred ratio or use default
    ratio = user_preferences.get("break_ratio", break_ratios.get(session_type, 0.2))
    
    # Calculate base break duration
    base_break = int(session_duration * ratio)
    
    # Apply ADHD-specific adjustments
    min_break = user_preferences.get("min_break_duration", 5)
    max_break = user_preferences.get("max_break_duration", 30)
    
    # Ensure break is within reasonable bounds
    optimal_break = max(min_break, min(max_break, base_break))
    
    return optimal_break


def detect_hyperfocus_pattern(
    session_duration: int,
    planned_duration: int,
    user_history: Optional[List[Dict[str, Any]]] = None
) -> Dict[str, Any]:
    """
    Detect if user is showing hyperfocus patterns.
    
    Args:
        session_duration: Current session duration in minutes
        planned_duration: Originally planned duration in minutes
        user_history: User's session history for pattern analysis
        
    Returns:
        Dictionary with hyperfocus detection results
    """
    user_history = user_history or []
    
    # Basic hyperfocus indicators
    is_hyperfocus = False
    confidence = 0.0
    indicators = []
    
    # Check if session is significantly longer than planned
    if session_duration > planned_duration * 1.5:
        is_hyperfocus = True
        confidence += 0.3
        indicators.append("session_overrun")
    
    # Check if session is longer than typical hyperfocus threshold (2 hours)
    if session_duration >= 120:
        is_hyperfocus = True
        confidence += 0.4
        indicators.append("extended_duration")
    
    # Analyze user's historical patterns
    if user_history:
        avg_session_length = sum(s.get("duration", 0) for s in user_history) / len(user_history)
        
        # If current session is much longer than user's average
        if session_duration > avg_session_length * 2:
            confidence += 0.2
            indicators.append("above_average_pattern")
        
        # Check for consecutive long sessions (hyperfocus episodes)
        recent_sessions = user_history[-5:]  # Last 5 sessions
        long_sessions = [s for s in recent_sessions if s.get("duration", 0) > 90]
        
        if len(long_sessions) >= 3:
            confidence += 0.1
            indicators.append("consecutive_long_sessions")
    
    # Cap confidence at 1.0
    confidence = min(1.0, confidence)
    
    return {
        "is_hyperfocus": is_hyperfocus,
        "confidence": confidence,
        "indicators": indicators,
        "recommended_action": _get_hyperfocus_recommendation(confidence, session_duration),
        "break_urgency": _calculate_break_urgency(session_duration, confidence)
    }


def _get_hyperfocus_recommendation(confidence: float, duration: int) -> str:
    """Get recommendation based on hyperfocus confidence and duration."""
    if confidence >= 0.7:
        return "strong_break_suggestion"
    elif confidence >= 0.5:
        return "gentle_reminder"
    elif duration >= 90:
        return "awareness_nudge"
    else:
        return "continue_monitoring"


def _calculate_break_urgency(duration: int, confidence: float) -> str:
    """Calculate urgency level for break suggestions."""
    if duration >= 180 or confidence >= 0.8:  # 3+ hours or high confidence
        return "high"
    elif duration >= 120 or confidence >= 0.6:  # 2+ hours or medium confidence
        return "medium"
    elif duration >= 90 or confidence >= 0.4:  # 1.5+ hours or low confidence
        return "low"
    else:
        return "none"


def generate_gentle_reminder_message(
    reminder_type: str,
    session_duration: int,
    user_preferences: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Generate gentle, ADHD-friendly reminder messages.
    
    Args:
        reminder_type: Type of reminder (break, hydration, movement, etc.)
        session_duration: Current session duration in minutes
        user_preferences: User's messaging preferences
        
    Returns:
        Dictionary with message content and options
    """
    user_preferences = user_preferences or {}
    tone = user_preferences.get("reminder_tone", "gentle")
    
    messages = {
        "break": {
            "gentle": [
                f"You've been focused for {session_duration} minutes. How are you feeling?",
                f"Great focus! You've been working for {session_duration} minutes. Ready for a pause?",
                f"You're doing amazing! Consider taking a moment to recharge after {session_duration} minutes of focus."
            ],
            "encouraging": [
                f"Wow! {session_duration} minutes of solid focus! You're crushing it!",
                f"Look at you go! {session_duration} minutes of deep work. Time to celebrate with a break?",
                f"You're on fire! {session_duration} minutes of focus deserves a well-earned break."
            ],
            "minimal": [
                f"Focus session: {session_duration} minutes",
                f"Time check: {session_duration} minutes",
                f"Session update: {session_duration} minutes"
            ]
        },
        "hydration": {
            "gentle": [
                "Your brain has been working hard! How about some water?",
                "Time for a hydration check! Your focused mind needs fuel.",
                "Remember to drink some water - your brain will thank you!"
            ],
            "encouraging": [
                "Hydration station! Keep that brilliant brain of yours happy!",
                "Water break time! You're doing great, keep yourself fueled!",
                "Drink up, superstar! Your brain needs hydration to keep being awesome!"
            ],
            "minimal": [
                "Hydration reminder",
                "Water break",
                "Drink water"
            ]
        },
        "movement": {
            "gentle": [
                "Your body might appreciate a gentle stretch after all that focus.",
                "Consider taking a moment to move around and reset.",
                "A little movement can help refresh your mind and body."
            ],
            "encouraging": [
                "Movement time! Get those endorphins flowing!",
                "Stretch it out! Your body has been supporting your amazing focus!",
                "Time to move that brilliant body of yours!"
            ],
            "minimal": [
                "Movement break",
                "Stretch reminder",
                "Move around"
            ]
        },
        "eye_strain": {
            "gentle": [
                "Your eyes have been working hard. Try looking at something far away for a moment.",
                "Give your eyes a break with the 20-20-20 rule: look 20 feet away for 20 seconds.",
                "Time to rest those hardworking eyes of yours."
            ],
            "encouraging": [
                "Eye break time! Look away and give those amazing eyes a rest!",
                "Your eyes are heroes! Give them a 20-second vacation!",
                "Eye care time! Look far away and let your vision reset!"
            ],
            "minimal": [
                "Eye break",
                "Look away",
                "Rest eyes"
            ]
        }
    }
    
    # Get appropriate message
    message_list = messages.get(reminder_type, {}).get(tone, ["Time for a break!"])
    import random
    message = random.choice(message_list)
    
    # Generate options based on reminder type
    options = _get_reminder_options(reminder_type, session_duration)
    
    return {
        "type": reminder_type,
        "message": message,
        "options": options,
        "tone": tone,
        "dismissible": user_preferences.get("dismissible_reminders", True),
        "auto_dismiss_seconds": user_preferences.get("auto_dismiss_seconds", 30)
    }


def _get_reminder_options(reminder_type: str, session_duration: int) -> List[str]:
    """Get appropriate options for reminder type."""
    base_options = ["continue", "take_break"]
    
    if session_duration >= 90:  # Long session
        base_options.append("extend_15min")
    
    if reminder_type == "break":
        return base_options + ["quick_break_5min"]
    elif reminder_type in ["hydration", "movement", "eye_strain"]:
        return ["done", "remind_later", "continue"]
    else:
        return base_options


def get_default_focus_modes() -> List[Dict[str, Any]]:
    """
    Get default focus mode templates for ADHD users.
    
    Returns:
        List of default focus mode configurations
    """
    return [
        {
            "name": "Pomodoro Classic",
            "description": "Traditional 25-minute focus with 5-minute breaks",
            "settings": {
                "session_duration": 25,
                "break_duration": 5,
                "long_break_duration": 15,
                "sessions_until_long_break": 4,
                "gentle_reminders": True,
                "reminder_threshold": 0.9,
                "hyperfocus_threshold": 120,
                "notification_sounds": "gentle",
                "break_suggestions": ["hydration", "movement", "eye_strain"]
            },
            "is_default": True
        },
        {
            "name": "Deep Work",
            "description": "90-minute focused sessions with longer breaks",
            "settings": {
                "session_duration": 90,
                "break_duration": 20,
                "gentle_reminders": True,
                "reminder_threshold": 0.8,
                "hyperfocus_threshold": 150,
                "notification_sounds": "minimal",
                "break_suggestions": ["movement", "hydration", "fresh_air"]
            },
            "is_default": True
        },
        {
            "name": "Creative Flow",
            "description": "Flexible duration with gentle hyperfocus protection",
            "settings": {
                "session_duration": 60,
                "break_duration": 10,
                "flexible_duration": True,
                "gentle_reminders": True,
                "reminder_threshold": 0.7,
                "hyperfocus_threshold": 180,
                "notification_sounds": "ambient",
                "break_suggestions": ["inspiration", "movement", "hydration"]
            },
            "is_default": True
        },
        {
            "name": "Study Session",
            "description": "45-minute focused study with structured breaks",
            "settings": {
                "session_duration": 45,
                "break_duration": 15,
                "gentle_reminders": True,
                "reminder_threshold": 0.85,
                "hyperfocus_threshold": 120,
                "notification_sounds": "gentle",
                "break_suggestions": ["review", "movement", "hydration"]
            },
            "is_default": True
        },
        {
            "name": "Quick Sprint",
            "description": "15-minute focused bursts for quick tasks",
            "settings": {
                "session_duration": 15,
                "break_duration": 5,
                "gentle_reminders": False,
                "reminder_threshold": 1.0,
                "hyperfocus_threshold": 60,
                "notification_sounds": "minimal",
                "break_suggestions": ["stretch", "hydration"]
            },
            "is_default": True
        }
    ]
