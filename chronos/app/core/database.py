"""
Database connection and session management for Project Chronos.

This module provides async database connectivity using SQLAlchemy 2.0+
with PostgreSQL and asyncpg driver, optimized for ADHD-focused features.
"""

from typing import AsyncGenerator

from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker, create_async_engine
from sqlalchemy.orm import DeclarativeBase

from chronos.app.core.config import settings


class Base(DeclarativeBase):
    """
    Base class for all SQLAlchemy models.
    
    This declarative base provides common functionality for all database
    models in Project Chronos, including ADHD-specific field patterns.
    """
    pass


# Create async engine with optimized settings for ADHD app workloads
engine = create_async_engine(
    str(settings.DATABASE_URL),
    echo=settings.LOG_LEVEL == "DEBUG",
    future=True,
    pool_size=20,
    max_overflow=30,
    pool_pre_ping=True,
    pool_recycle=3600,  # 1 hour
)

# Create async session factory
AsyncSessionLocal = async_sessionmaker(
    engine,
    class_=AsyncSession,
    expire_on_commit=False,
    autoflush=True,
    autocommit=False,
)


async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """
    Dependency for getting database session.
    
    This function provides a database session for FastAPI dependency injection,
    ensuring proper session lifecycle management for async operations.
    
    Yields:
        AsyncSession: Database session for async operations
        
    Example:
        ```python
        @app.get("/users/")
        async def get_users(db: AsyncSession = Depends(get_db)):
            result = await db.execute(select(User))
            return result.scalars().all()
        ```
    """
    async with AsyncSessionLocal() as session:
        try:
            yield session
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()


async def create_tables() -> None:
    """
    Create all database tables.
    
    This function creates all tables defined in the SQLAlchemy models.
    Used primarily for testing and initial setup.
    
    Note:
        In production, use Alembic migrations instead of this function.
    """
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)


async def drop_tables() -> None:
    """
    Drop all database tables.
    
    This function drops all tables defined in the SQLAlchemy models.
    Used primarily for testing cleanup.
    
    Warning:
        This will permanently delete all data. Use with extreme caution.
    """
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)


async def check_db_connection() -> bool:
    """
    Check database connection health.
    
    This function verifies that the database connection is working properly
    by executing a simple query.
    
    Returns:
        bool: True if connection is healthy, False otherwise
        
    Example:
        ```python
        if await check_db_connection():
            print("Database connection is healthy")
        else:
            print("Database connection failed")
        ```
    """
    try:
        async with AsyncSessionLocal() as session:
            await session.execute("SELECT 1")
            return True
    except Exception:
        return False


class DatabaseManager:
    """
    Database management utilities for Project Chronos.
    
    This class provides utilities for database operations, health checks,
    and maintenance tasks specific to ADHD-focused features.
    """
    
    def __init__(self) -> None:
        """Initialize database manager."""
        self.engine = engine
        self.session_factory = AsyncSessionLocal
    
    async def health_check(self) -> dict:
        """
        Perform comprehensive database health check.
        
        Returns:
            dict: Health check results with connection status and metrics
        """
        try:
            async with self.session_factory() as session:
                # Test basic connectivity
                await session.execute("SELECT 1")
                
                # Test ADHD-specific table access (when models are created)
                # This will be expanded as models are implemented
                
                return {
                    "status": "healthy",
                    "connection": "active",
                    "engine_pool_size": self.engine.pool.size(),
                    "engine_checked_out": self.engine.pool.checkedout(),
                }
        except Exception as e:
            return {
                "status": "unhealthy",
                "connection": "failed",
                "error": str(e),
            }
    
    async def get_connection_info(self) -> dict:
        """
        Get database connection information.
        
        Returns:
            dict: Connection information and pool statistics
        """
        return {
            "database_url": str(settings.DATABASE_URL).split("@")[-1],  # Hide credentials
            "pool_size": self.engine.pool.size(),
            "max_overflow": self.engine.pool._max_overflow,
            "checked_out_connections": self.engine.pool.checkedout(),
            "invalid_connections": self.engine.pool.invalidated(),
        }


# Global database manager instance
db_manager = DatabaseManager()
