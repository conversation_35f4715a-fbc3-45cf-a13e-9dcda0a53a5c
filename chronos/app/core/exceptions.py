"""
Custom exception classes for Project Chronos.

This module defines ADHD-friendly exception classes with clear, helpful
error messages that reduce cognitive load and provide actionable guidance.
"""

from typing import Any, Dict, Optional


class ChronosException(Exception):
    """
    Base exception class for Project Chronos.
    
    All custom exceptions inherit from this base class, providing
    consistent error handling and ADHD-friendly messaging.
    """
    
    def __init__(
        self,
        message: str,
        friendly_message: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        error_code: Optional[str] = None,
    ) -> None:
        """
        Initialize Chronos exception.
        
        Args:
            message: Technical error message for developers
            friendly_message: User-friendly message for ADHD users
            details: Additional error details and context
            error_code: Unique error code for tracking
        """
        super().__init__(message)
        self.message = message
        self.friendly_message = friendly_message or self._get_default_friendly_message()
        self.details = details or {}
        self.error_code = error_code or self.__class__.__name__
    
    def _get_default_friendly_message(self) -> str:
        """Get default user-friendly message."""
        return "Something went wrong. Please try again or contact support if the problem continues."
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert exception to dictionary for API responses."""
        return {
            "error_code": self.error_code,
            "message": self.message,
            "friendly_message": self.friendly_message,
            "details": self.details,
        }


# Database-related exceptions
class DatabaseError(ChronosException):
    """Database operation failed."""
    
    def _get_default_friendly_message(self) -> str:
        return "We're having trouble saving your data. Please try again in a moment."


class RecordNotFoundError(ChronosException):
    """Requested record was not found."""
    
    def _get_default_friendly_message(self) -> str:
        return "We couldn't find what you're looking for. It might have been moved or deleted."


class DuplicateRecordError(ChronosException):
    """Attempted to create duplicate record."""
    
    def _get_default_friendly_message(self) -> str:
        return "This item already exists. Please check your input and try again."


# Authentication and authorization exceptions
class AuthenticationError(ChronosException):
    """Authentication failed."""
    
    def _get_default_friendly_message(self) -> str:
        return "Please log in to access this feature."


class AuthorizationError(ChronosException):
    """User lacks required permissions."""
    
    def _get_default_friendly_message(self) -> str:
        return "You don't have permission to access this resource."


class InvalidCredentialsError(AuthenticationError):
    """Invalid login credentials provided."""
    
    def _get_default_friendly_message(self) -> str:
        return "Your email or password is incorrect. Please try again."


class TokenExpiredError(AuthenticationError):
    """Authentication token has expired."""
    
    def _get_default_friendly_message(self) -> str:
        return "Your session has expired. Please log in again."


# Task management exceptions
class TaskError(ChronosException):
    """Base exception for task-related errors."""
    
    def _get_default_friendly_message(self) -> str:
        return "There was a problem with your task. Please try again."


class TaskNotFoundError(TaskError):
    """Task was not found."""
    
    def _get_default_friendly_message(self) -> str:
        return "We couldn't find that task. It might have been completed or deleted."


class TaskValidationError(TaskError):
    """Task data validation failed."""
    
    def _get_default_friendly_message(self) -> str:
        return "Please check your task details and try again."


class TaskChunkingError(TaskError):
    """AI task chunking failed."""
    
    def _get_default_friendly_message(self) -> str:
        return "We couldn't break down your task right now. You can try again or break it down manually."


# Time blocking exceptions
class TimeBlockError(ChronosException):
    """Base exception for time blocking errors."""
    
    def _get_default_friendly_message(self) -> str:
        return "There was a problem with your schedule. Please try again."


class SchedulingConflictError(TimeBlockError):
    """Time block conflicts with existing schedule."""
    
    def _get_default_friendly_message(self) -> str:
        return "This time slot conflicts with something else in your schedule. Please choose a different time."


class InvalidTimeBlockError(TimeBlockError):
    """Time block data is invalid."""
    
    def _get_default_friendly_message(self) -> str:
        return "Please check your time block details and try again."


# Focus session exceptions
class FocusSessionError(ChronosException):
    """Base exception for focus session errors."""
    
    def _get_default_friendly_message(self) -> str:
        return "There was a problem with your focus session. Please try again."


class FocusSessionNotFoundError(FocusSessionError):
    """Focus session was not found."""
    
    def _get_default_friendly_message(self) -> str:
        return "We couldn't find that focus session. It might have been completed or cancelled."


class FocusSessionActiveError(FocusSessionError):
    """Cannot perform action while focus session is active."""

    def _get_default_friendly_message(self) -> str:
        return "Please finish or pause your current focus session before starting a new one."


class InvalidFocusSessionStateError(FocusSessionError):
    """Focus session is in invalid state for requested operation."""

    def _get_default_friendly_message(self) -> str:
        return "This action isn't available for your current focus session. Please check the session status."


# Timer exceptions
class TimerError(ChronosException):
    """Base exception for timer errors."""

    def _get_default_friendly_message(self) -> str:
        return "There was a problem with your timer. Please try again."


class TimerNotFoundError(TimerError):
    """Timer was not found."""

    def _get_default_friendly_message(self) -> str:
        return "We couldn't find that timer. It might have expired or been stopped."


# Notification exceptions
class NotificationError(ChronosException):
    """Base exception for notification errors."""
    
    def _get_default_friendly_message(self) -> str:
        return "There was a problem with notifications. Please check your settings."


class NotificationDeliveryError(NotificationError):
    """Failed to deliver notification."""
    
    def _get_default_friendly_message(self) -> str:
        return "We couldn't send your notification. Please check your notification settings."


# AI service exceptions
class AIServiceError(ChronosException):
    """AI service operation failed."""
    
    def _get_default_friendly_message(self) -> str:
        return "Our AI assistant is temporarily unavailable. Please try again later."


class AIQuotaExceededError(AIServiceError):
    """AI service quota exceeded."""
    
    def _get_default_friendly_message(self) -> str:
        return "You've reached your AI assistance limit for today. It will reset tomorrow."


# Integration exceptions
class IntegrationError(ChronosException):
    """External service integration failed."""
    
    def _get_default_friendly_message(self) -> str:
        return "We're having trouble connecting to an external service. Please try again later."


class CalendarSyncError(IntegrationError):
    """Calendar synchronization failed."""
    
    def _get_default_friendly_message(self) -> str:
        return "We couldn't sync with your calendar. Please check your connection and try again."


# Rate limiting exceptions
class RateLimitExceededError(ChronosException):
    """Rate limit exceeded."""
    
    def _get_default_friendly_message(self) -> str:
        return "You're making requests too quickly. Please wait a moment and try again."


# Validation exceptions
class ValidationError(ChronosException):
    """Data validation failed."""
    
    def _get_default_friendly_message(self) -> str:
        return "Please check your input and try again."


class ConfigurationError(ChronosException):
    """Application configuration error."""
    
    def _get_default_friendly_message(self) -> str:
        return "There's a configuration problem. Please contact support."


# ADHD-specific exceptions
class ADHDFeatureError(ChronosException):
    """ADHD-specific feature error."""
    
    def _get_default_friendly_message(self) -> str:
        return "There was a problem with an ADHD-focused feature. Please try again."


class EnergyLevelMismatchError(ADHDFeatureError):
    """Task energy level doesn't match user's current state."""
    
    def _get_default_friendly_message(self) -> str:
        return "This task might be too demanding for your current energy level. Try filtering for easier tasks."


class HyperfocusWarningError(ADHDFeatureError):
    """User has been hyperfocusing for too long."""
    
    def _get_default_friendly_message(self) -> str:
        return "You've been focused for a while! Consider taking a break to recharge."
