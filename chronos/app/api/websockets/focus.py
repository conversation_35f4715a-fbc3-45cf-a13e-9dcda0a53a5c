"""
WebSocket endpoints for focus sessions and real-time timer updates.

This module provides WebSocket connections for real-time focus session
updates, timer synchronization, and gentle ADHD-friendly notifications.
"""

import asyncio
import json
from typing import Dict, Optional
from uuid import UUID

from fastapi import WebSocket, WebSocketDisconnect
from sqlalchemy.ext.asyncio import AsyncSession

from chronos.app.core.database import get_db
from chronos.app.schemas.focus import BreakR<PERSON>inderMessage, TimerState
from chronos.app.services.focus_service import FocusSessionService
from chronos.app.services.timer_service import TimerService
from chronos.app.utils.focus_utils import generate_gentle_reminder_message


class FocusWebSocketManager:
    """Manage WebSocket connections for real-time focus updates."""
    
    def __init__(self):
        """Initialize WebSocket manager."""
        self.active_connections: Dict[UUID, WebSocket] = {}
        self.user_sessions: Dict[UUID, UUID] = {}  # user_id -> session_id
        self.timer_service = TimerService()
        self._update_tasks: Dict[UUID, asyncio.Task] = {}
    
    async def connect(self, websocket: WebSocket, user_id: UUID):
        """
        Connect user to focus session WebSocket.
        
        Args:
            websocket: WebSocket connection
            user_id: User identifier
        """
        await websocket.accept()
        self.active_connections[user_id] = websocket
        
        # Start timer update task for this user
        if user_id not in self._update_tasks:
            task = asyncio.create_task(self._timer_update_loop(user_id))
            self._update_tasks[user_id] = task
        
        # Send initial connection confirmation
        await self.send_message(user_id, {
            "type": "connection_established",
            "message": "Connected to focus session updates",
            "timestamp": asyncio.get_event_loop().time()
        })
    
    async def disconnect(self, user_id: UUID):
        """
        Disconnect user from focus session WebSocket.
        
        Args:
            user_id: User identifier
        """
        if user_id in self.active_connections:
            del self.active_connections[user_id]
        
        if user_id in self.user_sessions:
            del self.user_sessions[user_id]
        
        # Cancel timer update task
        if user_id in self._update_tasks:
            task = self._update_tasks[user_id]
            task.cancel()
            del self._update_tasks[user_id]
    
    async def send_message(self, user_id: UUID, message: Dict):
        """
        Send message to specific user.
        
        Args:
            user_id: User identifier
            message: Message to send
        """
        if user_id in self.active_connections:
            websocket = self.active_connections[user_id]
            try:
                await websocket.send_text(json.dumps(message))
            except Exception:
                # Connection might be closed, remove it
                await self.disconnect(user_id)
    
    async def send_timer_update(self, user_id: UUID, timer_state: TimerState):
        """
        Send real-time timer updates to connected client.
        
        Args:
            user_id: User identifier
            timer_state: Current timer state
        """
        message = {
            "type": "timer_update",
            "data": timer_state.dict(),
            "timestamp": asyncio.get_event_loop().time()
        }
        await self.send_message(user_id, message)
    
    async def send_break_reminder(
        self,
        user_id: UUID,
        reminder_type: str,
        session_duration: int,
        user_preferences: Optional[Dict] = None
    ):
        """
        Send gentle break reminder to user.
        
        Args:
            user_id: User identifier
            reminder_type: Type of reminder
            session_duration: Current session duration
            user_preferences: User's reminder preferences
        """
        reminder_data = generate_gentle_reminder_message(
            reminder_type=reminder_type,
            session_duration=session_duration,
            user_preferences=user_preferences
        )
        
        reminder_message = BreakReminderMessage(
            type=reminder_type,
            message=reminder_data["message"],
            options=reminder_data["options"],
            session_id=self.user_sessions.get(user_id, UUID("00000000-0000-0000-0000-000000000000"))
        )
        
        message = {
            "type": "break_reminder",
            "data": reminder_message.dict(),
            "timestamp": asyncio.get_event_loop().time(),
            "auto_dismiss_seconds": reminder_data.get("auto_dismiss_seconds", 30)
        }
        
        await self.send_message(user_id, message)
    
    async def send_hyperfocus_alert(
        self,
        user_id: UUID,
        session_id: UUID,
        session_duration: int,
        confidence: float
    ):
        """
        Send hyperfocus detection alert.
        
        Args:
            user_id: User identifier
            session_id: Session identifier
            session_duration: Current session duration
            confidence: Hyperfocus detection confidence
        """
        message = {
            "type": "hyperfocus_alert",
            "data": {
                "session_id": str(session_id),
                "session_duration": session_duration,
                "confidence": confidence,
                "message": f"You've been focused for {session_duration} minutes. How are you feeling?",
                "options": ["continue", "take_break", "extend_15min"],
                "urgency": "medium" if confidence > 0.7 else "low"
            },
            "timestamp": asyncio.get_event_loop().time()
        }
        
        await self.send_message(user_id, message)
    
    async def register_session(self, user_id: UUID, session_id: UUID):
        """
        Register active session for user.
        
        Args:
            user_id: User identifier
            session_id: Session identifier
        """
        self.user_sessions[user_id] = session_id
    
    async def unregister_session(self, user_id: UUID):
        """
        Unregister active session for user.
        
        Args:
            user_id: User identifier
        """
        if user_id in self.user_sessions:
            del self.user_sessions[user_id]
    
    async def _timer_update_loop(self, user_id: UUID):
        """
        Background task to send periodic timer updates.
        
        Args:
            user_id: User identifier
        """
        try:
            while user_id in self.active_connections:
                # Check if user has active session
                if user_id in self.user_sessions:
                    session_id = self.user_sessions[user_id]
                    
                    try:
                        # Get current timer state
                        timer_state = await self.timer_service.get_timer_state(session_id)
                        
                        # Send timer update
                        await self.send_timer_update(user_id, timer_state)
                        
                        # Check for break reminders or hyperfocus
                        await self._check_session_alerts(user_id, session_id, timer_state)
                        
                    except Exception:
                        # Timer might not exist or session ended
                        await self.unregister_session(user_id)
                
                # Wait before next update (every 30 seconds for gentle updates)
                await asyncio.sleep(30)
                
        except asyncio.CancelledError:
            # Task was cancelled, clean up
            pass
        except Exception as e:
            # Log error and disconnect user
            print(f"Timer update loop error for user {user_id}: {str(e)}")
            await self.disconnect(user_id)
    
    async def _check_session_alerts(
        self,
        user_id: UUID,
        session_id: UUID,
        timer_state: TimerState
    ):
        """
        Check if session needs alerts (break reminders, hyperfocus).
        
        Args:
            user_id: User identifier
            session_id: Session identifier
            timer_state: Current timer state
        """
        try:
            # Get session from database to check for alerts
            async with get_db() as db:
                focus_service = FocusSessionService(db=db, timer_service=self.timer_service)
                
                # Check for hyperfocus
                hyperfocus_detected = await focus_service.check_hyperfocus(user_id, session_id)
                if hyperfocus_detected:
                    await self.send_hyperfocus_alert(
                        user_id=user_id,
                        session_id=session_id,
                        session_duration=timer_state.elapsed_time,
                        confidence=0.8  # Would be calculated based on actual detection
                    )
                
                # Check for break reminders
                session = await focus_service.get_session_by_id(user_id, session_id)
                if session.should_show_break_reminder():
                    await self.send_break_reminder(
                        user_id=user_id,
                        reminder_type="break",
                        session_duration=timer_state.elapsed_time
                    )
                    
        except Exception as e:
            # Log error but don't break the update loop
            print(f"Session alert check error: {str(e)}")


# Global WebSocket manager instance
focus_websocket_manager = FocusWebSocketManager()


async def websocket_endpoint(websocket: WebSocket, user_id: str):
    """
    WebSocket endpoint for focus session updates.
    
    Args:
        websocket: WebSocket connection
        user_id: User identifier as string
    """
    user_uuid = UUID(user_id)
    
    try:
        await focus_websocket_manager.connect(websocket, user_uuid)
        
        # Keep connection alive and handle incoming messages
        while True:
            try:
                # Wait for messages from client
                data = await websocket.receive_text()
                message = json.loads(data)
                
                # Handle different message types
                await _handle_websocket_message(user_uuid, message)
                
            except WebSocketDisconnect:
                break
            except json.JSONDecodeError:
                # Invalid JSON, send error
                await focus_websocket_manager.send_message(user_uuid, {
                    "type": "error",
                    "message": "Invalid JSON format"
                })
            except Exception as e:
                # Other errors
                await focus_websocket_manager.send_message(user_uuid, {
                    "type": "error", 
                    "message": f"Error processing message: {str(e)}"
                })
                
    except Exception as e:
        print(f"WebSocket connection error: {str(e)}")
    finally:
        await focus_websocket_manager.disconnect(user_uuid)


async def _handle_websocket_message(user_id: UUID, message: Dict):
    """
    Handle incoming WebSocket messages from client.
    
    Args:
        user_id: User identifier
        message: Message from client
    """
    message_type = message.get("type")
    
    if message_type == "register_session":
        session_id = UUID(message.get("session_id"))
        await focus_websocket_manager.register_session(user_id, session_id)
        
    elif message_type == "unregister_session":
        await focus_websocket_manager.unregister_session(user_id)
        
    elif message_type == "ping":
        # Respond to ping with pong
        await focus_websocket_manager.send_message(user_id, {
            "type": "pong",
            "timestamp": asyncio.get_event_loop().time()
        })
    
    elif message_type == "reminder_response":
        # Handle user response to break reminder
        response = message.get("response")
        session_id = message.get("session_id")
        
        # Log response for analytics (would be implemented)
        print(f"User {user_id} responded '{response}' to reminder for session {session_id}")
    
    else:
        # Unknown message type
        await focus_websocket_manager.send_message(user_id, {
            "type": "error",
            "message": f"Unknown message type: {message_type}"
        })
