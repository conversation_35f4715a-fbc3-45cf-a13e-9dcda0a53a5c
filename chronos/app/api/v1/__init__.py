"""
API v1 package for Project Chronos.

This package contains version 1 of the API endpoints with ADHD-optimized
task management and focus session functionality.
"""

from fastapi import APIRouter

from chronos.app.api.v1 import focus
from .tasks import router as tasks_router

api_router = APIRouter()

# Include focus session routes
api_router.include_router(focus.router, prefix="/focus", tags=["focus"])

# Include task management routes
api_router.include_router(tasks_router, prefix="/tasks", tags=["tasks"])

__all__ = ["api_router", "tasks_router"]
