"""
Task management API endpoints for ADHD-optimized functionality.

This module provides REST API endpoints for task CRUD operations,
AI-powered chunking, adaptive filtering, and the task jar feature.
"""

from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from chronos.app.core.database import get_db
from chronos.app.models.user import User
from chronos.app.schemas.task import (
    TaskCreate,
    TaskUpdate,
    TaskResponse,
    TaskChunkRequest,
    TaskJarRequest,
    TaskFilterRequest,
)
from chronos.app.services.task_service import TaskService
from chronos.app.services.ai_service import AIChunkingService
from chronos.app.services.filter_service import AdaptiveFilterService
from chronos.app.core.exceptions import NotFoundError, ValidationError, AIServiceError

# TODO: Import authentication dependency when Agent 2 is implemented
# from chronos.app.api.deps import get_current_user

router = APIRouter(prefix="/tasks", tags=["tasks"])


# Temporary mock for current user until Agent 2 is implemented
async def get_current_user() -> User:
    """Temporary mock user for development."""
    from uuid import uuid4
    user = User(
        id=uuid4(),
        email="<EMAIL>",
        username="testuser",
        full_name="Test User"
    )
    return user


@router.post("/", response_model=TaskResponse, status_code=status.HTTP_201_CREATED)
async def create_task(
    task_data: TaskCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Create a new task with ADHD-optimized defaults.
    
    This endpoint creates a new task with intelligent defaults for users with ADHD,
    including appropriate energy level settings and context tag suggestions.
    """
    try:
        task_service = TaskService(db)
        task = await task_service.create_task(current_user.id, task_data)
        return task
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/", response_model=List[TaskResponse])
async def get_tasks(
    energy_level: Optional[str] = Query(None, regex="^(low|medium|high)$"),
    max_duration: Optional[int] = Query(None, ge=1, le=1440),
    context_tags: Optional[str] = Query(None),
    priority: Optional[str] = Query(None, regex="^(low|medium|high|urgent)$"),
    status: Optional[str] = Query(None, regex="^(pending|in_progress|completed|cancelled)$"),
    due_soon: Optional[bool] = Query(None),
    limit: int = Query(50, ge=1, le=100),
    offset: int = Query(0, ge=0),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get user's tasks with adaptive filtering.
    
    This endpoint provides intelligent task filtering based on the user's current
    context, energy level, and ADHD-specific needs. It supports filtering by:
    - Energy level (low/medium/high)
    - Maximum duration
    - Context tags
    - Priority level
    - Task status
    - Due date proximity
    """
    # Parse context tags from comma-separated string
    context_tag_list = None
    if context_tags:
        context_tag_list = [tag.strip() for tag in context_tags.split(",")]
    
    filter_request = TaskFilterRequest(
        energy_level=energy_level,
        max_duration=max_duration,
        context_tags=context_tag_list,
        priority=priority,
        status=status,
        due_soon=due_soon,
        limit=limit,
        offset=offset
    )
    
    filter_service = AdaptiveFilterService(db)
    tasks = await filter_service.get_filtered_tasks(current_user.id, filter_request)
    return tasks


@router.get("/{task_id}", response_model=TaskResponse)
async def get_task(
    task_id: UUID,
    include_subtasks: bool = Query(False),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get a specific task by ID.
    
    Optionally includes subtasks if the task has been chunked using AI.
    """
    task_service = TaskService(db)
    task = await task_service.get_task_by_id(
        current_user.id, task_id, include_subtasks
    )
    
    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Task not found"
        )
    
    return task


@router.put("/{task_id}", response_model=TaskResponse)
async def update_task(
    task_id: UUID,
    task_data: TaskUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Update an existing task.
    
    Supports partial updates and automatically handles status transitions
    like marking completion timestamps.
    """
    try:
        task_service = TaskService(db)
        task = await task_service.update_task(current_user.id, task_id, task_data)
        
        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Task not found"
            )
        
        return task
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.delete("/{task_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_task(
    task_id: UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Delete a task and all its subtasks.
    
    This operation cascades to delete all subtasks created through AI chunking.
    """
    task_service = TaskService(db)
    deleted = await task_service.delete_task(current_user.id, task_id)
    
    if not deleted:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Task not found"
        )


@router.post("/{task_id}/chunk", response_model=List[TaskResponse])
async def chunk_task(
    task_id: UUID,
    chunk_request: TaskChunkRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Break down a task using AI chunking.
    
    This endpoint uses AI (OpenAI/Anthropic) to intelligently break down
    overwhelming tasks into smaller, actionable subtasks optimized for ADHD users.
    
    The AI considers:
    - Task complexity and scope
    - User's preferred chunk size
    - ADHD-specific factors (decision fatigue, overwhelm)
    - Context and energy level requirements
    """
    try:
        ai_service = AIChunkingService(db)
        subtasks = await ai_service.chunk_task(
            current_user.id, task_id, chunk_request
        )
        return subtasks
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except AIServiceError as e:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=str(e)
        )


@router.get("/jar/shake", response_model=List[TaskResponse])
async def get_task_jar(
    jar_size: int = Query(5, ge=1, le=20),
    energy_level: Optional[str] = Query(None, regex="^(low|medium|high)$"),
    max_duration: Optional[int] = Query(None, ge=1, le=1440),
    context_tags: Optional[str] = Query(None),
    exclude_recent: bool = Query(True),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get random task selection for decision fatigue reduction.
    
    The "task jar" feature helps users with ADHD overcome choice paralysis by
    providing a curated random selection of suitable tasks. This reduces
    decision fatigue and helps maintain momentum.
    
    Features:
    - Weighted random selection (prioritizes urgent/important tasks)
    - Respects energy level and context constraints
    - Excludes recently completed tasks to maintain variety
    - Configurable jar size for different needs
    """
    # Parse context tags from comma-separated string
    context_tag_list = None
    if context_tags:
        context_tag_list = [tag.strip() for tag in context_tags.split(",")]
    
    jar_request = TaskJarRequest(
        jar_size=jar_size,
        energy_level=energy_level,
        max_duration=max_duration,
        context_tags=context_tag_list,
        exclude_recent=exclude_recent
    )
    
    filter_service = AdaptiveFilterService(db)
    tasks = await filter_service.get_task_jar(current_user.id, jar_request)
    return tasks


@router.get("/energy/{energy_level}", response_model=List[TaskResponse])
async def get_energy_matched_tasks(
    energy_level: str,
    max_duration: Optional[int] = Query(None, ge=1, le=1440),
    limit: int = Query(10, ge=1, le=50),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get tasks that match user's current energy level.
    
    This endpoint provides tasks that are appropriate for the user's current
    energy state, helping them maintain productivity even during low-energy periods.
    """
    if energy_level not in ["low", "medium", "high"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Energy level must be 'low', 'medium', or 'high'"
        )
    
    filter_service = AdaptiveFilterService(db)
    tasks = await filter_service.get_energy_matched_tasks(
        current_user.id, energy_level, max_duration, limit
    )
    return tasks


@router.get("/quick-wins", response_model=List[TaskResponse])
async def get_quick_wins(
    max_duration: int = Query(15, ge=1, le=60),
    limit: int = Query(5, ge=1, le=20),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get quick, low-energy tasks for momentum building.
    
    Quick wins are short, low-energy tasks that help users with ADHD build
    momentum and maintain a sense of progress, especially during difficult periods.
    """
    filter_service = AdaptiveFilterService(db)
    tasks = await filter_service.get_quick_wins(
        current_user.id, max_duration, limit
    )
    return tasks


@router.get("/stats/completion", response_model=dict)
async def get_completion_stats(
    days: int = Query(30, ge=1, le=365),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get task completion statistics for the user.
    
    Provides insights into task completion patterns, which can help users
    with ADHD understand their productivity patterns and optimize their workflow.
    """
    task_service = TaskService(db)
    stats = await task_service.get_task_completion_stats(current_user.id, days)
    return stats
