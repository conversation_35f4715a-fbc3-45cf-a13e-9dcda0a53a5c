"""
Pydantic schemas for focus sessions and timer functionality.

This module defines request/response schemas for focus sessions, timers,
and ADHD-specific focus modes with validation and serialization.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional
from uuid import UUID

from pydantic import BaseModel, Field, validator


class FocusSessionBase(BaseModel):
    """Base schema for focus sessions."""
    
    session_type: str = Field(
        default="pomodoro",
        description="Type of focus session: pomodoro, deep_work, sprint, custom"
    )
    planned_duration: int = Field(
        ge=1,
        le=480,  # Max 8 hours
        description="Planned session duration in minutes"
    )
    break_duration: int = Field(
        default=5,
        ge=1,
        le=60,
        description="Break duration in minutes"
    )
    focus_mode_settings: Dict[str, Any] = Field(
        default_factory=dict,
        description="Focus mode configuration and ADHD-specific settings"
    )
    
    @validator('session_type')
    def validate_session_type(cls, v):
        """Validate session type."""
        allowed_types = ['pomodoro', 'deep_work', 'sprint', 'custom']
        if v not in allowed_types:
            raise ValueError(f'Session type must be one of: {allowed_types}')
        return v


class FocusSessionCreate(FocusSessionBase):
    """Schema for creating a new focus session."""
    
    task_id: Optional[UUID] = Field(
        None,
        description="Associated task ID (optional)"
    )


class FocusSessionUpdate(BaseModel):
    """Schema for updating a focus session."""
    
    actual_duration: Optional[int] = Field(
        None,
        ge=0,
        description="Actual session duration in minutes"
    )
    status: Optional[str] = Field(
        None,
        description="Session status: active, paused, completed, cancelled"
    )
    focus_mode_settings: Optional[Dict[str, Any]] = Field(
        None,
        description="Updated focus mode settings"
    )
    
    @validator('status')
    def validate_status(cls, v):
        """Validate session status."""
        if v is not None:
            allowed_statuses = ['planned', 'active', 'paused', 'break', 'completed', 'cancelled']
            if v not in allowed_statuses:
                raise ValueError(f'Status must be one of: {allowed_statuses}')
        return v


class FocusSessionResponse(FocusSessionBase):
    """Schema for focus session responses."""
    
    id: UUID
    user_id: UUID
    task_id: Optional[UUID]
    status: str
    actual_duration: Optional[int]
    started_at: Optional[datetime]
    completed_at: Optional[datetime]
    paused_at: Optional[datetime]
    hyperfocus_detected: bool
    hyperfocus_duration: Optional[int]
    created_at: datetime
    updated_at: datetime
    
    # Computed fields
    elapsed_time: int = Field(description="Elapsed time in minutes")
    remaining_time: int = Field(description="Remaining time in minutes")
    progress_percentage: float = Field(description="Progress as percentage (0.0-1.0)")
    is_active: bool = Field(description="Whether session is currently active")
    should_show_break_reminder: bool = Field(description="Whether to show break reminder")
    
    class Config:
        from_attributes = True


class FocusSessionInDB(FocusSessionResponse):
    """Schema for focus session in database (internal use)."""
    pass


class TimerState(BaseModel):
    """Schema for real-time timer state."""
    
    session_id: UUID
    status: str
    elapsed_time: int = Field(description="Elapsed time in minutes")
    remaining_time: int = Field(description="Remaining time in minutes")
    progress_percentage: float = Field(description="Progress as percentage (0.0-1.0)")
    is_paused: bool = Field(description="Whether timer is paused")
    last_updated: datetime = Field(description="Last update timestamp")
    
    class Config:
        from_attributes = True


class PauseRequest(BaseModel):
    """Schema for pausing a focus session."""
    
    reason: Optional[str] = Field(
        None,
        max_length=200,
        description="Optional reason for pausing"
    )


class SessionCompletionRequest(BaseModel):
    """Schema for completing a focus session."""
    
    completion_notes: Optional[str] = Field(
        None,
        max_length=500,
        description="Optional completion notes"
    )
    actual_duration: Optional[int] = Field(
        None,
        ge=0,
        description="Actual duration if different from elapsed time"
    )


class FocusAnalytics(BaseModel):
    """Schema for focus session analytics."""
    
    total_sessions: int = Field(description="Total number of sessions")
    completed_sessions: int = Field(description="Number of completed sessions")
    total_focus_time: int = Field(description="Total focus time in minutes")
    average_session_duration: float = Field(description="Average session duration in minutes")
    completion_rate: float = Field(description="Session completion rate (0.0-1.0)")
    hyperfocus_incidents: int = Field(description="Number of hyperfocus incidents")
    most_productive_hours: List[int] = Field(description="Most productive hours of day")
    session_type_breakdown: Dict[str, int] = Field(description="Sessions by type")
    weekly_trend: List[Dict[str, Any]] = Field(description="Weekly focus trends")
    
    class Config:
        from_attributes = True


class FocusModeBase(BaseModel):
    """Base schema for focus modes."""
    
    name: str = Field(max_length=100, description="Focus mode name")
    description: Optional[str] = Field(
        None,
        max_length=500,
        description="Focus mode description"
    )
    settings: Dict[str, Any] = Field(
        default_factory=dict,
        description="Focus mode settings and preferences"
    )
    is_default: bool = Field(
        default=False,
        description="Whether this is a default system mode"
    )


class FocusModeCreate(FocusModeBase):
    """Schema for creating a focus mode."""
    pass


class FocusModeResponse(FocusModeBase):
    """Schema for focus mode responses."""
    
    id: UUID
    user_id: UUID
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class BreakReminderMessage(BaseModel):
    """Schema for break reminder messages."""
    
    type: str = Field(description="Reminder type: gentle, hydration, movement, eye_strain")
    message: str = Field(description="Reminder message")
    options: List[str] = Field(description="Available user options")
    session_id: UUID = Field(description="Associated session ID")
    
    class Config:
        from_attributes = True
