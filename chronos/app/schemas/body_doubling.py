"""
Pydantic schemas for body doubling functionality.

This module defines request/response schemas for body doubling sessions,
participants, and real-time messaging.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional
from uuid import UUID

from pydantic import BaseModel, Field, validator


class BodyDoublingSessionBase(BaseModel):
    """Base schema for body doubling sessions."""
    
    title: str = Field(..., min_length=1, max_length=200, description="Session title")
    description: Optional[str] = Field(None, description="Optional session description")
    max_participants: int = Field(4, ge=2, le=10, description="Maximum participants")
    session_type: str = Field("open", description="Session type")
    is_public: bool = Field(True, description="Whether session is publicly discoverable")
    requires_approval: bool = Field(False, description="Whether host approval is required")
    password_protected: bool = Field(False, description="Whether session requires password")
    scheduled_start: Optional[datetime] = Field(None, description="Scheduled start time")
    scheduled_end: Optional[datetime] = Field(None, description="Scheduled end time")
    session_settings: Dict[str, Any] = Field(default_factory=dict, description="Session configuration")
    
    @validator("session_type")
    def validate_session_type(cls, v):
        """Validate session type."""
        allowed_types = ["open", "private", "focus_group", "study_group"]
        if v not in allowed_types:
            raise ValueError(f"Session type must be one of: {allowed_types}")
        return v
    
    @validator("scheduled_end")
    def validate_end_after_start(cls, v, values):
        """Ensure end time is after start time."""
        if v and "scheduled_start" in values and values["scheduled_start"]:
            if v <= values["scheduled_start"]:
                raise ValueError("Scheduled end must be after scheduled start")
        return v


class BodyDoublingSessionCreate(BodyDoublingSessionBase):
    """Schema for creating a new body doubling session."""
    
    session_password: Optional[str] = Field(None, description="Session password if protected")
    
    @validator("session_password")
    def validate_password_requirement(cls, v, values):
        """Ensure password is provided if session is password protected."""
        if values.get("password_protected") and not v:
            raise ValueError("Password is required for password-protected sessions")
        return v


class BodyDoublingSessionUpdate(BaseModel):
    """Schema for updating a body doubling session."""
    
    title: Optional[str] = Field(None, min_length=1, max_length=200)
    description: Optional[str] = None
    max_participants: Optional[int] = Field(None, ge=2, le=10)
    is_public: Optional[bool] = None
    requires_approval: Optional[bool] = None
    scheduled_start: Optional[datetime] = None
    scheduled_end: Optional[datetime] = None
    session_settings: Optional[Dict[str, Any]] = None


class BodyDoublingSessionResponse(BodyDoublingSessionBase):
    """Schema for body doubling session responses."""
    
    id: UUID
    host_user_id: UUID
    status: str
    actual_start: Optional[datetime] = None
    actual_end: Optional[datetime] = None
    current_focus_session_id: Optional[UUID] = None
    participant_count: int = Field(0, description="Current number of participants")
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class BodyDoublingParticipantBase(BaseModel):
    """Base schema for body doubling participants."""
    
    share_progress: bool = Field(True, description="Share task progress with others")
    anonymous_mode: bool = Field(False, description="Participate anonymously")
    participant_settings: Dict[str, Any] = Field(default_factory=dict, description="Participant preferences")


class BodyDoublingParticipantCreate(BodyDoublingParticipantBase):
    """Schema for joining a body doubling session."""
    
    session_password: Optional[str] = Field(None, description="Session password if required")


class BodyDoublingParticipantUpdate(BaseModel):
    """Schema for updating participant settings."""
    
    status: Optional[str] = None
    share_progress: Optional[bool] = None
    anonymous_mode: Optional[bool] = None
    current_task_id: Optional[UUID] = None
    participant_settings: Optional[Dict[str, Any]] = None
    
    @validator("status")
    def validate_status(cls, v):
        """Validate participant status."""
        if v is not None:
            allowed_statuses = ["active", "away", "focused", "break", "left"]
            if v not in allowed_statuses:
                raise ValueError(f"Status must be one of: {allowed_statuses}")
        return v


class BodyDoublingParticipantResponse(BodyDoublingParticipantBase):
    """Schema for body doubling participant responses."""
    
    id: UUID
    session_id: UUID
    user_id: UUID
    joined_at: datetime
    left_at: Optional[datetime] = None
    status: str
    last_activity: datetime
    current_task_id: Optional[UUID] = None
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class SessionMessageBase(BaseModel):
    """Base schema for session messages."""
    
    message_type: str = Field("chat", description="Message type")
    content: str = Field(..., min_length=1, max_length=1000, description="Message content")
    message_data: Dict[str, Any] = Field(default_factory=dict, description="Additional message data")
    
    @validator("message_type")
    def validate_message_type(cls, v):
        """Validate message type."""
        allowed_types = ["chat", "encouragement", "system", "progress_update"]
        if v not in allowed_types:
            raise ValueError(f"Message type must be one of: {allowed_types}")
        return v


class SessionMessageCreate(SessionMessageBase):
    """Schema for creating session messages."""
    pass


class SessionMessageResponse(SessionMessageBase):
    """Schema for session message responses."""
    
    id: UUID
    session_id: UUID
    sender_id: UUID
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class GroupFocusRequest(BaseModel):
    """Schema for starting a group focus session."""
    
    focus_duration: int = Field(..., ge=5, le=180, description="Focus duration in minutes")
    break_duration: int = Field(5, ge=1, le=30, description="Break duration in minutes")
    session_type: str = Field("pomodoro", description="Focus session type")
    focus_settings: Dict[str, Any] = Field(default_factory=dict, description="Focus session settings")
    
    @validator("session_type")
    def validate_focus_session_type(cls, v):
        """Validate focus session type."""
        allowed_types = ["pomodoro", "deep_work", "sprint", "custom"]
        if v not in allowed_types:
            raise ValueError(f"Focus session type must be one of: {allowed_types}")
        return v


class GroupFocusResponse(BaseModel):
    """Schema for group focus session responses."""
    
    focus_session_id: UUID
    session_id: UUID
    focus_duration: int
    break_duration: int
    started_at: datetime
    participants: List[UUID]
    
    class Config:
        from_attributes = True


class TaskProgressUpdate(BaseModel):
    """Schema for task progress updates in body doubling sessions."""
    
    task_id: UUID
    progress_type: str = Field(..., description="Type of progress update")
    progress_data: Dict[str, Any] = Field(default_factory=dict, description="Progress details")
    message: Optional[str] = Field(None, max_length=500, description="Optional progress message")
    
    @validator("progress_type")
    def validate_progress_type(cls, v):
        """Validate progress type."""
        allowed_types = ["started", "completed", "paused", "milestone", "stuck", "breakthrough"]
        if v not in allowed_types:
            raise ValueError(f"Progress type must be one of: {allowed_types}")
        return v


class EncouragementMessage(BaseModel):
    """Schema for encouragement messages."""
    
    encouragement_type: str = Field(..., description="Type of encouragement")
    message: Optional[str] = Field(None, max_length=200, description="Optional custom message")
    
    @validator("encouragement_type")
    def validate_encouragement_type(cls, v):
        """Validate encouragement type."""
        allowed_types = ["thumbs_up", "clap", "fire", "heart", "star", "custom"]
        if v not in allowed_types:
            raise ValueError(f"Encouragement type must be one of: {allowed_types}")
        return v


class WebSocketMessage(BaseModel):
    """Schema for WebSocket messages."""
    
    type: str = Field(..., description="Message type")
    data: Dict[str, Any] = Field(default_factory=dict, description="Message data")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Message timestamp")
    
    @validator("type")
    def validate_websocket_message_type(cls, v):
        """Validate WebSocket message type."""
        allowed_types = [
            "participant_joined", "participant_left", "participant_status_changed",
            "task_progress", "encouragement", "chat_message", "focus_session_started",
            "focus_session_ended", "session_status_changed", "heartbeat"
        ]
        if v not in allowed_types:
            raise ValueError(f"WebSocket message type must be one of: {allowed_types}")
        return v


class SessionListResponse(BaseModel):
    """Schema for listing body doubling sessions."""
    
    sessions: List[BodyDoublingSessionResponse]
    total: int
    page: int
    page_size: int
    has_next: bool
    has_prev: bool


class ParticipantListResponse(BaseModel):
    """Schema for listing session participants."""
    
    participants: List[BodyDoublingParticipantResponse]
    total: int


class SessionStatsResponse(BaseModel):
    """Schema for session statistics."""
    
    session_id: UUID
    total_duration: Optional[int] = Field(None, description="Total session duration in minutes")
    participant_count: int
    messages_sent: int
    focus_sessions_completed: int
    tasks_completed: int
    average_focus_duration: Optional[float] = Field(None, description="Average focus duration in minutes")
    
    class Config:
        from_attributes = True
