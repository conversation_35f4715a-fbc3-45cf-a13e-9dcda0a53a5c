"""
Task-related Pydantic schemas for API validation and serialization.

This module provides schemas for task CRUD operations, AI chunking,
and adaptive filtering with ADHD-specific field validation.
"""

from datetime import datetime
from typing import List, Optional, Dict, Any
from uuid import UUID

from pydantic import BaseModel, Field, validator


class TaskBase(BaseModel):
    """Base task schema with common fields."""
    
    title: str = Field(
        ...,
        min_length=1,
        max_length=500,
        description="Task title (1-500 characters)"
    )
    description: Optional[str] = Field(
        None,
        description="Detailed task description"
    )
    priority: str = Field(
        "medium",
        regex="^(low|medium|high|urgent)$",
        description="Task priority level"
    )
    energy_level: str = Field(
        "medium",
        regex="^(low|medium|high)$",
        description="Required energy level for task completion"
    )
    estimated_duration: Optional[int] = Field(
        None,
        ge=1,
        le=1440,  # Max 24 hours
        description="Estimated duration in minutes"
    )
    context_tags: List[str] = Field(
        default_factory=list,
        description="Context tags for adaptive filtering"
    )
    due_date: Optional[datetime] = Field(
        None,
        description="Task due date"
    )

    @validator('context_tags')
    def validate_context_tags(cls, v):
        """Validate context tags format and content."""
        if len(v) > 10:
            raise ValueError("Maximum 10 context tags allowed")
        
        for tag in v:
            if not tag.strip():
                raise ValueError("Context tags cannot be empty")
            if len(tag) > 50:
                raise ValueError("Context tags must be 50 characters or less")
        
        return [tag.strip().lower() for tag in v]


class TaskCreate(TaskBase):
    """Schema for creating new tasks."""
    
    parent_task_id: Optional[UUID] = Field(
        None,
        description="Parent task ID if this is a subtask"
    )


class TaskUpdate(BaseModel):
    """Schema for updating existing tasks."""
    
    title: Optional[str] = Field(
        None,
        min_length=1,
        max_length=500,
        description="Updated task title"
    )
    description: Optional[str] = Field(
        None,
        description="Updated task description"
    )
    status: Optional[str] = Field(
        None,
        regex="^(pending|in_progress|completed|cancelled)$",
        description="Updated task status"
    )
    priority: Optional[str] = Field(
        None,
        regex="^(low|medium|high|urgent)$",
        description="Updated task priority"
    )
    energy_level: Optional[str] = Field(
        None,
        regex="^(low|medium|high)$",
        description="Updated energy level requirement"
    )
    estimated_duration: Optional[int] = Field(
        None,
        ge=1,
        le=1440,
        description="Updated estimated duration in minutes"
    )
    actual_duration: Optional[int] = Field(
        None,
        ge=0,
        le=1440,
        description="Actual time spent on task in minutes"
    )
    context_tags: Optional[List[str]] = Field(
        None,
        description="Updated context tags"
    )
    due_date: Optional[datetime] = Field(
        None,
        description="Updated due date"
    )

    @validator('context_tags')
    def validate_context_tags(cls, v):
        """Validate context tags if provided."""
        if v is None:
            return v
        
        if len(v) > 10:
            raise ValueError("Maximum 10 context tags allowed")
        
        for tag in v:
            if not tag.strip():
                raise ValueError("Context tags cannot be empty")
            if len(tag) > 50:
                raise ValueError("Context tags must be 50 characters or less")
        
        return [tag.strip().lower() for tag in v]


class TaskResponse(TaskBase):
    """Schema for task API responses."""
    
    id: UUID
    user_id: UUID
    status: str
    actual_duration: Optional[int]
    is_chunked: bool
    parent_task_id: Optional[UUID]
    completed_at: Optional[datetime]
    created_at: datetime
    updated_at: datetime
    
    # Computed fields
    subtask_count: Optional[int] = Field(
        None,
        description="Number of subtasks (if chunked)"
    )
    completion_percentage: Optional[float] = Field(
        None,
        description="Completion percentage for chunked tasks"
    )
    
    class Config:
        from_attributes = True


class TaskChunkRequest(BaseModel):
    """Schema for AI task chunking requests."""
    
    chunk_size: str = Field(
        "small",
        regex="^(small|medium|large)$",
        description="Preferred chunk size for AI breakdown"
    )
    context: Optional[str] = Field(
        None,
        max_length=1000,
        description="Additional context for better chunking"
    )
    max_subtasks: Optional[int] = Field(
        5,
        ge=2,
        le=10,
        description="Maximum number of subtasks to generate"
    )
    include_time_estimates: bool = Field(
        True,
        description="Whether to include time estimates in subtasks"
    )


class TaskJarRequest(BaseModel):
    """Schema for task jar random selection requests."""
    
    jar_size: int = Field(
        5,
        ge=1,
        le=20,
        description="Number of tasks to randomly select"
    )
    energy_level: Optional[str] = Field(
        None,
        regex="^(low|medium|high)$",
        description="Filter by energy level"
    )
    max_duration: Optional[int] = Field(
        None,
        ge=1,
        le=1440,
        description="Maximum task duration in minutes"
    )
    context_tags: Optional[List[str]] = Field(
        None,
        description="Required context tags"
    )
    exclude_recent: bool = Field(
        True,
        description="Exclude recently completed or skipped tasks"
    )


class TaskFilterRequest(BaseModel):
    """Schema for adaptive task filtering requests."""
    
    energy_level: Optional[str] = Field(
        None,
        regex="^(low|medium|high)$",
        description="Filter by energy level"
    )
    max_duration: Optional[int] = Field(
        None,
        ge=1,
        le=1440,
        description="Maximum task duration in minutes"
    )
    context_tags: Optional[List[str]] = Field(
        None,
        description="Required context tags"
    )
    priority: Optional[str] = Field(
        None,
        regex="^(low|medium|high|urgent)$",
        description="Filter by priority level"
    )
    status: Optional[str] = Field(
        None,
        regex="^(pending|in_progress|completed|cancelled)$",
        description="Filter by task status"
    )
    due_soon: Optional[bool] = Field(
        None,
        description="Filter tasks due within 24 hours"
    )
    limit: int = Field(
        50,
        ge=1,
        le=100,
        description="Maximum number of tasks to return"
    )
    offset: int = Field(
        0,
        ge=0,
        description="Number of tasks to skip for pagination"
    )
