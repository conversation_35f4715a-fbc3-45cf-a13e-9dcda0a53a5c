"""
Main FastAPI application for Project Chronos.

This module creates and configures the FastAPI application with
ADHD-focused features and comprehensive middleware.
"""

from fastapi import Fast<PERSON><PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import J<PERSON>NResponse
import time
import logging

from chronos.app.core.config import settings
from chronos.app.core.exceptions import ChronosException

# Configure logging
logging.basicConfig(
    level=getattr(logging, settings.LOG_LEVEL),
    format=settings.LOG_FORMAT
)

logger = logging.getLogger(__name__)


def create_application() -> FastAPI:
    """
    Create and configure FastAPI application.
    
    Returns:
        FastAPI: Configured application instance
    """
    
    app = FastAPI(
        title=settings.PROJECT_NAME,
        description="ADHD-focused productivity and time management API",
        version=settings.VERSION,
        docs_url="/docs",
        redoc_url="/redoc",
        openapi_url="/openapi.json",
        # ADHD-friendly API documentation
        openapi_tags=[
            {
                "name": "Authentication",
                "description": "User authentication and authorization"
            },
            {
                "name": "Users",
                "description": "User management with ADHD-specific preferences"
            },
            {
                "name": "Tasks",
                "description": "Task management with AI chunking and energy levels"
            },
            {
                "name": "Time Blocking",
                "description": "Visual time management and scheduling"
            },
            {
                "name": "Focus Sessions",
                "description": "Pomodoro timers and hyperfocus tracking"
            },
            {
                "name": "Notifications",
                "description": "Persistent reminders and gentle notifications"
            },
            {
                "name": "Gamification",
                "description": "Points, achievements, and motivation features"
            },
            {
                "name": "System",
                "description": "Health checks and system information"
            }
        ]
    )
    
    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.BACKEND_CORS_ORIGINS,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Add trusted host middleware for security
    if not settings.TESTING:
        app.add_middleware(
            TrustedHostMiddleware,
            allowed_hosts=["localhost", "127.0.0.1", settings.SERVER_NAME]
        )
    
    # Global exception handlers
    @app.exception_handler(ChronosException)
    async def chronos_exception_handler(request: Request, exc: ChronosException):
        """Handle custom Chronos exceptions with ADHD-friendly messages."""
        logger.error(f"Chronos exception: {exc.message}", exc_info=exc)
        
        return JSONResponse(
            status_code=400,
            content={
                "error": exc.to_dict(),
                "timestamp": time.time(),
                "path": str(request.url.path)
            }
        )
    
    @app.exception_handler(Exception)
    async def general_exception_handler(request: Request, exc: Exception):
        """Handle unexpected exceptions gracefully."""
        logger.error(f"Unexpected error: {exc}", exc_info=True)
        
        return JSONResponse(
            status_code=500,
            content={
                "error": {
                    "code": "INTERNAL_ERROR",
                    "message": "An unexpected error occurred",
                    "friendly_message": "Something went wrong on our end. We're working to fix it!",
                },
                "timestamp": time.time(),
                "path": str(request.url.path)
            }
        )
    
    # Health check endpoint
    @app.get("/health", tags=["System"])
    async def health_check():
        """
        Health check endpoint for monitoring.
        
        Returns basic application health status and ADHD-specific metrics.
        """
        from chronos.app.core.database import db_manager
        
        # Check database health
        db_health = await db_manager.health_check()
        
        return {
            "status": "healthy" if db_health["status"] == "healthy" else "unhealthy",
            "timestamp": time.time(),
            "version": settings.VERSION,
            "environment": "development" if settings.TESTING else "production",
            "database": db_health,
            "adhd_features": {
                "ai_chunking_enabled": settings.AI_CHUNKING_ENABLED,
                "default_chunk_size": settings.DEFAULT_CHUNK_SIZE,
                "hyperfocus_threshold": settings.HYPERFOCUS_WARNING_THRESHOLD,
            }
        }
    
    # Root endpoint with ADHD-friendly welcome
    @app.get("/", tags=["System"])
    async def root():
        """
        Root endpoint with welcome message.
        
        Provides a friendly introduction to the ADHD-focused API.
        """
        return {
            "message": "Welcome to Project Chronos! 🧠✨",
            "description": "ADHD-focused productivity and time management API",
            "version": settings.VERSION,
            "docs": "/docs",
            "features": [
                "AI-powered task chunking",
                "Visual time management",
                "Gentle focus sessions",
                "Persistent reminders",
                "Gamified motivation",
                "Body doubling support"
            ],
            "adhd_friendly": True
        }
    
    # Include API routers (will be added as other agents are implemented)
    # app.include_router(auth_router, prefix="/api/v1/auth", tags=["Authentication"])
    # app.include_router(users_router, prefix="/api/v1/users", tags=["Users"])
    # app.include_router(tasks_router, prefix="/api/v1/tasks", tags=["Tasks"])
    
    return app


# Create application instance
app = create_application()


# Startup and shutdown events
@app.on_event("startup")
async def startup_event():
    """Application startup tasks."""
    logger.info("🧠 Starting Project Chronos...")
    logger.info(f"📊 Environment: {'Testing' if settings.TESTING else 'Development'}")
    logger.info(f"🗄️  Database: {str(settings.DATABASE_URL).split('@')[-1]}")
    logger.info(f"🤖 AI Chunking: {'Enabled' if settings.AI_CHUNKING_ENABLED else 'Disabled'}")
    
    # Check database connection
    from chronos.app.core.database import check_db_connection
    if await check_db_connection():
        logger.info("✅ Database connection established")
    else:
        logger.error("❌ Database connection failed")


@app.on_event("shutdown")
async def shutdown_event():
    """Application shutdown tasks."""
    logger.info("🛑 Shutting down Project Chronos...")
    
    # Close database connections
    from chronos.app.core.database import engine
    await engine.dispose()
    logger.info("✅ Database connections closed")


if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "chronos.app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level=settings.LOG_LEVEL.lower()
    )
