"""
Adaptive task filtering service for ADHD users.

This service provides context-aware task filtering, energy level matching,
and the "task jar" feature for decision fatigue reduction.
"""

import random
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from uuid import UUID

from sqlalchemy import select, and_, or_, func
from sqlalchemy.ext.asyncio import AsyncSession

from chronos.app.models.task import Task
from chronos.app.models.user import User
from chronos.app.schemas.task import TaskJarRequest, TaskFilterRequest
from chronos.app.utils.adaptive_utils import calculate_task_score, get_energy_level_score


class AdaptiveFilterService:
    """
    Context-aware task filtering service for ADHD users.
    
    This service provides intelligent task filtering based on user context,
    energy levels, and ADHD-specific needs like decision fatigue reduction.
    """
    
    def __init__(self, db: AsyncSession):
        """
        Initialize adaptive filter service.
        
        Args:
            db: Database session for operations
        """
        self.db = db
    
    async def get_filtered_tasks(
        self,
        user_id: UUID,
        filter_request: TaskFilterRequest,
        current_time: Optional[datetime] = None
    ) -> List[Task]:
        """
        Get tasks filtered by user's current state and preferences.
        
        Args:
            user_id: User identifier
            filter_request: Filtering criteria
            current_time: Current time for time-based filtering
            
        Returns:
            List[Task]: Filtered and prioritized list of tasks
        """
        if current_time is None:
            current_time = datetime.utcnow()
        
        # Build base query
        query = select(Task).where(
            and_(
                Task.user_id == user_id,
                Task.status.in_(["pending", "in_progress"])
            )
        )
        
        # Apply filters
        query = self._apply_adaptive_filters(query, filter_request, current_time)
        
        # Execute query
        result = await self.db.execute(query)
        tasks = result.scalars().all()
        
        # Apply intelligent scoring and sorting
        scored_tasks = await self._score_and_sort_tasks(
            tasks, filter_request, current_time
        )
        
        return scored_tasks
    
    async def get_task_jar(
        self,
        user_id: UUID,
        jar_request: TaskJarRequest,
        current_time: Optional[datetime] = None
    ) -> List[Task]:
        """
        Get random task selection for decision fatigue reduction.
        
        Args:
            user_id: User identifier
            jar_request: Task jar parameters
            current_time: Current time for filtering
            
        Returns:
            List[Task]: Randomly selected tasks
        """
        if current_time is None:
            current_time = datetime.utcnow()
        
        # Build filter criteria from jar request
        filter_request = TaskFilterRequest(
            energy_level=jar_request.energy_level,
            max_duration=jar_request.max_duration,
            context_tags=jar_request.context_tags,
            status="pending",  # Only pending tasks for jar
            limit=100  # Get larger pool for random selection
        )
        
        # Get filtered tasks
        available_tasks = await self.get_filtered_tasks(
            user_id, filter_request, current_time
        )
        
        # Exclude recently completed or skipped tasks if requested
        if jar_request.exclude_recent:
            available_tasks = await self._exclude_recent_tasks(
                user_id, available_tasks, current_time
            )
        
        # Randomly select tasks with weighted probability
        selected_tasks = self._weighted_random_selection(
            available_tasks, jar_request.jar_size
        )
        
        return selected_tasks
    
    async def get_energy_matched_tasks(
        self,
        user_id: UUID,
        current_energy: str,
        max_duration: Optional[int] = None,
        limit: int = 10
    ) -> List[Task]:
        """
        Get tasks that match user's current energy level.
        
        Args:
            user_id: User identifier
            current_energy: Current energy level (low, medium, high)
            max_duration: Maximum task duration in minutes
            limit: Maximum number of tasks to return
            
        Returns:
            List[Task]: Energy-matched tasks
        """
        energy_levels = {"low": 1, "medium": 2, "high": 3}
        current_level = energy_levels.get(current_energy, 2)
        
        query = select(Task).where(
            and_(
                Task.user_id == user_id,
                Task.status.in_(["pending", "in_progress"]),
                func.case(
                    (Task.energy_level == "low", 1),
                    (Task.energy_level == "medium", 2),
                    (Task.energy_level == "high", 3),
                    else_=2
                ) <= current_level
            )
        )
        
        # Apply duration filter if specified
        if max_duration:
            query = query.where(
                or_(
                    Task.estimated_duration.is_(None),
                    Task.estimated_duration <= max_duration
                )
            )
        
        # Order by priority and due date
        query = query.order_by(
            Task.priority == "urgent",
            Task.due_date.asc().nullslast(),
            Task.created_at.desc()
        ).limit(limit)
        
        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def get_quick_wins(
        self,
        user_id: UUID,
        max_duration: int = 15,
        limit: int = 5
    ) -> List[Task]:
        """
        Get quick, low-energy tasks for momentum building.
        
        Args:
            user_id: User identifier
            max_duration: Maximum task duration in minutes
            limit: Maximum number of tasks to return
            
        Returns:
            List[Task]: Quick win tasks
        """
        query = select(Task).where(
            and_(
                Task.user_id == user_id,
                Task.status == "pending",
                Task.energy_level == "low",
                or_(
                    Task.estimated_duration.is_(None),
                    Task.estimated_duration <= max_duration
                )
            )
        ).order_by(
            Task.priority.desc(),
            Task.estimated_duration.asc().nullslast()
        ).limit(limit)
        
        result = await self.db.execute(query)
        return result.scalars().all()
    
    def _apply_adaptive_filters(
        self,
        query,
        filter_request: TaskFilterRequest,
        current_time: datetime
    ):
        """Apply adaptive filtering criteria to query."""
        
        # Filter by status
        if filter_request.status:
            query = query.where(Task.status == filter_request.status)
        
        # Filter by priority
        if filter_request.priority:
            query = query.where(Task.priority == filter_request.priority)
        
        # Filter by energy level (tasks with equal or lower energy requirement)
        if filter_request.energy_level:
            energy_levels = {"low": 1, "medium": 2, "high": 3}
            current_level = energy_levels.get(filter_request.energy_level, 2)
            
            query = query.where(
                func.case(
                    (Task.energy_level == "low", 1),
                    (Task.energy_level == "medium", 2),
                    (Task.energy_level == "high", 3),
                    else_=2
                ) <= current_level
            )
        
        # Filter by maximum duration
        if filter_request.max_duration:
            query = query.where(
                or_(
                    Task.estimated_duration.is_(None),
                    Task.estimated_duration <= filter_request.max_duration
                )
            )
        
        # Filter by context tags
        if filter_request.context_tags:
            for tag in filter_request.context_tags:
                query = query.where(Task.context_tags.contains([tag]))
        
        # Filter tasks due soon (within 24 hours)
        if filter_request.due_soon:
            tomorrow = current_time + timedelta(days=1)
            query = query.where(
                and_(
                    Task.due_date.is_not(None),
                    Task.due_date <= tomorrow
                )
            )
        
        # Apply pagination
        query = query.offset(filter_request.offset).limit(filter_request.limit)
        
        return query
    
    async def _score_and_sort_tasks(
        self,
        tasks: List[Task],
        filter_request: TaskFilterRequest,
        current_time: datetime
    ) -> List[Task]:
        """
        Score and sort tasks based on ADHD-specific criteria.
        
        Args:
            tasks: List of tasks to score
            filter_request: Original filter request for context
            current_time: Current time for scoring
            
        Returns:
            List[Task]: Scored and sorted tasks
        """
        scored_tasks = []
        
        for task in tasks:
            score = calculate_task_score(
                task=task,
                current_energy=filter_request.energy_level,
                current_time=current_time,
                user_context=filter_request.context_tags or []
            )
            scored_tasks.append((task, score))
        
        # Sort by score (descending) and return tasks
        scored_tasks.sort(key=lambda x: x[1], reverse=True)
        return [task for task, score in scored_tasks]
    
    async def _exclude_recent_tasks(
        self,
        user_id: UUID,
        tasks: List[Task],
        current_time: datetime,
        hours_back: int = 24
    ) -> List[Task]:
        """
        Exclude tasks that were recently completed or interacted with.
        
        Args:
            user_id: User identifier
            tasks: List of tasks to filter
            current_time: Current time
            hours_back: Hours to look back for recent activity
            
        Returns:
            List[Task]: Tasks excluding recent ones
        """
        cutoff_time = current_time - timedelta(hours=hours_back)
        
        # Get recently completed tasks
        recent_query = select(Task.id).where(
            and_(
                Task.user_id == user_id,
                Task.completed_at >= cutoff_time
            )
        )
        
        result = await self.db.execute(recent_query)
        recent_task_ids = {row[0] for row in result.fetchall()}
        
        # Filter out recent tasks
        filtered_tasks = [
            task for task in tasks 
            if task.id not in recent_task_ids
        ]
        
        return filtered_tasks
    
    def _weighted_random_selection(
        self,
        tasks: List[Task],
        count: int
    ) -> List[Task]:
        """
        Perform weighted random selection of tasks.
        
        Args:
            tasks: Available tasks for selection
            count: Number of tasks to select
            
        Returns:
            List[Task]: Randomly selected tasks
        """
        if len(tasks) <= count:
            return tasks
        
        # Create weights based on priority and urgency
        weights = []
        for task in tasks:
            weight = 1.0
            
            # Higher weight for urgent/high priority tasks
            if task.priority == "urgent":
                weight *= 3.0
            elif task.priority == "high":
                weight *= 2.0
            
            # Higher weight for tasks due soon
            if task.due_date:
                days_until_due = (task.due_date - datetime.utcnow()).days
                if days_until_due <= 1:
                    weight *= 2.5
                elif days_until_due <= 3:
                    weight *= 1.5
            
            weights.append(weight)
        
        # Perform weighted random selection
        selected_tasks = random.choices(
            tasks, weights=weights, k=min(count, len(tasks))
        )
        
        return selected_tasks
