"""
Task management service for ADHD-optimized CRUD operations.

This service provides comprehensive task management functionality
with ADHD-specific features like energy level tracking and context awareness.
"""

from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from uuid import UUID

from sqlalchemy import select, update, delete, func, and_, or_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from chronos.app.models.task import Task
from chronos.app.models.user import User
from chronos.app.schemas.task import TaskCreate, TaskUpdate, TaskFilterRequest
from chronos.app.core.exceptions import NotFoundError, ValidationError


class TaskService:
    """
    Service for task management with ADHD-specific optimizations.
    
    This service handles all task-related operations including CRUD,
    filtering, and ADHD-specific features like energy level matching.
    """
    
    def __init__(self, db: AsyncSession):
        """
        Initialize task service.
        
        Args:
            db: Database session for operations
        """
        self.db = db
    
    async def create_task(
        self,
        user_id: UUID,
        task_data: TaskCreate
    ) -> Task:
        """
        Create a new task with ADHD-optimized defaults.
        
        Args:
            user_id: ID of the user creating the task
            task_data: Task creation data
            
        Returns:
            Task: Created task instance
            
        Raises:
            ValidationError: If parent task doesn't exist or belongs to different user
        """
        # Validate parent task if specified
        if task_data.parent_task_id:
            parent_task = await self.get_task_by_id(user_id, task_data.parent_task_id)
            if not parent_task:
                raise ValidationError("Parent task not found")
        
        # Create task with ADHD-optimized defaults
        task = Task(
            user_id=user_id,
            title=task_data.title,
            description=task_data.description,
            priority=task_data.priority,
            energy_level=task_data.energy_level,
            estimated_duration=task_data.estimated_duration,
            context_tags=task_data.context_tags,
            due_date=task_data.due_date,
            parent_task_id=task_data.parent_task_id,
        )
        
        self.db.add(task)
        await self.db.commit()
        await self.db.refresh(task)
        
        return task
    
    async def get_task_by_id(
        self,
        user_id: UUID,
        task_id: UUID,
        include_subtasks: bool = False
    ) -> Optional[Task]:
        """
        Get a task by ID with user ownership validation.
        
        Args:
            user_id: ID of the user requesting the task
            task_id: ID of the task to retrieve
            include_subtasks: Whether to include subtasks in the result
            
        Returns:
            Task: Task instance if found, None otherwise
        """
        query = select(Task).where(
            and_(Task.id == task_id, Task.user_id == user_id)
        )
        
        if include_subtasks:
            query = query.options(selectinload(Task.subtasks))
        
        result = await self.db.execute(query)
        return result.scalar_one_or_none()
    
    async def update_task(
        self,
        user_id: UUID,
        task_id: UUID,
        task_data: TaskUpdate
    ) -> Optional[Task]:
        """
        Update an existing task.
        
        Args:
            user_id: ID of the user updating the task
            task_id: ID of the task to update
            task_data: Task update data
            
        Returns:
            Task: Updated task instance if found, None otherwise
        """
        task = await self.get_task_by_id(user_id, task_id)
        if not task:
            return None
        
        # Update fields that are provided
        update_data = task_data.dict(exclude_unset=True)
        
        # Handle status changes
        if "status" in update_data:
            if update_data["status"] == "completed" and not task.completed_at:
                update_data["completed_at"] = datetime.utcnow()
            elif update_data["status"] != "completed" and task.completed_at:
                update_data["completed_at"] = None
        
        for field, value in update_data.items():
            setattr(task, field, value)
        
        await self.db.commit()
        await self.db.refresh(task)
        
        return task
    
    async def delete_task(
        self,
        user_id: UUID,
        task_id: UUID
    ) -> bool:
        """
        Delete a task and all its subtasks.
        
        Args:
            user_id: ID of the user deleting the task
            task_id: ID of the task to delete
            
        Returns:
            bool: True if task was deleted, False if not found
        """
        task = await self.get_task_by_id(user_id, task_id)
        if not task:
            return False
        
        await self.db.delete(task)
        await self.db.commit()
        
        return True
    
    async def get_user_tasks(
        self,
        user_id: UUID,
        filter_request: Optional[TaskFilterRequest] = None,
        include_subtasks: bool = False
    ) -> List[Task]:
        """
        Get user's tasks with optional filtering.
        
        Args:
            user_id: ID of the user requesting tasks
            filter_request: Optional filtering criteria
            include_subtasks: Whether to include subtasks in results
            
        Returns:
            List[Task]: Filtered list of tasks
        """
        query = select(Task).where(Task.user_id == user_id)
        
        if include_subtasks:
            query = query.options(selectinload(Task.subtasks))
        
        # Apply filters if provided
        if filter_request:
            query = self._apply_filters(query, filter_request)
        
        # Default ordering: urgent first, then by due date, then by created date
        query = query.order_by(
            Task.priority == "urgent",
            Task.due_date.asc().nullslast(),
            Task.created_at.desc()
        )
        
        # Apply pagination
        if filter_request:
            query = query.offset(filter_request.offset).limit(filter_request.limit)
        
        result = await self.db.execute(query)
        return result.scalars().all()
    
    def _apply_filters(self, query, filter_request: TaskFilterRequest):
        """Apply filtering criteria to task query."""
        
        # Filter by status
        if filter_request.status:
            query = query.where(Task.status == filter_request.status)
        
        # Filter by priority
        if filter_request.priority:
            query = query.where(Task.priority == filter_request.priority)
        
        # Filter by energy level (tasks with equal or lower energy requirement)
        if filter_request.energy_level:
            energy_levels = {"low": 1, "medium": 2, "high": 3}
            current_level = energy_levels.get(filter_request.energy_level, 2)
            
            query = query.where(
                func.case(
                    (Task.energy_level == "low", 1),
                    (Task.energy_level == "medium", 2),
                    (Task.energy_level == "high", 3),
                    else_=2
                ) <= current_level
            )
        
        # Filter by maximum duration
        if filter_request.max_duration:
            query = query.where(
                or_(
                    Task.estimated_duration.is_(None),
                    Task.estimated_duration <= filter_request.max_duration
                )
            )
        
        # Filter by context tags
        if filter_request.context_tags:
            for tag in filter_request.context_tags:
                query = query.where(Task.context_tags.contains([tag]))
        
        # Filter tasks due soon (within 24 hours)
        if filter_request.due_soon:
            tomorrow = datetime.utcnow() + timedelta(days=1)
            query = query.where(
                and_(
                    Task.due_date.is_not(None),
                    Task.due_date <= tomorrow
                )
            )
        
        return query
    
    async def mark_task_as_chunked(
        self,
        user_id: UUID,
        task_id: UUID
    ) -> Optional[Task]:
        """
        Mark a task as chunked after AI processing.
        
        Args:
            user_id: ID of the user
            task_id: ID of the task to mark as chunked
            
        Returns:
            Task: Updated task if found, None otherwise
        """
        task = await self.get_task_by_id(user_id, task_id)
        if not task:
            return None
        
        task.is_chunked = True
        await self.db.commit()
        await self.db.refresh(task)
        
        return task
    
    async def get_task_completion_stats(
        self,
        user_id: UUID,
        days: int = 30
    ) -> Dict[str, Any]:
        """
        Get task completion statistics for user.
        
        Args:
            user_id: ID of the user
            days: Number of days to look back
            
        Returns:
            Dict[str, Any]: Completion statistics
        """
        since_date = datetime.utcnow() - timedelta(days=days)
        
        # Get completion stats
        completed_query = select(func.count(Task.id)).where(
            and_(
                Task.user_id == user_id,
                Task.status == "completed",
                Task.completed_at >= since_date
            )
        )
        
        total_query = select(func.count(Task.id)).where(
            and_(
                Task.user_id == user_id,
                Task.created_at >= since_date
            )
        )
        
        completed_result = await self.db.execute(completed_query)
        total_result = await self.db.execute(total_query)
        
        completed_count = completed_result.scalar()
        total_count = total_result.scalar()
        
        completion_rate = (completed_count / total_count * 100) if total_count > 0 else 0
        
        return {
            "completed_tasks": completed_count,
            "total_tasks": total_count,
            "completion_rate": round(completion_rate, 2),
            "period_days": days
        }
