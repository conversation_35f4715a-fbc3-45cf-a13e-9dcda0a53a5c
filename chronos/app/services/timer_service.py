"""
Timer service for Project Chronos.

This module provides real-time timer management for focus sessions,
including Redis-based state persistence and WebSocket integration.
"""

import asyncio
import json
from datetime import datetime, timedelta
from typing import Dict, Optional
from uuid import UUID

import redis.asyncio as redis

from chronos.app.core.config import settings
from chronos.app.core.exceptions import TimerError, TimerNotFoundError
from chronos.app.schemas.focus import TimerState


class Timer:
    """Individual timer instance."""
    
    def __init__(
        self,
        session_id: UUID,
        duration: int,
        timer_type: str = "focus",
        start_time: Optional[datetime] = None
    ):
        """
        Initialize timer.
        
        Args:
            session_id: Associated session ID
            duration: Timer duration in minutes
            timer_type: Type of timer (focus, break)
            start_time: Timer start time (defaults to now)
        """
        self.session_id = session_id
        self.duration = duration  # in minutes
        self.timer_type = timer_type
        self.start_time = start_time or datetime.utcnow()
        self.paused_at: Optional[datetime] = None
        self.paused_duration = 0  # Total paused time in seconds
        self.is_active = True
        self.is_paused = False
    
    def get_elapsed_seconds(self) -> int:
        """Get elapsed time in seconds."""
        if self.is_paused and self.paused_at:
            return int((self.paused_at - self.start_time).total_seconds()) - self.paused_duration
        
        current_time = datetime.utcnow()
        elapsed = int((current_time - self.start_time).total_seconds()) - self.paused_duration
        return max(0, elapsed)
    
    def get_remaining_seconds(self) -> int:
        """Get remaining time in seconds."""
        total_seconds = self.duration * 60
        elapsed = self.get_elapsed_seconds()
        return max(0, total_seconds - elapsed)
    
    def get_progress_percentage(self) -> float:
        """Get progress as percentage (0.0 to 1.0)."""
        total_seconds = self.duration * 60
        elapsed = self.get_elapsed_seconds()
        return min(1.0, elapsed / total_seconds)
    
    def pause(self) -> None:
        """Pause the timer."""
        if not self.is_paused and self.is_active:
            self.paused_at = datetime.utcnow()
            self.is_paused = True
    
    def resume(self) -> None:
        """Resume the timer."""
        if self.is_paused and self.paused_at:
            # Add paused time to total paused duration
            pause_duration = int((datetime.utcnow() - self.paused_at).total_seconds())
            self.paused_duration += pause_duration
            self.paused_at = None
            self.is_paused = False
    
    def extend(self, additional_minutes: int) -> None:
        """Extend timer duration."""
        self.duration += additional_minutes
    
    def stop(self) -> None:
        """Stop the timer."""
        self.is_active = False
        self.is_paused = False
    
    def to_dict(self) -> Dict:
        """Convert timer to dictionary for serialization."""
        return {
            "session_id": str(self.session_id),
            "duration": self.duration,
            "timer_type": self.timer_type,
            "start_time": self.start_time.isoformat(),
            "paused_at": self.paused_at.isoformat() if self.paused_at else None,
            "paused_duration": self.paused_duration,
            "is_active": self.is_active,
            "is_paused": self.is_paused,
        }
    
    @classmethod
    def from_dict(cls, data: Dict) -> "Timer":
        """Create timer from dictionary."""
        timer = cls(
            session_id=UUID(data["session_id"]),
            duration=data["duration"],
            timer_type=data["timer_type"],
            start_time=datetime.fromisoformat(data["start_time"])
        )
        timer.paused_at = (
            datetime.fromisoformat(data["paused_at"])
            if data["paused_at"] else None
        )
        timer.paused_duration = data["paused_duration"]
        timer.is_active = data["is_active"]
        timer.is_paused = data["is_paused"]
        return timer


class TimerService:
    """Real-time timer management service."""
    
    def __init__(self):
        """Initialize timer service with Redis connection."""
        self.redis_client: Optional[redis.Redis] = None
        self.active_timers: Dict[str, Timer] = {}
        self._timer_key_prefix = "chronos:timer:"
    
    async def _get_redis(self) -> redis.Redis:
        """Get Redis connection."""
        if not self.redis_client:
            self.redis_client = redis.from_url(
                str(settings.REDIS_URL),
                encoding="utf-8",
                decode_responses=True
            )
        return self.redis_client
    
    def _get_timer_key(self, session_id: UUID) -> str:
        """Get Redis key for timer."""
        return f"{self._timer_key_prefix}{session_id}"
    
    async def create_timer(
        self,
        session_id: UUID,
        duration: int,
        timer_type: str = "focus"
    ) -> Timer:
        """
        Create and start a new timer.
        
        Args:
            session_id: Session identifier
            duration: Timer duration in minutes
            timer_type: Type of timer
            
        Returns:
            Created timer instance
        """
        try:
            timer = Timer(
                session_id=session_id,
                duration=duration,
                timer_type=timer_type
            )
            
            # Store in Redis for persistence
            redis_client = await self._get_redis()
            timer_key = self._get_timer_key(session_id)
            await redis_client.setex(
                timer_key,
                timedelta(hours=24),  # Expire after 24 hours
                json.dumps(timer.to_dict())
            )
            
            # Store in memory for quick access
            self.active_timers[str(session_id)] = timer
            
            return timer
            
        except Exception as e:
            raise TimerError(f"Failed to create timer: {str(e)}")
    
    async def get_timer_state(self, session_id: UUID) -> TimerState:
        """
        Get current timer state.
        
        Args:
            session_id: Session identifier
            
        Returns:
            Current timer state
            
        Raises:
            TimerNotFoundError: If timer not found
        """
        timer = await self._get_timer(session_id)
        
        return TimerState(
            session_id=session_id,
            status="active" if timer.is_active and not timer.is_paused else "paused" if timer.is_paused else "stopped",
            elapsed_time=timer.get_elapsed_seconds() // 60,  # Convert to minutes
            remaining_time=timer.get_remaining_seconds() // 60,  # Convert to minutes
            progress_percentage=timer.get_progress_percentage(),
            is_paused=timer.is_paused,
            last_updated=datetime.utcnow()
        )
    
    async def pause_timer(self, session_id: UUID) -> TimerState:
        """
        Pause active timer.
        
        Args:
            session_id: Session identifier
            
        Returns:
            Updated timer state
        """
        timer = await self._get_timer(session_id)
        timer.pause()
        await self._save_timer(timer)
        return await self.get_timer_state(session_id)
    
    async def resume_timer(self, session_id: UUID) -> TimerState:
        """
        Resume paused timer.
        
        Args:
            session_id: Session identifier
            
        Returns:
            Updated timer state
        """
        timer = await self._get_timer(session_id)
        timer.resume()
        await self._save_timer(timer)
        return await self.get_timer_state(session_id)
    
    async def extend_timer(
        self,
        session_id: UUID,
        additional_minutes: int
    ) -> TimerState:
        """
        Extend timer duration.
        
        Args:
            session_id: Session identifier
            additional_minutes: Minutes to add
            
        Returns:
            Updated timer state
        """
        timer = await self._get_timer(session_id)
        timer.extend(additional_minutes)
        await self._save_timer(timer)
        return await self.get_timer_state(session_id)
    
    async def stop_timer(self, session_id: UUID) -> TimerState:
        """
        Stop and remove timer.

        Args:
            session_id: Session identifier

        Returns:
            Final timer state
        """
        timer = await self._get_timer(session_id)

        # Get final state before stopping
        final_state = TimerState(
            session_id=session_id,
            status="stopped",
            elapsed_time=timer.get_elapsed_seconds() // 60,
            remaining_time=0,
            progress_percentage=1.0,
            is_paused=False,
            last_updated=datetime.utcnow()
        )

        timer.stop()

        # Remove from Redis and memory
        redis_client = await self._get_redis()
        timer_key = self._get_timer_key(session_id)
        await redis_client.delete(timer_key)

        if str(session_id) in self.active_timers:
            del self.active_timers[str(session_id)]

        return final_state
    
    async def _get_timer(self, session_id: UUID) -> Timer:
        """Get timer from memory or Redis."""
        session_str = str(session_id)
        
        # Try memory first
        if session_str in self.active_timers:
            return self.active_timers[session_str]
        
        # Try Redis
        redis_client = await self._get_redis()
        timer_key = self._get_timer_key(session_id)
        timer_data = await redis_client.get(timer_key)
        
        if not timer_data:
            raise TimerNotFoundError(f"Timer not found for session {session_id}")
        
        # Restore timer from Redis
        timer = Timer.from_dict(json.loads(timer_data))
        self.active_timers[session_str] = timer
        return timer
    
    async def _save_timer(self, timer: Timer) -> None:
        """Save timer to Redis."""
        redis_client = await self._get_redis()
        timer_key = self._get_timer_key(timer.session_id)
        await redis_client.setex(
            timer_key,
            timedelta(hours=24),
            json.dumps(timer.to_dict())
        )
    
    async def cleanup_expired_timers(self) -> None:
        """Clean up expired timers (background task)."""
        try:
            redis_client = await self._get_redis()
            
            # Get all timer keys
            pattern = f"{self._timer_key_prefix}*"
            keys = await redis_client.keys(pattern)
            
            for key in keys:
                timer_data = await redis_client.get(key)
                if timer_data:
                    timer_dict = json.loads(timer_data)
                    start_time = datetime.fromisoformat(timer_dict["start_time"])
                    
                    # Remove timers older than 24 hours
                    if datetime.utcnow() - start_time > timedelta(hours=24):
                        await redis_client.delete(key)
                        session_id = timer_dict["session_id"]
                        if session_id in self.active_timers:
                            del self.active_timers[session_id]
                            
        except Exception as e:
            # Log error but don't raise - this is a background cleanup
            print(f"Timer cleanup error: {str(e)}")
    
    async def close(self) -> None:
        """Close Redis connection."""
        if self.redis_client:
            await self.redis_client.close()
