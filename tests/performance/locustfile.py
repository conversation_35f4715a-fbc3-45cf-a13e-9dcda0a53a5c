"""
Performance testing for Project Chronos ADHD-focused features.

This module uses Locust to test the performance of ADHD-specific features
under load, ensuring they remain responsive for users who need reliable,
fast interactions.
"""

import random
import json
from datetime import datetime, timedelta
from locust import HttpUser, task, between, events
from locust.exception import RescheduleTask


class ADHDUserBehavior(HttpUser):
    """
    Simulates ADHD user behavior patterns for performance testing.
    
    Models realistic usage patterns including:
    - Frequent task switching
    - Quick task creation bursts
    - Energy-based filtering
    - AI chunking requests
    """
    
    wait_time = between(1, 5)  # ADHD users may have variable interaction patterns
    
    def on_start(self):
        """Initialize user session with ADHD profile."""
        # Create test user with ADHD preferences
        self.user_data = {
            "email": f"loadtest_user_{random.randint(1000, 9999)}@chronos.test",
            "password": "test_password_123",
            "adhd_diagnosed": True,
            "preferences": {
                "energy_patterns": {
                    "morning": random.choice(["low", "medium", "high"]),
                    "afternoon": random.choice(["low", "medium", "high"]),
                    "evening": random.choice(["low", "medium", "high"])
                },
                "notification_preferences": {
                    "persistent": True,
                    "gentle": True,
                    "frequency": "high"
                },
                "chunking_preferences": {
                    "default_size": "small",
                    "max_subtasks": 5,
                    "auto_chunk_threshold": 45
                }
            }
        }
        
        # Register and login
        self.register_user()
        self.login_user()
        
        # Track user state for realistic behavior
        self.current_energy = random.choice(["low", "medium", "high"])
        self.task_count = 0
        self.session_start = datetime.now()
    
    def register_user(self):
        """Register a test user."""
        response = self.client.post("/api/v1/auth/register", json=self.user_data)
        if response.status_code not in [201, 409]:  # 409 = already exists
            print(f"Registration failed: {response.status_code}")
    
    def login_user(self):
        """Login and get authentication token."""
        login_data = {
            "email": self.user_data["email"],
            "password": self.user_data["password"]
        }
        
        with self.client.post("/api/v1/auth/login", json=login_data, catch_response=True) as response:
            if response.status_code == 200:
                self.auth_token = response.json().get("access_token")
                self.headers = {"Authorization": f"Bearer {self.auth_token}"}
                response.success()
            else:
                response.failure(f"Login failed: {response.status_code}")
                raise RescheduleTask()
    
    @task(3)
    def create_adhd_task(self):
        """Create ADHD-optimized tasks (high frequency - common action)."""
        task_data = {
            "title": f"ADHD Task {self.task_count + 1}",
            "description": f"Task created during load test at {datetime.now()}",
            "priority": random.choice(["low", "medium", "high", "urgent"]),
            "energy_level": random.choice(["low", "medium", "high"]),
            "estimated_duration": random.randint(5, 90),  # ADHD-friendly durations
            "context_tags": random.sample(
                ["home", "office", "computer", "phone", "errands", "creative"], 
                k=random.randint(1, 3)
            ),
            "due_date": (datetime.now() + timedelta(days=random.randint(1, 7))).isoformat()
        }
        
        with self.client.post("/api/v1/tasks/", json=task_data, headers=self.headers, catch_response=True) as response:
            if response.status_code == 201:
                self.task_count += 1
                response.success()
            else:
                response.failure(f"Task creation failed: {response.status_code}")
    
    @task(2)
    def filter_tasks_by_energy(self):
        """Filter tasks by energy level (common ADHD behavior)."""
        energy_level = random.choice(["low", "medium", "high"])
        
        with self.client.get(
            f"/api/v1/tasks/?energy_level={energy_level}", 
            headers=self.headers,
            name="/api/v1/tasks/?energy_level=[level]",
            catch_response=True
        ) as response:
            if response.status_code == 200:
                tasks = response.json()
                # Verify filtering worked
                if all(task.get("energy_level") == energy_level for task in tasks):
                    response.success()
                else:
                    response.failure("Energy level filtering failed")
            else:
                response.failure(f"Energy filtering failed: {response.status_code}")
    
    @task(2)
    def get_task_jar(self):
        """Request task jar for decision fatigue reduction."""
        jar_size = random.randint(3, 7)
        
        with self.client.get(
            f"/api/v1/tasks/jar?jar_size={jar_size}",
            headers=self.headers,
            name="/api/v1/tasks/jar",
            catch_response=True
        ) as response:
            if response.status_code == 200:
                jar_tasks = response.json()
                if len(jar_tasks) <= jar_size:
                    response.success()
                else:
                    response.failure("Task jar returned too many tasks")
            else:
                response.failure(f"Task jar failed: {response.status_code}")
    
    @task(1)
    def request_ai_chunking(self):
        """Request AI task chunking (less frequent but important)."""
        # First create a large task
        large_task_data = {
            "title": f"Large Project {random.randint(1, 1000)}",
            "description": "A complex project that needs chunking",
            "estimated_duration": random.randint(120, 300),  # 2-5 hours
            "energy_level": "high",
            "priority": "medium"
        }
        
        # Create the task
        create_response = self.client.post("/api/v1/tasks/", json=large_task_data, headers=self.headers)
        if create_response.status_code != 201:
            return
        
        task_id = create_response.json()["id"]
        
        # Request chunking
        chunk_request = {
            "chunk_size": random.choice(["small", "medium"]),
            "context": "User has ADHD and needs manageable steps"
        }
        
        with self.client.post(
            f"/api/v1/tasks/{task_id}/chunk",
            json=chunk_request,
            headers=self.headers,
            name="/api/v1/tasks/[id]/chunk",
            catch_response=True
        ) as response:
            if response.status_code == 200:
                subtasks = response.json()
                if isinstance(subtasks, list) and len(subtasks) > 0:
                    response.success()
                else:
                    response.failure("Chunking returned invalid response")
            elif response.status_code == 503:
                # AI service unavailable - this is acceptable in load testing
                response.success()
            else:
                response.failure(f"Chunking failed: {response.status_code}")
    
    @task(2)
    def filter_by_context(self):
        """Filter tasks by context tags."""
        context = random.choice(["home", "office", "computer", "phone"])
        
        with self.client.get(
            f"/api/v1/tasks/?context_tags={context}",
            headers=self.headers,
            name="/api/v1/tasks/?context_tags=[context]",
            catch_response=True
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Context filtering failed: {response.status_code}")
    
    @task(1)
    def update_task_status(self):
        """Update task status (completing tasks)."""
        # Get user's tasks first
        response = self.client.get("/api/v1/tasks/", headers=self.headers)
        if response.status_code != 200:
            return
        
        tasks = response.json()
        if not tasks:
            return
        
        # Pick a random pending task
        pending_tasks = [t for t in tasks if t.get("status") == "pending"]
        if not pending_tasks:
            return
        
        task = random.choice(pending_tasks)
        task_id = task["id"]
        
        # Update to completed with actual duration
        update_data = {
            "status": "completed",
            "actual_duration": random.randint(
                max(1, task.get("estimated_duration", 30) - 15),
                task.get("estimated_duration", 30) + 30
            )
        }
        
        with self.client.patch(
            f"/api/v1/tasks/{task_id}",
            json=update_data,
            headers=self.headers,
            name="/api/v1/tasks/[id]",
            catch_response=True
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Task update failed: {response.status_code}")
    
    @task(1)
    def get_user_profile(self):
        """Get user profile (checking preferences)."""
        with self.client.get("/api/v1/users/me", headers=self.headers, catch_response=True) as response:
            if response.status_code == 200:
                profile = response.json()
                if "preferences" in profile:
                    response.success()
                else:
                    response.failure("Profile missing preferences")
            else:
                response.failure(f"Profile retrieval failed: {response.status_code}")
    
    @task(1)
    def check_adhd_features_status(self):
        """Check ADHD features availability."""
        with self.client.get("/api/v1/features/adhd-status", catch_response=True) as response:
            if response.status_code == 200:
                features = response.json()
                required_features = ["task_management", "ai_chunking", "adaptive_filtering", "task_jar"]
                if all(feature in features for feature in required_features):
                    response.success()
                else:
                    response.failure("Missing required ADHD features")
            else:
                response.failure(f"Feature status check failed: {response.status_code}")


class HighFrequencyADHDUser(ADHDUserBehavior):
    """
    Simulates ADHD users during hyperfocus or high-activity periods.
    
    Higher frequency interactions to test system under peak load.
    """
    
    wait_time = between(0.5, 2)  # Faster interactions during hyperfocus
    
    @task(5)
    def rapid_task_creation(self):
        """Rapid task creation during hyperfocus periods."""
        self.create_adhd_task()
    
    @task(3)
    def frequent_filtering(self):
        """Frequent task filtering and searching."""
        self.filter_tasks_by_energy()


class LowEnergyADHDUser(ADHDUserBehavior):
    """
    Simulates ADHD users during low energy periods.
    
    Slower interactions, more browsing, less task creation.
    """
    
    wait_time = between(3, 10)  # Slower interactions during low energy
    
    @task(1)
    def minimal_task_creation(self):
        """Minimal task creation during low energy."""
        self.create_adhd_task()
    
    @task(3)
    def browse_existing_tasks(self):
        """More browsing, less action during low energy."""
        self.filter_tasks_by_energy()
    
    @task(2)
    def check_easy_tasks(self):
        """Look for easy, low-energy tasks."""
        with self.client.get(
            "/api/v1/tasks/?energy_level=low&max_duration=30",
            headers=self.headers,
            name="/api/v1/tasks/?energy_level=low&max_duration=30",
            catch_response=True
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Easy task filtering failed: {response.status_code}")


# Performance test event handlers
@events.test_start.add_listener
def on_test_start(environment, **kwargs):
    """Initialize performance test."""
    print("🚀 Starting ADHD-focused performance testing...")
    print("Testing realistic ADHD user behavior patterns under load")


@events.test_stop.add_listener
def on_test_stop(environment, **kwargs):
    """Cleanup after performance test."""
    print("🏁 ADHD performance testing completed")
    
    # Print summary of ADHD-specific metrics
    stats = environment.stats
    
    print("\n📊 ADHD Feature Performance Summary:")
    print("-" * 50)
    
    # Key ADHD endpoints to monitor
    adhd_endpoints = [
        "/api/v1/tasks/",
        "/api/v1/tasks/jar",
        "/api/v1/tasks/[id]/chunk",
        "/api/v1/tasks/?energy_level=[level]",
        "/api/v1/features/adhd-status"
    ]
    
    for endpoint in adhd_endpoints:
        if endpoint in stats.entries:
            entry = stats.entries[endpoint]
            print(f"{endpoint}:")
            print(f"  Requests: {entry.num_requests}")
            print(f"  Avg Response Time: {entry.avg_response_time:.2f}ms")
            print(f"  95th Percentile: {entry.get_response_time_percentile(0.95):.2f}ms")
            print(f"  Failure Rate: {entry.num_failures / entry.num_requests * 100:.2f}%")
            print()
    
    # Check if ADHD-critical endpoints meet performance requirements
    critical_endpoints = ["/api/v1/tasks/", "/api/v1/tasks/jar"]
    performance_issues = []
    
    for endpoint in critical_endpoints:
        if endpoint in stats.entries:
            entry = stats.entries[endpoint]
            avg_time = entry.avg_response_time
            p95_time = entry.get_response_time_percentile(0.95)
            failure_rate = entry.num_failures / entry.num_requests * 100
            
            # ADHD users need fast, reliable responses
            if avg_time > 500:  # 500ms average
                performance_issues.append(f"{endpoint} avg response time too high: {avg_time:.2f}ms")
            if p95_time > 1000:  # 1s 95th percentile
                performance_issues.append(f"{endpoint} 95th percentile too high: {p95_time:.2f}ms")
            if failure_rate > 1:  # 1% failure rate
                performance_issues.append(f"{endpoint} failure rate too high: {failure_rate:.2f}%")
    
    if performance_issues:
        print("⚠️  ADHD Performance Issues Detected:")
        for issue in performance_issues:
            print(f"  - {issue}")
    else:
        print("✅ All ADHD-critical endpoints meet performance requirements")
