"""
Testing utilities and helpers for Project Chronos.

This module provides utility functions and classes for testing
ADHD-focused features, including assertion helpers, data validation,
and test scenario builders.
"""

import asyncio
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime, timedelta
from uuid import UUID
import json

from app.models.user import User
from app.models.task import Task


class ADHDTestAssertions:
    """
    Custom assertion helpers for ADHD-specific feature testing.
    
    Provides specialized assertions that validate ADHD-focused
    functionality like energy matching, time estimation accuracy,
    and notification persistence.
    """
    
    @staticmethod
    def assert_energy_level_appropriate(
        task: Task, 
        user_current_energy: str,
        tolerance: str = "strict"
    ) -> None:
        """
        Assert that task energy level matches user's current energy.
        
        Args:
            task: Task to validate
            user_current_energy: User's current energy level
            tolerance: Matching tolerance ("strict", "flexible", "any")
        """
        energy_hierarchy = {"low": 1, "medium": 2, "high": 3}
        
        task_energy = energy_hierarchy.get(task.energy_level, 0)
        user_energy = energy_hierarchy.get(user_current_energy, 0)
        
        if tolerance == "strict":
            assert task_energy == user_energy, (
                f"Task energy level '{task.energy_level}' doesn't match "
                f"user energy '{user_current_energy}'"
            )
        elif tolerance == "flexible":
            # Allow tasks one level below user energy
            assert task_energy <= user_energy, (
                f"Task energy level '{task.energy_level}' is too high "
                f"for user energy '{user_current_energy}'"
            )
        # "any" tolerance passes all checks
    
    @staticmethod
    def assert_task_duration_appropriate_for_adhd(
        task: Task,
        max_duration: int = 90,
        chunking_threshold: int = 60
    ) -> None:
        """
        Assert that task duration is appropriate for ADHD users.
        
        Args:
            task: Task to validate
            max_duration: Maximum recommended duration in minutes
            chunking_threshold: Duration above which chunking is recommended
        """
        duration = task.estimated_duration
        
        if duration > chunking_threshold:
            # Check if task has subtasks (is chunked)
            assert hasattr(task, 'subtasks') and task.subtasks, (
                f"Task duration {duration} minutes exceeds chunking threshold "
                f"of {chunking_threshold} minutes but task is not chunked"
            )
        
        if duration > max_duration:
            assert False, (
                f"Task duration {duration} minutes exceeds ADHD-friendly "
                f"maximum of {max_duration} minutes"
            )
    
    @staticmethod
    def assert_notification_persistence_adhd_appropriate(
        notification_config: Dict[str, Any],
        adhd_diagnosed: bool
    ) -> None:
        """
        Assert notification settings are appropriate for ADHD users.
        
        Args:
            notification_config: Notification configuration to validate
            adhd_diagnosed: Whether user has ADHD diagnosis
        """
        if adhd_diagnosed:
            assert notification_config.get("persistent", False), (
                "ADHD users should have persistent notifications enabled"
            )
            assert notification_config.get("gentle", False), (
                "ADHD users should have gentle notifications enabled"
            )
            assert notification_config.get("frequency") in ["high", "moderate"], (
                "ADHD users should have high or moderate notification frequency"
            )
        else:
            # Neurotypical users can have any settings
            pass
    
    @staticmethod
    def assert_time_estimation_tracking(
        task: Task,
        actual_duration: Optional[int] = None
    ) -> None:
        """
        Assert that time estimation tracking is working correctly.
        
        Args:
            task: Task to validate
            actual_duration: Actual time spent (if task is completed)
        """
        assert task.estimated_duration is not None, (
            "Task must have estimated duration for ADHD time tracking"
        )
        assert task.estimated_duration > 0, (
            "Estimated duration must be positive"
        )
        
        if actual_duration is not None:
            accuracy = actual_duration / task.estimated_duration
            # Store accuracy for learning
            task.time_estimation_accuracy = accuracy
            
            # Warn about significant underestimation (common ADHD pattern)
            if accuracy > 2.0:
                print(f"Warning: Task took {accuracy:.1f}x longer than estimated")


class TestDataValidator:
    """
    Validator for test data integrity and ADHD feature compliance.
    
    Ensures that test data accurately reflects ADHD user patterns
    and system requirements.
    """
    
    @staticmethod
    def validate_adhd_user_preferences(preferences: Dict[str, Any]) -> bool:
        """
        Validate ADHD user preferences structure and values.
        
        Args:
            preferences: User preferences dictionary
            
        Returns:
            True if preferences are valid for ADHD users
        """
        required_sections = [
            "energy_patterns",
            "notification_preferences", 
            "chunking_preferences"
        ]
        
        for section in required_sections:
            if section not in preferences:
                return False
        
        # Validate energy patterns
        energy_patterns = preferences["energy_patterns"]
        required_times = ["morning", "afternoon", "evening"]
        valid_levels = ["low", "medium", "high"]
        
        for time_period in required_times:
            if time_period not in energy_patterns:
                return False
            if energy_patterns[time_period] not in valid_levels:
                return False
        
        # Validate notification preferences
        notif_prefs = preferences["notification_preferences"]
        if not isinstance(notif_prefs.get("persistent"), bool):
            return False
        if not isinstance(notif_prefs.get("gentle"), bool):
            return False
        
        # Validate chunking preferences
        chunk_prefs = preferences["chunking_preferences"]
        if chunk_prefs.get("default_size") not in ["small", "medium", "large"]:
            return False
        if not isinstance(chunk_prefs.get("max_subtasks"), int):
            return False
        
        return True
    
    @staticmethod
    def validate_task_adhd_compliance(task: Task) -> List[str]:
        """
        Validate task compliance with ADHD best practices.
        
        Args:
            task: Task to validate
            
        Returns:
            List of validation warnings/errors
        """
        issues = []
        
        # Check duration
        if task.estimated_duration > 120:  # 2 hours
            issues.append("Task duration exceeds ADHD-friendly limit")
        
        # Check energy level assignment
        if not task.energy_level:
            issues.append("Task missing energy level assignment")
        
        # Check context tags
        if not task.context_tags or len(task.context_tags) == 0:
            issues.append("Task missing context tags for filtering")
        
        # Check priority assignment
        if not task.priority:
            issues.append("Task missing priority assignment")
        
        return issues


class AsyncTestHelper:
    """
    Helper class for async test operations.
    
    Provides utilities for testing async ADHD features like
    real-time notifications and background task processing.
    """
    
    @staticmethod
    async def wait_for_condition(
        condition: Callable[[], bool],
        timeout: float = 5.0,
        interval: float = 0.1
    ) -> bool:
        """
        Wait for a condition to become true with timeout.
        
        Args:
            condition: Function that returns True when condition is met
            timeout: Maximum time to wait in seconds
            interval: Check interval in seconds
            
        Returns:
            True if condition was met, False if timeout
        """
        start_time = asyncio.get_event_loop().time()
        
        while asyncio.get_event_loop().time() - start_time < timeout:
            if condition():
                return True
            await asyncio.sleep(interval)
        
        return False
    
    @staticmethod
    async def simulate_user_delay(
        delay_type: str = "adhd_typical",
        base_delay: float = 0.1
    ) -> None:
        """
        Simulate realistic user interaction delays for testing.
        
        Args:
            delay_type: Type of delay to simulate
            base_delay: Base delay in seconds
        """
        delay_multipliers = {
            "adhd_typical": 1.5,      # ADHD users may take longer
            "distracted": 3.0,        # Distraction scenario
            "hyperfocus": 0.5,        # Hyperfocus scenario
            "normal": 1.0             # Neurotypical baseline
        }
        
        multiplier = delay_multipliers.get(delay_type, 1.0)
        await asyncio.sleep(base_delay * multiplier)


class MockADHDScenarios:
    """
    Mock scenarios for testing ADHD-specific edge cases.
    
    Provides realistic test scenarios that reflect common
    ADHD challenges and use patterns.
    """
    
    @staticmethod
    def create_time_blindness_scenario() -> Dict[str, Any]:
        """
        Create scenario testing time blindness features.
        
        Returns:
            Test scenario data for time blindness testing
        """
        return {
            "scenario_name": "time_blindness_support",
            "user_behavior": {
                "consistently_underestimates": True,
                "loses_track_of_time": True,
                "needs_visual_time_cues": True
            },
            "expected_features": [
                "circular_time_display",
                "buffer_time_insertion",
                "time_tracking_reminders"
            ],
            "test_tasks": [
                {
                    "title": "Underestimated Task",
                    "estimated_duration": 30,
                    "actual_duration": 75,
                    "expected_accuracy": 0.4
                }
            ]
        }
    
    @staticmethod
    def create_task_paralysis_scenario() -> Dict[str, Any]:
        """
        Create scenario testing task paralysis solutions.
        
        Returns:
            Test scenario data for task paralysis testing
        """
        return {
            "scenario_name": "task_paralysis_solutions",
            "user_behavior": {
                "overwhelmed_by_choices": True,
                "needs_task_chunking": True,
                "benefits_from_random_selection": True
            },
            "expected_features": [
                "ai_task_chunking",
                "task_jar_selection",
                "decision_support"
            ],
            "test_tasks": [
                {
                    "title": "Overwhelming Project",
                    "estimated_duration": 240,
                    "complexity": "high",
                    "needs_chunking": True
                }
            ]
        }
    
    @staticmethod
    def create_hyperfocus_scenario() -> Dict[str, Any]:
        """
        Create scenario testing hyperfocus management.
        
        Returns:
            Test scenario data for hyperfocus testing
        """
        return {
            "scenario_name": "hyperfocus_management",
            "user_behavior": {
                "enters_hyperfocus": True,
                "ignores_breaks": True,
                "needs_gentle_interruptions": True
            },
            "expected_features": [
                "hyperfocus_detection",
                "gentle_break_reminders",
                "session_protection"
            ],
            "test_sessions": [
                {
                    "planned_duration": 60,
                    "actual_duration": 180,
                    "break_reminders_ignored": 3
                }
            ]
        }


def create_test_database_url(test_name: str) -> str:
    """
    Create unique test database URL for isolated testing.
    
    Args:
        test_name: Name of the test for database naming
        
    Returns:
        Unique database URL for the test
    """
    clean_name = "".join(c for c in test_name if c.isalnum() or c in "_-")
    return f"postgresql+asyncpg://test:test@localhost:5432/test_chronos_{clean_name}"


def assert_adhd_feature_enabled(
    feature_name: str,
    user_preferences: Dict[str, Any],
    expected_state: bool = True
) -> None:
    """
    Assert that ADHD-specific feature is properly configured.
    
    Args:
        feature_name: Name of the feature to check
        user_preferences: User preferences dictionary
        expected_state: Expected enabled state
    """
    feature_mapping = {
        "persistent_notifications": ["notification_preferences", "persistent"],
        "gentle_notifications": ["notification_preferences", "gentle"],
        "task_chunking": ["chunking_preferences", "auto_chunk_threshold"],
        "energy_matching": ["scheduling_preferences", "energy_matching"]
    }
    
    if feature_name not in feature_mapping:
        raise ValueError(f"Unknown ADHD feature: {feature_name}")
    
    path = feature_mapping[feature_name]
    value = user_preferences
    
    for key in path:
        if key not in value:
            assert not expected_state, f"ADHD feature {feature_name} not configured"
            return
        value = value[key]
    
    if isinstance(value, bool):
        assert value == expected_state, (
            f"ADHD feature {feature_name} should be {expected_state}"
        )
    else:
        # For non-boolean values, just check existence
        assert value is not None, f"ADHD feature {feature_name} not configured"
