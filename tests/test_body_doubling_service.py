"""
Tests for body doubling service functionality.

This module tests the ADHD-optimized virtual body doubling features
including session management, real-time collaboration, and focus synchronization.
"""

import pytest
from datetime import datetime, timedelta
from uuid import uuid4

from app.services.body_doubling_service import BodyDoublingService
from app.schemas.body_doubling import (
    BodyDoublingSessionCreate,
    BodyDoublingParticipantJoin,
    GroupFocusSessionCreate,
    SessionDiscoveryRequest,
    EncouragementMessage,
    ProgressShare,
)
from app.models.body_doubling import BodyDoublingSession, BodyDoublingParticipant
from app.core.exceptions import (
    SessionNotFoundError,
    SessionFullError,
    BodyDoublingError,
    PermissionDeniedError,
)


class TestBodyDoublingService:
    """Test cases for BodyDoublingService."""
    
    @pytest.mark.asyncio
    async def test_create_session(self, db_session, test_user):
        """Test creating a body doubling session."""
        service = BodyDoublingService(db_session)
        
        session_data = BodyDoublingSessionCreate(
            title="Study Session",
            description="Working on Python projects",
            session_type="study",
            max_participants=3,
            is_public=True,
            scheduled_start=datetime.utcnow() + timedelta(minutes=30),
            scheduled_duration=120
        )
        
        session = await service.create_session(test_user.id, session_data)
        
        assert session.title == "Study Session"
        assert session.host_user_id == test_user.id
        assert session.status == "waiting"
        assert session.max_participants == 3
        assert len(session.participants) == 1  # Host auto-joins
        
        # Verify host is automatically added as participant
        host_participant = session.participants[0]
        assert host_participant.user_id == test_user.id
        assert host_participant.status == "active"
    
    @pytest.mark.asyncio
    async def test_discover_sessions(self, db_session, test_user):
        """Test discovering available sessions."""
        service = BodyDoublingService(db_session)
        
        # Create test sessions
        session1_data = BodyDoublingSessionCreate(
            title="Study Session",
            session_type="study",
            is_public=True,
            max_participants=4
        )
        session1 = await service.create_session(test_user.id, session1_data)
        
        session2_data = BodyDoublingSessionCreate(
            title="Work Session",
            session_type="work",
            is_public=True,
            max_participants=2
        )
        session2 = await service.create_session(test_user.id, session2_data)
        
        # Test discovery
        discovery_request = SessionDiscoveryRequest(
            session_type="study",
            has_space=True,
            public_only=True
        )
        
        sessions, total_count = await service.discover_sessions(
            test_user.id, discovery_request
        )
        
        assert total_count == 1
        assert sessions[0].title == "Study Session"
        assert sessions[0].session_type == "study"
    
    @pytest.mark.asyncio
    async def test_join_session(self, db_session, test_user, test_user_2):
        """Test joining a body doubling session."""
        service = BodyDoublingService(db_session)
        
        # Create session
        session_data = BodyDoublingSessionCreate(
            title="Collaborative Work",
            max_participants=3,
            is_public=True
        )
        session = await service.create_session(test_user.id, session_data)
        
        # Join session
        join_data = BodyDoublingParticipantJoin(
            display_name="Study Buddy",
            share_progress=True,
            receive_encouragement=True
        )
        
        updated_session, participant = await service.join_session(
            session.id, test_user_2.id, join_data
        )
        
        assert participant.user_id == test_user_2.id
        assert participant.display_name == "Study Buddy"
        assert participant.status == "active"
        assert updated_session.get_current_participant_count() == 2
    
    @pytest.mark.asyncio
    async def test_join_full_session(self, db_session, test_user, test_user_2):
        """Test joining a session that's at capacity."""
        service = BodyDoublingService(db_session)
        
        # Create session with max 1 participant (host only)
        session_data = BodyDoublingSessionCreate(
            title="Solo Work",
            max_participants=1,
            is_public=True
        )
        session = await service.create_session(test_user.id, session_data)
        
        # Try to join full session
        join_data = BodyDoublingParticipantJoin()
        
        with pytest.raises(SessionFullError):
            await service.join_session(session.id, test_user_2.id, join_data)
    
    @pytest.mark.asyncio
    async def test_leave_session(self, db_session, test_user, test_user_2):
        """Test leaving a body doubling session."""
        service = BodyDoublingService(db_session)
        
        # Create and join session
        session_data = BodyDoublingSessionCreate(
            title="Temporary Work",
            max_participants=3
        )
        session = await service.create_session(test_user.id, session_data)
        
        join_data = BodyDoublingParticipantJoin()
        await service.join_session(session.id, test_user_2.id, join_data)
        
        # Leave session
        participant = await service.leave_session(
            session.id, test_user_2.id, "Need to take a break"
        )
        
        assert participant.status == "left"
        assert participant.left_at is not None
        assert participant.session_data.get("leave_reason") == "Need to take a break"
    
    @pytest.mark.asyncio
    async def test_start_group_focus_session(self, db_session, test_user):
        """Test starting a group focus session."""
        service = BodyDoublingService(db_session)
        
        # Create session
        session_data = BodyDoublingSessionCreate(
            title="Focus Time",
            max_participants=4
        )
        session = await service.create_session(test_user.id, session_data)
        
        # Start focus session
        focus_data = GroupFocusSessionCreate(
            focus_type="pomodoro",
            focus_duration=25,
            break_duration=5,
            total_cycles=4,
            scheduled_start=datetime.utcnow() + timedelta(minutes=5)
        )
        
        focus_session = await service.start_group_focus_session(
            session.id, test_user.id, focus_data
        )
        
        assert focus_session.body_doubling_session_id == session.id
        assert focus_session.focus_duration == 25
        assert focus_session.break_duration == 5
        assert focus_session.total_cycles == 4
        assert focus_session.status == "scheduled"
    
    @pytest.mark.asyncio
    async def test_send_encouragement(self, db_session, test_user, test_user_2):
        """Test sending encouragement messages."""
        service = BodyDoublingService(db_session)
        
        # Create session and add participants
        session_data = BodyDoublingSessionCreate(
            title="Support Group",
            max_participants=3
        )
        session = await service.create_session(test_user.id, session_data)
        
        join_data = BodyDoublingParticipantJoin(receive_encouragement=True)
        await service.join_session(session.id, test_user_2.id, join_data)
        
        # Send encouragement
        encouragement = EncouragementMessage(
            message="You're doing great! Keep it up!",
            message_type="encouragement",
            is_anonymous=False
        )
        
        message_data = await service.send_encouragement(
            session.id, test_user.id, encouragement
        )
        
        assert message_data["message"] == "You're doing great! Keep it up!"
        assert len(message_data["recipients"]) == 1  # Only test_user_2 receives it
        assert str(test_user_2.id) in message_data["recipients"]
    
    @pytest.mark.asyncio
    async def test_share_progress(self, db_session, test_user, test_user_2):
        """Test sharing progress updates."""
        service = BodyDoublingService(db_session)
        
        # Create session and add participants
        session_data = BodyDoublingSessionCreate(
            title="Progress Sharing",
            max_participants=3
        )
        session = await service.create_session(test_user.id, session_data)
        
        join_data = BodyDoublingParticipantJoin(share_progress=True)
        await service.join_session(session.id, test_user_2.id, join_data)
        
        # Share progress
        progress = ProgressShare(
            progress_type="task_completion",
            description="Finished the first chapter",
            completion_percentage=0.25,
            celebration_level="moderate"
        )
        
        progress_data = await service.share_progress(
            session.id, test_user_2.id, progress
        )
        
        assert progress_data["progress_type"] == "task_completion"
        assert progress_data["description"] == "Finished the first chapter"
        assert progress_data["completion_percentage"] == 0.25
        assert progress_data["user_id"] == str(test_user_2.id)
    
    @pytest.mark.asyncio
    async def test_get_session_stats(self, db_session, test_user):
        """Test getting user session statistics."""
        service = BodyDoublingService(db_session)
        
        # Create a few sessions
        for i in range(3):
            session_data = BodyDoublingSessionCreate(
                title=f"Session {i}",
                session_type="study" if i % 2 == 0 else "work"
            )
            await service.create_session(test_user.id, session_data)
        
        # Get stats
        stats = await service.get_session_stats(test_user.id)
        
        assert stats["total_sessions_hosted"] == 3
        assert stats["total_sessions_joined"] == 3  # Host auto-joins
        assert "study" in stats["favorite_session_types"]
        assert "collaboration_effectiveness" in stats
    
    @pytest.mark.asyncio
    async def test_session_capacity_enforcement(self, db_session, test_user):
        """Test that session capacity is properly enforced."""
        service = BodyDoublingService(db_session)
        
        # Create session with capacity for 2 (including host)
        session_data = BodyDoublingSessionCreate(
            title="Small Group",
            max_participants=2
        )
        session = await service.create_session(test_user.id, session_data)
        
        # Session should allow joining (1 participant, capacity 2)
        assert session.can_join()
        
        # Add one more participant to reach capacity
        join_data = BodyDoublingParticipantJoin()
        user_2_id = uuid4()
        
        # Mock user_2 exists (in real test, would create user)
        participant = await service._add_participant(
            session.id, user_2_id, join_data
        )
        
        # Refresh session to get updated participant count
        updated_session = await service._get_session_with_participants(session.id)
        
        # Session should now be at capacity
        assert not updated_session.can_join()
        assert updated_session.get_current_participant_count() == 2
