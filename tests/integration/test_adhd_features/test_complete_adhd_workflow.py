"""
Integration tests for complete ADHD user workflows.

Tests end-to-end ADHD user journeys including task creation, chunking,
scheduling, focus sessions, and completion with gamification.
"""

import pytest
from datetime import datetime, timedelta
from httpx import AsyncClient

from app.models.user import User
from app.models.task import Task, TaskStatus
from app.models.focus_session import FocusSession, SessionStatus
from app.models.time_block import TimeBlock, BlockStatus
from tests.factories import UserFactory


@pytest.mark.integration
@pytest.mark.adhd_workflow
class TestCompleteADHDWorkflow:
    """Test complete ADHD user workflows."""

    @pytest.fixture
    async def adhd_user_client(self, client: AsyncClient, db_session):
        """Create authenticated ADHD user client."""
        user = await UserFactory.create(
            db_session,
            adhd_diagnosed=True,
            preferences={
                "ai_chunking_enabled": True,
                "energy_tracking_enabled": True,
                "hyperfocus_protection": True,
                "gamification_enabled": True,
                "gentle_reminders": True
            }
        )
        
        # Authenticate user
        auth_response = await client.post("/api/v1/auth/login", json={
            "email": user.email,
            "password": "testpassword123"
        })
        
        token = auth_response.json()["access_token"]
        headers = {"Authorization": f"Bearer {token}"}
        
        return client, user, headers

    async def test_overwhelming_task_to_completion_workflow(self, adhd_user_client):
        """Test complete workflow from overwhelming task to successful completion."""
        client, user, headers = adhd_user_client
        
        # Step 1: User creates an overwhelming task
        task_response = await client.post(
            "/api/v1/tasks",
            headers=headers,
            json={
                "title": "Organize entire home office",
                "description": "Clean, organize, and set up productive workspace",
                "estimated_duration": 240,  # 4 hours - overwhelming!
                "energy_level": "high",
                "priority": "medium"
            }
        )
        
        assert task_response.status_code == 201
        task_data = task_response.json()
        task_id = task_data["id"]
        
        # Step 2: AI chunking breaks down the overwhelming task
        chunk_response = await client.post(
            f"/api/v1/tasks/{task_id}/chunk",
            headers=headers,
            json={
                "max_chunk_duration": 45,
                "user_energy_level": "high",
                "context": "home_office_organization"
            }
        )
        
        assert chunk_response.status_code == 200
        chunks_data = chunk_response.json()
        
        assert len(chunks_data["chunks"]) >= 3
        assert all(chunk["estimated_duration"] <= 45 for chunk in chunks_data["chunks"])
        
        # Step 3: Schedule first chunk in optimal time slot
        first_chunk = chunks_data["chunks"][0]
        
        schedule_response = await client.post(
            "/api/v1/time-blocks",
            headers=headers,
            json={
                "task_id": task_id,
                "title": first_chunk["title"],
                "start_time": (datetime.utcnow() + timedelta(hours=1)).isoformat(),
                "duration_minutes": first_chunk["estimated_duration"],
                "energy_level": "high",
                "block_type": "focus"
            }
        )
        
        assert schedule_response.status_code == 201
        time_block_data = schedule_response.json()
        
        # Step 4: Start focus session for the chunk
        focus_response = await client.post(
            "/api/v1/focus/sessions",
            headers=headers,
            json={
                "task_id": task_id,
                "time_block_id": time_block_data["id"],
                "session_type": "pomodoro",
                "planned_duration": first_chunk["estimated_duration"],
                "enable_hyperfocus_protection": True
            }
        )
        
        assert focus_response.status_code == 201
        session_data = focus_response.json()
        session_id = session_data["id"]
        
        # Step 5: Start the focus session
        start_response = await client.post(
            f"/api/v1/focus/sessions/{session_id}/start",
            headers=headers
        )
        
        assert start_response.status_code == 200
        
        # Step 6: Complete the focus session
        complete_response = await client.post(
            f"/api/v1/focus/sessions/{session_id}/complete",
            headers=headers,
            json={
                "completion_notes": "Successfully cleared desk surface",
                "energy_after": "medium",
                "satisfaction_rating": 4
            }
        )
        
        assert complete_response.status_code == 200
        completion_data = complete_response.json()
        
        # Verify gamification rewards
        assert "points_earned" in completion_data
        assert completion_data["points_earned"] > 0
        assert "celebration" in completion_data
        
        # Step 7: Mark chunk as completed
        chunk_complete_response = await client.patch(
            f"/api/v1/tasks/{task_id}",
            headers=headers,
            json={
                "status": "in_progress",
                "progress_notes": "Completed first chunk - desk cleared"
            }
        )
        
        assert chunk_complete_response.status_code == 200
        
        # Step 8: Get suggestions for next chunk
        next_chunk_response = await client.get(
            f"/api/v1/tasks/{task_id}/next-chunk-suggestions",
            headers=headers
        )
        
        assert next_chunk_response.status_code == 200
        suggestions = next_chunk_response.json()
        
        assert "recommended_chunk" in suggestions
        assert "optimal_time_slots" in suggestions
        assert "energy_considerations" in suggestions

    async def test_hyperfocus_protection_workflow(self, adhd_user_client):
        """Test hyperfocus protection during extended work sessions."""
        client, user, headers = adhd_user_client
        
        # Create engaging task that might trigger hyperfocus
        task_response = await client.post(
            "/api/v1/tasks",
            headers=headers,
            json={
                "title": "Design new app feature",
                "description": "Creative design work - high hyperfocus risk",
                "estimated_duration": 60,
                "energy_level": "high",
                "context_tags": ["creative", "computer"]
            }
        )
        
        task_id = task_response.json()["id"]
        
        # Start deep work session with hyperfocus protection
        session_response = await client.post(
            "/api/v1/focus/sessions",
            headers=headers,
            json={
                "task_id": task_id,
                "session_type": "deep_work",
                "planned_duration": 90,
                "enable_hyperfocus_protection": True,
                "hyperfocus_threshold": 75
            }
        )
        
        session_id = session_response.json()["id"]
        
        # Start session
        await client.post(f"/api/v1/focus/sessions/{session_id}/start", headers=headers)
        
        # Simulate time passing beyond hyperfocus threshold
        # (In real implementation, this would be handled by background tasks)
        
        # Check hyperfocus status
        status_response = await client.get(
            f"/api/v1/focus/sessions/{session_id}/hyperfocus-status",
            headers=headers
        )
        
        assert status_response.status_code == 200
        status_data = status_response.json()
        
        # Should detect potential hyperfocus
        if status_data["session_duration_minutes"] > 75:
            assert status_data["hyperfocus_detected"] is True
            assert "break_suggestion" in status_data
            assert status_data["break_suggestion"]["urgency"] in ["gentle", "moderate", "strong"]

    async def test_energy_crash_recovery_workflow(self, adhd_user_client):
        """Test workflow for recovering from energy crashes."""
        client, user, headers = adhd_user_client
        
        # User reports energy crash
        energy_response = await client.post(
            "/api/v1/user/energy-update",
            headers=headers,
            json={
                "current_energy": "very_low",
                "energy_trend": "declining",
                "context": "afternoon_crash",
                "mood": "frustrated"
            }
        )
        
        assert energy_response.status_code == 200
        
        # Get energy-appropriate task suggestions
        suggestions_response = await client.get(
            "/api/v1/tasks/energy-appropriate",
            headers=headers,
            params={"energy_level": "very_low", "max_duration": 15}
        )
        
        assert suggestions_response.status_code == 200
        suggestions = suggestions_response.json()
        
        assert len(suggestions["tasks"]) > 0
        assert all(task["energy_level"] == "low" for task in suggestions["tasks"])
        assert all(task["estimated_duration"] <= 15 for task in suggestions["tasks"])
        
        # Get dopamine menu for energy boost
        dopamine_response = await client.get(
            "/api/v1/motivation/dopamine-menu",
            headers=headers,
            params={
                "energy_level": "very_low",
                "available_time": 10,
                "context": "energy_boost"
            }
        )
        
        assert dopamine_response.status_code == 200
        dopamine_menu = dopamine_response.json()
        
        assert len(dopamine_menu["activities"]) > 0
        assert all(activity["energy_requirement"] == "low" for activity in dopamine_menu["activities"])
        
        # Complete a dopamine activity
        activity = dopamine_menu["activities"][0]
        completion_response = await client.post(
            "/api/v1/motivation/dopamine-menu/complete",
            headers=headers,
            json={
                "activity_id": activity["id"],
                "actual_duration": 5,
                "energy_before": "very_low",
                "energy_after": "low",
                "satisfaction_rating": 3
            }
        )
        
        assert completion_response.status_code == 200
        completion_data = completion_response.json()
        
        assert "points_earned" in completion_data
        assert "energy_boost_acknowledged" in completion_data

    async def test_task_paralysis_breakthrough_workflow(self, adhd_user_client):
        """Test workflow for overcoming task paralysis."""
        client, user, headers = adhd_user_client
        
        # User reports task paralysis
        paralysis_response = await client.post(
            "/api/v1/user/task-paralysis-report",
            headers=headers,
            json={
                "paralysis_type": "choice_overload",
                "duration_minutes": 30,
                "context": "too_many_options",
                "emotional_state": "overwhelmed"
            }
        )
        
        assert paralysis_response.status_code == 200
        
        # Get paralysis-breaking suggestions
        breakthrough_response = await client.get(
            "/api/v1/tasks/paralysis-breakthrough",
            headers=headers,
            params={"paralysis_type": "choice_overload"}
        )
        
        assert breakthrough_response.status_code == 200
        breakthrough_data = breakthrough_response.json()
        
        assert "simplified_choices" in breakthrough_data
        assert len(breakthrough_data["simplified_choices"]) <= 3  # Reduce choice overload
        assert "micro_task" in breakthrough_data  # Tiny first step
        assert breakthrough_data["micro_task"]["estimated_duration"] <= 5
        
        # Complete micro task to build momentum
        micro_task = breakthrough_data["micro_task"]
        micro_response = await client.post(
            "/api/v1/tasks/micro-completion",
            headers=headers,
            json={
                "micro_task_id": micro_task["id"],
                "completion_time_seconds": 120,
                "confidence_after": "slightly_better"
            }
        )
        
        assert micro_response.status_code == 200
        micro_completion = micro_response.json()
        
        assert "momentum_built" in micro_completion
        assert "next_suggested_action" in micro_completion

    async def test_social_accountability_workflow(self, adhd_user_client, db_session):
        """Test social accountability and body doubling workflow."""
        client, user, headers = adhd_user_client
        
        # Create accountability partner
        partner = await UserFactory.create(db_session, adhd_diagnosed=True)
        
        # Send accountability partner request
        partner_response = await client.post(
            "/api/v1/social/accountability-partners",
            headers=headers,
            json={
                "partner_email": partner.email,
                "message": "Let's support each other!",
                "shared_goals": ["daily_tasks", "focus_sessions"]
            }
        )
        
        assert partner_response.status_code == 201
        
        # Create body doubling session
        session_response = await client.post(
            "/api/v1/body-doubling/sessions",
            headers=headers,
            json={
                "title": "Morning Focus Session",
                "description": "Working on our respective projects together",
                "max_participants": 4,
                "session_type": "focus_group",
                "scheduled_start": (datetime.utcnow() + timedelta(hours=1)).isoformat(),
                "duration_minutes": 60
            }
        )
        
        assert session_response.status_code == 201
        session_data = session_response.json()
        
        # Join the session
        join_response = await client.post(
            f"/api/v1/body-doubling/sessions/{session_data['id']}/join",
            headers=headers,
            json={
                "share_progress": True,
                "current_task": "Organize project files"
            }
        )
        
        assert join_response.status_code == 200
        
        # Share progress during session
        progress_response = await client.post(
            f"/api/v1/body-doubling/sessions/{session_data['id']}/progress",
            headers=headers,
            json={
                "progress_update": "Completed file organization",
                "energy_level": "medium",
                "encouragement_for_others": "Great work everyone!"
            }
        )
        
        assert progress_response.status_code == 200

    async def test_weekly_review_and_adjustment_workflow(self, adhd_user_client):
        """Test weekly review and system adjustment workflow."""
        client, user, headers = adhd_user_client
        
        # Get weekly analytics
        analytics_response = await client.get(
            "/api/v1/analytics/weekly-review",
            headers=headers,
            params={"week_offset": 0}  # Current week
        )
        
        assert analytics_response.status_code == 200
        analytics = analytics_response.json()
        
        assert "task_completion_rate" in analytics
        assert "focus_session_success" in analytics
        assert "energy_patterns" in analytics
        assert "adhd_insights" in analytics
        
        # Get personalized recommendations
        recommendations_response = await client.get(
            "/api/v1/recommendations/weekly-adjustments",
            headers=headers
        )
        
        assert recommendations_response.status_code == 200
        recommendations = recommendations_response.json()
        
        assert "schedule_adjustments" in recommendations
        assert "energy_optimization" in recommendations
        assert "focus_improvements" in recommendations
        
        # Apply recommended adjustments
        adjustment_response = await client.post(
            "/api/v1/user/apply-recommendations",
            headers=headers,
            json={
                "selected_recommendations": [
                    recommendations["schedule_adjustments"][0]["id"],
                    recommendations["energy_optimization"][0]["id"]
                ],
                "custom_adjustments": {
                    "morning_focus_time": "09:00",
                    "break_frequency": "every_45_minutes"
                }
            }
        )
        
        assert adjustment_response.status_code == 200
        
        # Verify adjustments were applied
        profile_response = await client.get("/api/v1/user/profile", headers=headers)
        updated_profile = profile_response.json()
        
        assert "preferences" in updated_profile
        assert updated_profile["preferences"]["morning_focus_time"] == "09:00"
