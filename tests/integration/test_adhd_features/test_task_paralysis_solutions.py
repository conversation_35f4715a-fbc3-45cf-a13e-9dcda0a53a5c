"""
Integration tests for task paralysis solution features.

This module tests features designed to overcome task paralysis in ADHD users,
including AI task chunking, task jar selection, and decision support systems.
"""

import pytest
from datetime import datetime, timedelta
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession

from tests.factories import UserFactory, TaskFactory
from tests.utils import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ADHDTestAssertions


class TestTaskParalysisSolutions:
    """Test features designed to overcome task paralysis."""
    
    @pytest.mark.integration
    @pytest.mark.adhd
    async def test_ai_task_chunking_quality(
        self, 
        client: AsyncClient, 
        adhd_user_with_tasks: dict
    ):
        """Test AI task chunking produces appropriate subtasks."""
        
        user = adhd_user_with_tasks["user"]
        tasks = adhd_user_with_tasks["tasks"]
        
        # Find the large project task
        large_task = next(
            task for task in tasks 
            if task.title == "Large Project - Website Redesign"
        )
        
        # Create authentication token
        from app.services.auth_service import AuthService
        auth_service = AuthService()
        token = await auth_service.create_access_token({"sub": str(user.id)})
        
        # Request AI chunking
        chunk_request = {
            "chunk_size": "small",
            "context": "This is for a user with ADHD who needs very specific, actionable steps",
            "user_preferences": {
                "chunking_preferences": {
                    "default_size": "small",
                    "max_subtasks": 5,
                    "auto_chunk_threshold": 60
                }
            }
        }
        
        response = await client.post(
            f"/api/v1/tasks/{large_task.id}/chunk",
            json=chunk_request,
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert response.status_code == 200
        subtasks = response.json()
        
        # Verify chunking quality for ADHD users
        assert 3 <= len(subtasks) <= 7  # Appropriate number of chunks
        
        for subtask in subtasks:
            # Each subtask should be ADHD-friendly
            assert subtask["estimated_duration"] <= 45  # Small chunks
            assert len(subtask["title"]) > 10  # Descriptive titles
            assert subtask["description"]  # Has clear description
            assert subtask["energy_level"] in ["low", "medium", "high"]
            
            # Verify ADHD-specific optimizations
            ADHDTestAssertions.assert_task_duration_appropriate_for_adhd(
                type('Task', (), subtask)(),  # Mock task object
                max_duration=60
            )
        
        # Verify subtasks have clear progression
        titles = [subtask["title"] for subtask in subtasks]
        assert any("step" in title.lower() or "phase" in title.lower() for title in titles)
    
    @pytest.mark.integration
    @pytest.mark.adhd
    async def test_task_jar_decision_support(
        self, 
        client: AsyncClient, 
        adhd_user_with_tasks: dict
    ):
        """Test task jar feature for overcoming choice paralysis."""
        
        user = adhd_user_with_tasks["user"]
        
        # Create authentication token
        from app.services.auth_service import AuthService
        auth_service = AuthService()
        token = await auth_service.create_access_token({"sub": str(user.id)})
        
        # Request task jar selection
        response = await client.get(
            "/api/v1/tasks/jar?jar_size=5",
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert response.status_code == 200
        jar_tasks = response.json()
        
        # Verify jar selection for ADHD users
        assert len(jar_tasks) <= 5
        assert len(jar_tasks) >= 1
        
        # Verify tasks are suitable for random selection
        for task in jar_tasks:
            assert task["status"] == "pending"
            assert task.get("parent_task_id") is None  # Only parent tasks
            
            # Should be manageable tasks for ADHD users
            if task.get("estimated_duration"):
                assert task["estimated_duration"] <= 90  # Not overwhelming
        
        # Test jar with energy level filter
        energy_filtered_response = await client.get(
            "/api/v1/tasks/jar?jar_size=3&energy_level=low",
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert energy_filtered_response.status_code == 200
        filtered_jar = energy_filtered_response.json()
        
        # All tasks should match energy level
        for task in filtered_jar:
            assert task["energy_level"] == "low"
    
    @pytest.mark.integration
    @pytest.mark.adhd
    async def test_decision_fatigue_reduction_features(
        self, 
        client: AsyncClient, 
        adhd_user_with_tasks: dict
    ):
        """Test features that reduce decision fatigue."""
        
        user = adhd_user_with_tasks["user"]
        
        # Create authentication token
        from app.services.auth_service import AuthService
        auth_service = AuthService()
        token = await auth_service.create_access_token({"sub": str(user.id)})
        
        # Test smart task recommendations
        recommendations_response = await client.get(
            "/api/v1/tasks/recommendations",
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert recommendations_response.status_code == 200
        recommendations = recommendations_response.json()
        
        # Verify recommendations reduce decision burden
        assert "quick_wins" in recommendations  # Easy tasks to build momentum
        assert "energy_matched" in recommendations  # Tasks matching current energy
        assert "context_appropriate" in recommendations  # Tasks for current context
        
        # Quick wins should be very manageable
        quick_wins = recommendations["quick_wins"]
        for task in quick_wins:
            assert task["estimated_duration"] <= 30  # Very quick
            assert task["energy_level"] in ["low", "medium"]  # Not overwhelming
        
        # Test "next best action" feature
        next_action_response = await client.get(
            "/api/v1/tasks/next-action",
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert next_action_response.status_code == 200
        next_action = next_action_response.json()
        
        # Should provide single, clear next action
        assert "task" in next_action
        assert "reasoning" in next_action
        assert "confidence_score" in next_action
        
        # Reasoning should be ADHD-friendly
        reasoning = next_action["reasoning"]
        assert len(reasoning) > 0
        assert any(
            keyword in reasoning.lower() 
            for keyword in ["energy", "context", "momentum", "quick", "easy"]
        )
    
    @pytest.mark.integration
    @pytest.mark.adhd
    async def test_task_breakdown_progressive_disclosure(
        self, 
        client: AsyncClient, 
        adhd_user_with_tasks: dict
    ):
        """Test progressive disclosure of task complexity."""
        
        user = adhd_user_with_tasks["user"]
        
        # Create authentication token
        from app.services.auth_service import AuthService
        auth_service = AuthService()
        token = await auth_service.create_access_token({"sub": str(user.id)})
        
        # Create a complex task
        complex_task_data = {
            "title": "Complex Multi-Phase Project",
            "description": "A project with multiple interconnected phases",
            "estimated_duration": 300,  # 5 hours - very complex
            "energy_level": "high",
            "priority": "high",
            "context_tags": ["computer", "planning", "complex"]
        }
        
        create_response = await client.post(
            "/api/v1/tasks/",
            json=complex_task_data,
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert create_response.status_code == 201
        complex_task = create_response.json()
        task_id = complex_task["id"]
        
        # Request progressive breakdown
        breakdown_response = await client.post(
            f"/api/v1/tasks/{task_id}/progressive-breakdown",
            json={
                "disclosure_level": "overview",  # Start with high-level overview
                "adhd_optimized": True
            },
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert breakdown_response.status_code == 200
        overview = breakdown_response.json()
        
        # Overview should be non-overwhelming
        assert "phases" in overview
        assert len(overview["phases"]) <= 5  # Not too many phases
        
        for phase in overview["phases"]:
            assert "title" in phase
            assert "estimated_duration" in phase
            assert phase["estimated_duration"] <= 90  # Each phase manageable
        
        # Request detailed breakdown for first phase
        first_phase_id = overview["phases"][0]["id"]
        detail_response = await client.post(
            f"/api/v1/tasks/{first_phase_id}/progressive-breakdown",
            json={
                "disclosure_level": "detailed",
                "adhd_optimized": True
            },
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert detail_response.status_code == 200
        details = detail_response.json()
        
        # Detailed view should show actionable steps
        assert "steps" in details
        for step in details["steps"]:
            assert step["estimated_duration"] <= 45  # ADHD-friendly steps
            assert "action_verb" in step["title"].lower()  # Clear action
    
    @pytest.mark.integration
    @pytest.mark.adhd
    async def test_momentum_building_features(
        self, 
        client: AsyncClient, 
        adhd_user_with_tasks: dict
    ):
        """Test features that help build and maintain momentum."""
        
        user = adhd_user_with_tasks["user"]
        
        # Create authentication token
        from app.services.auth_service import AuthService
        auth_service = AuthService()
        token = await auth_service.create_access_token({"sub": str(user.id)})
        
        # Test momentum starter suggestions
        momentum_response = await client.get(
            "/api/v1/tasks/momentum-starters",
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert momentum_response.status_code == 200
        starters = momentum_response.json()
        
        # Should provide easy wins to build momentum
        assert "easy_wins" in starters
        assert "two_minute_tasks" in starters
        assert "preparation_tasks" in starters
        
        # Easy wins should be very quick
        easy_wins = starters["easy_wins"]
        for task in easy_wins:
            assert task["estimated_duration"] <= 15  # Very quick wins
            assert task["energy_level"] == "low"  # Low barrier to entry
        
        # Two-minute tasks for immediate action
        two_minute_tasks = starters["two_minute_tasks"]
        for task in two_minute_tasks:
            assert task["estimated_duration"] <= 5  # Truly quick
        
        # Test momentum tracking
        # Complete a momentum starter
        if easy_wins:
            starter_task = easy_wins[0]
            
            # Mark as completed
            complete_response = await client.patch(
                f"/api/v1/tasks/{starter_task['id']}",
                json={"status": "completed"},
                headers={"Authorization": f"Bearer {token}"}
            )
            assert complete_response.status_code == 200
            
            # Check momentum score update
            momentum_score_response = await client.get(
                f"/api/v1/users/{user.id}/momentum-score",
                headers={"Authorization": f"Bearer {token}"}
            )
            
            assert momentum_score_response.status_code == 200
            momentum_data = momentum_score_response.json()
            
            assert "current_score" in momentum_data
            assert "trend" in momentum_data
            assert momentum_data["current_score"] > 0  # Should increase after completion
    
    @pytest.mark.integration
    @pytest.mark.adhd
    async def test_context_switching_minimization(
        self, 
        client: AsyncClient, 
        adhd_user_with_tasks: dict
    ):
        """Test features that minimize context switching."""
        
        user = adhd_user_with_tasks["user"]
        
        # Create authentication token
        from app.services.auth_service import AuthService
        auth_service = AuthService()
        token = await auth_service.create_access_token({"sub": str(user.id)})
        
        # Request context-grouped tasks
        context_response = await client.get(
            "/api/v1/tasks/grouped-by-context",
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert context_response.status_code == 200
        grouped_tasks = context_response.json()
        
        # Should group tasks by context to minimize switching
        assert "computer" in grouped_tasks or "home" in grouped_tasks
        
        for context, tasks in grouped_tasks.items():
            # All tasks in group should share context
            for task in tasks:
                assert context in task["context_tags"]
        
        # Test batch scheduling to minimize context switches
        batch_request = {
            "preferred_contexts": ["computer", "home"],
            "max_context_switches": 2,  # Limit switches for ADHD
            "session_duration": 120  # 2-hour focused session
        }
        
        batch_response = await client.post(
            "/api/v1/scheduling/batch-schedule",
            json=batch_request,
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert batch_response.status_code == 200
        batch_schedule = batch_response.json()
        
        # Verify context switching is minimized
        assert "scheduled_tasks" in batch_schedule
        assert "context_switches" in batch_schedule
        assert batch_schedule["context_switches"] <= batch_request["max_context_switches"]
        
        # Verify ADHD-friendly scheduling
        scheduled_tasks = batch_schedule["scheduled_tasks"]
        total_duration = sum(task["estimated_duration"] for task in scheduled_tasks)
        assert total_duration <= batch_request["session_duration"]
        
        # Should include buffer time between context switches
        if batch_schedule["context_switches"] > 0:
            assert "buffer_time" in batch_schedule
            assert batch_schedule["buffer_time"] >= 10  # ADHD needs transition time
