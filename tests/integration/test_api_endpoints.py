"""
Integration tests for API endpoints with ADHD-specific functionality.

This module tests all API endpoints to ensure they work correctly
with ADHD-focused features and provide appropriate responses.
"""

import pytest
from datetime import datetime, timedelta
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession

from tests.factories import UserFactory, TaskFactory


class TestTaskAPIEndpoints:
    """Test suite for task management API endpoints."""
    
    @pytest.mark.integration
    async def test_create_task_endpoint(
        self, 
        client: AsyncClient, 
        authenticated_user: dict
    ):
        """Test task creation endpoint with ADHD features."""
        
        token = authenticated_user["token"]
        
        task_data = {
            "title": "ADHD-Optimized Task",
            "description": "A task designed for ADHD users",
            "priority": "medium",
            "energy_level": "low",
            "estimated_duration": 30,
            "context_tags": ["home", "computer"],
            "due_date": (datetime.utcnow() + timedelta(days=1)).isoformat()
        }
        
        response = await client.post(
            "/api/v1/tasks/",
            json=task_data,
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert response.status_code == 201
        created_task = response.json()
        
        # Verify ADHD-specific fields
        assert created_task["title"] == task_data["title"]
        assert created_task["energy_level"] == task_data["energy_level"]
        assert created_task["estimated_duration"] == task_data["estimated_duration"]
        assert created_task["context_tags"] == task_data["context_tags"]
        assert created_task["status"] == "pending"
        
        # Verify timestamps
        assert "created_at" in created_task
        assert "updated_at" in created_task
        assert "id" in created_task
    
    @pytest.mark.integration
    async def test_get_tasks_with_filters(
        self, 
        client: AsyncClient, 
        adhd_user_with_tasks: dict
    ):
        """Test task retrieval with ADHD-specific filters."""
        
        user = adhd_user_with_tasks["user"]
        
        # Create authentication token
        from app.services.auth_service import AuthService
        auth_service = AuthService()
        token = await auth_service.create_access_token({"sub": str(user.id)})
        
        # Test energy level filtering
        response = await client.get(
            "/api/v1/tasks/?energy_level=low",
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert response.status_code == 200
        tasks = response.json()
        
        # All returned tasks should match energy level
        for task in tasks:
            assert task["energy_level"] == "low"
        
        # Test duration filtering
        duration_response = await client.get(
            "/api/v1/tasks/?max_duration=60",
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert duration_response.status_code == 200
        duration_tasks = duration_response.json()
        
        # All tasks should be under duration limit
        for task in duration_tasks:
            if task.get("estimated_duration"):
                assert task["estimated_duration"] <= 60
        
        # Test context tag filtering
        context_response = await client.get(
            "/api/v1/tasks/?context_tags=computer",
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert context_response.status_code == 200
        context_tasks = context_response.json()
        
        # All tasks should have the context tag
        for task in context_tasks:
            if task.get("context_tags"):
                assert "computer" in task["context_tags"]
    
    @pytest.mark.integration
    async def test_update_task_endpoint(
        self, 
        client: AsyncClient, 
        authenticated_user: dict
    ):
        """Test task update endpoint with status tracking."""
        
        token = authenticated_user["token"]
        
        # Create a task first
        task_data = {
            "title": "Task to Update",
            "energy_level": "medium",
            "estimated_duration": 45
        }
        
        create_response = await client.post(
            "/api/v1/tasks/",
            json=task_data,
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert create_response.status_code == 201
        task = create_response.json()
        task_id = task["id"]
        
        # Update the task
        update_data = {
            "status": "in_progress",
            "actual_duration": 30  # Started working on it
        }
        
        update_response = await client.patch(
            f"/api/v1/tasks/{task_id}",
            json=update_data,
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert update_response.status_code == 200
        updated_task = update_response.json()
        
        # Verify updates
        assert updated_task["status"] == "in_progress"
        assert updated_task["actual_duration"] == 30
        assert updated_task["updated_at"] != task["updated_at"]
    
    @pytest.mark.integration
    async def test_delete_task_soft_delete(
        self, 
        client: AsyncClient, 
        authenticated_user: dict
    ):
        """Test task soft deletion for ADHD users."""
        
        token = authenticated_user["token"]
        
        # Create a task
        task_data = {"title": "Task to Delete"}
        
        create_response = await client.post(
            "/api/v1/tasks/",
            json=task_data,
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert create_response.status_code == 201
        task = create_response.json()
        task_id = task["id"]
        
        # Soft delete the task
        delete_response = await client.delete(
            f"/api/v1/tasks/{task_id}",
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert delete_response.status_code == 200
        
        # Task should not appear in regular listing
        list_response = await client.get(
            "/api/v1/tasks/",
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert list_response.status_code == 200
        tasks = list_response.json()
        
        # Task should not be in active tasks
        task_ids = [t["id"] for t in tasks]
        assert task_id not in task_ids
        
        # But should be retrievable from deleted tasks
        deleted_response = await client.get(
            "/api/v1/tasks/deleted",
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert deleted_response.status_code == 200
        deleted_tasks = deleted_response.json()
        
        deleted_task_ids = [t["id"] for t in deleted_tasks]
        assert task_id in deleted_task_ids


class TestAIChunkingAPIEndpoints:
    """Test suite for AI chunking API endpoints."""
    
    @pytest.mark.integration
    async def test_chunk_task_endpoint(
        self, 
        client: AsyncClient, 
        authenticated_user: dict
    ):
        """Test AI task chunking endpoint."""
        
        token = authenticated_user["token"]
        
        # Create a large task
        large_task_data = {
            "title": "Large Project for Chunking",
            "description": "A complex project that needs to be broken down",
            "estimated_duration": 180,  # 3 hours
            "energy_level": "high",
            "priority": "medium"
        }
        
        create_response = await client.post(
            "/api/v1/tasks/",
            json=large_task_data,
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert create_response.status_code == 201
        task = create_response.json()
        task_id = task["id"]
        
        # Request chunking
        chunk_request = {
            "chunk_size": "small",
            "context": "User has ADHD and needs small, manageable steps"
        }
        
        chunk_response = await client.post(
            f"/api/v1/tasks/{task_id}/chunk",
            json=chunk_request,
            headers={"Authorization": f"Bearer {token}"}
        )
        
        # Note: This might return 503 if no AI service is configured
        if chunk_response.status_code == 503:
            pytest.skip("AI service not available for testing")
        
        assert chunk_response.status_code == 200
        subtasks = chunk_response.json()
        
        # Verify chunking results
        assert isinstance(subtasks, list)
        assert len(subtasks) > 0
        
        for subtask in subtasks:
            assert "title" in subtask
            assert "description" in subtask
            assert "estimated_duration" in subtask
            assert subtask["estimated_duration"] <= 60  # ADHD-friendly chunks
    
    @pytest.mark.integration
    async def test_task_jar_endpoint(
        self, 
        client: AsyncClient, 
        adhd_user_with_tasks: dict
    ):
        """Test task jar endpoint for decision fatigue reduction."""
        
        user = adhd_user_with_tasks["user"]
        
        # Create authentication token
        from app.services.auth_service import AuthService
        auth_service = AuthService()
        token = await auth_service.create_access_token({"sub": str(user.id)})
        
        # Request task jar
        response = await client.get(
            "/api/v1/tasks/jar?jar_size=3",
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert response.status_code == 200
        jar_tasks = response.json()
        
        # Verify jar functionality
        assert isinstance(jar_tasks, list)
        assert len(jar_tasks) <= 3
        
        # All tasks should be actionable
        for task in jar_tasks:
            assert task["status"] == "pending"
            assert "title" in task
            assert "id" in task


class TestUserAPIEndpoints:
    """Test suite for user-related API endpoints."""
    
    @pytest.mark.integration
    async def test_user_profile_endpoint(
        self, 
        client: AsyncClient, 
        authenticated_user: dict
    ):
        """Test user profile endpoint with ADHD preferences."""
        
        token = authenticated_user["token"]
        user_id = authenticated_user["user"].id
        
        # Get user profile
        response = await client.get(
            f"/api/v1/users/{user_id}",
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert response.status_code == 200
        profile = response.json()
        
        # Verify ADHD-specific fields
        assert "adhd_diagnosed" in profile
        assert "preferences" in profile
        
        if profile["adhd_diagnosed"]:
            preferences = profile["preferences"]
            assert "energy_patterns" in preferences
            assert "notification_preferences" in preferences
            assert "chunking_preferences" in preferences
    
    @pytest.mark.integration
    async def test_update_user_preferences(
        self, 
        client: AsyncClient, 
        authenticated_user: dict
    ):
        """Test updating user ADHD preferences."""
        
        token = authenticated_user["token"]
        user_id = authenticated_user["user"].id
        
        # Update preferences
        new_preferences = {
            "energy_patterns": {
                "morning": "high",
                "afternoon": "low",
                "evening": "medium"
            },
            "notification_preferences": {
                "persistent": True,
                "gentle": True,
                "frequency": "high"
            }
        }
        
        update_response = await client.patch(
            f"/api/v1/users/{user_id}",
            json={"preferences": new_preferences},
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert update_response.status_code == 200
        updated_user = update_response.json()
        
        # Verify preferences were updated
        assert updated_user["preferences"]["energy_patterns"]["morning"] == "high"
        assert updated_user["preferences"]["notification_preferences"]["persistent"] is True


class TestHealthCheckEndpoints:
    """Test suite for health check and system status endpoints."""
    
    @pytest.mark.integration
    async def test_health_check_endpoint(self, client: AsyncClient):
        """Test basic health check endpoint."""
        
        response = await client.get("/health")
        
        assert response.status_code == 200
        health_data = response.json()
        
        assert "status" in health_data
        assert health_data["status"] == "healthy"
    
    @pytest.mark.integration
    async def test_adhd_features_status_endpoint(self, client: AsyncClient):
        """Test ADHD features status endpoint."""
        
        response = await client.get("/api/v1/features/adhd-status")
        
        assert response.status_code == 200
        features = response.json()
        
        # Verify ADHD feature availability
        assert "task_management" in features
        assert "ai_chunking" in features
        assert "adaptive_filtering" in features
        assert "task_jar" in features
        
        # Each feature should have availability status
        for feature_name, feature_info in features.items():
            assert "available" in feature_info
            assert isinstance(feature_info["available"], bool)


class TestErrorHandling:
    """Test suite for API error handling."""
    
    @pytest.mark.integration
    async def test_unauthorized_access(self, client: AsyncClient):
        """Test unauthorized access to protected endpoints."""
        
        # Try to access tasks without authentication
        response = await client.get("/api/v1/tasks/")
        
        assert response.status_code == 401
        error_data = response.json()
        assert "detail" in error_data
    
    @pytest.mark.integration
    async def test_task_not_found(
        self, 
        client: AsyncClient, 
        authenticated_user: dict
    ):
        """Test handling of non-existent task requests."""
        
        token = authenticated_user["token"]
        fake_task_id = "00000000-0000-0000-0000-000000000000"
        
        response = await client.get(
            f"/api/v1/tasks/{fake_task_id}",
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert response.status_code == 404
        error_data = response.json()
        assert "detail" in error_data
    
    @pytest.mark.integration
    async def test_invalid_task_data(
        self, 
        client: AsyncClient, 
        authenticated_user: dict
    ):
        """Test validation of invalid task data."""
        
        token = authenticated_user["token"]
        
        # Send invalid task data
        invalid_task_data = {
            "title": "",  # Empty title should be invalid
            "energy_level": "invalid_level",  # Invalid energy level
            "estimated_duration": -10  # Negative duration
        }
        
        response = await client.post(
            "/api/v1/tasks/",
            json=invalid_task_data,
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert response.status_code == 422  # Validation error
        error_data = response.json()
        assert "detail" in error_data
