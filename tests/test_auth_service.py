"""
Tests for authentication service functionality.

This module tests the ADHD-optimized authentication features
including user registration, login, password management, and token handling.
"""

import pytest
from datetime import datetime, timedelta
from uuid import uuid4

from app.services.auth_service import AuthService
from app.schemas.auth import (
    UserReg<PERSON>,
    User<PERSON>ogin,
    Pass<PERSON><PERSON><PERSON><PERSON>,
    PasswordReset,
)
from app.core.security import security_manager, hash_password
from app.models.user import User
from app.core.exceptions import (
    AuthenticationError,
    ValidationError,
    UserNotFoundError,
)


class TestAuthService:
    """Test cases for AuthService."""
    
    @pytest.mark.asyncio
    async def test_register_user(self, db_session):
        """Test user registration with ADHD-friendly process."""
        auth_service = AuthService(db_session)
        
        registration_data = UserRegister(
            email="<EMAIL>",
            password="securepassword123",
            confirm_password="securepassword123",
            first_name="Test",
            last_name="User",
            adhd_diagnosis=True,
            preferred_chunk_size="medium",
            default_energy_level="medium"
        )
        
        user = await auth_service.register_user(registration_data)
        
        assert user.email == "<EMAIL>"
        assert user.first_name == "Test"
        assert user.last_name == "User"
        assert user.adhd_diagnosis == True
        assert user.preferred_chunk_size == "medium"
        assert user.default_energy_level == "medium"
        assert user.is_active == True
        assert user.is_verified == False  # Requires email verification
        assert user.hashed_password != "securepassword123"  # Should be hashed
    
    @pytest.mark.asyncio
    async def test_register_duplicate_email(self, db_session):
        """Test registration with duplicate email address."""
        auth_service = AuthService(db_session)
        
        # Create first user
        registration_data = UserRegister(
            email="<EMAIL>",
            password="password123",
            confirm_password="password123",
            first_name="First",
        )
        
        await auth_service.register_user(registration_data)
        
        # Try to register with same email
        duplicate_registration = UserRegister(
            email="<EMAIL>",
            password="different123",
            confirm_password="different123",
            first_name="Second",
        )
        
        with pytest.raises(ValidationError, match="Email address is already registered"):
            await auth_service.register_user(duplicate_registration)
    
    @pytest.mark.asyncio
    async def test_authenticate_user_success(self, db_session):
        """Test successful user authentication."""
        auth_service = AuthService(db_session)
        
        # Register user first
        registration_data = UserRegister(
            email="<EMAIL>",
            password="testpassword123",
            confirm_password="testpassword123",
            first_name="Auth",
        )
        
        user = await auth_service.register_user(registration_data)
        
        # Authenticate user
        credentials = UserLogin(
            email="<EMAIL>",
            password="testpassword123",
            remember_me=False
        )
        
        authenticated_user, tokens = await auth_service.authenticate_user(credentials)
        
        assert authenticated_user.id == user.id
        assert authenticated_user.email == "<EMAIL>"
        assert tokens.access_token is not None
        assert tokens.refresh_token is not None
        assert tokens.token_type == "bearer"
        assert tokens.expires_in > 0
        assert authenticated_user.last_login is not None
    
    @pytest.mark.asyncio
    async def test_authenticate_user_remember_me(self, db_session):
        """Test authentication with remember me option."""
        auth_service = AuthService(db_session)
        
        # Register user
        registration_data = UserRegister(
            email="<EMAIL>",
            password="testpassword123",
            confirm_password="testpassword123",
            first_name="Remember",
        )
        
        await auth_service.register_user(registration_data)
        
        # Authenticate with remember me
        credentials = UserLogin(
            email="<EMAIL>",
            password="testpassword123",
            remember_me=True
        )
        
        user, tokens = await auth_service.authenticate_user(credentials)
        
        assert tokens.access_token is not None
        assert tokens.refresh_token is not None
        # Verify refresh token has extended expiry (would need to decode to check)
    
    @pytest.mark.asyncio
    async def test_authenticate_invalid_email(self, db_session):
        """Test authentication with invalid email."""
        auth_service = AuthService(db_session)
        
        credentials = UserLogin(
            email="<EMAIL>",
            password="anypassword",
            remember_me=False
        )
        
        with pytest.raises(AuthenticationError, match="Invalid email or password"):
            await auth_service.authenticate_user(credentials)
    
    @pytest.mark.asyncio
    async def test_authenticate_invalid_password(self, db_session):
        """Test authentication with invalid password."""
        auth_service = AuthService(db_session)
        
        # Register user
        registration_data = UserRegister(
            email="<EMAIL>",
            password="correctpassword",
            confirm_password="correctpassword",
            first_name="Wrong",
        )
        
        await auth_service.register_user(registration_data)
        
        # Try with wrong password
        credentials = UserLogin(
            email="<EMAIL>",
            password="wrongpassword",
            remember_me=False
        )
        
        with pytest.raises(AuthenticationError, match="Invalid email or password"):
            await auth_service.authenticate_user(credentials)
    
    @pytest.mark.asyncio
    async def test_refresh_access_token(self, db_session):
        """Test refreshing access token."""
        auth_service = AuthService(db_session)
        
        # Register and authenticate user
        registration_data = UserRegister(
            email="<EMAIL>",
            password="testpassword123",
            confirm_password="testpassword123",
            first_name="Refresh",
        )
        
        user = await auth_service.register_user(registration_data)
        
        credentials = UserLogin(
            email="<EMAIL>",
            password="testpassword123"
        )
        
        _, initial_tokens = await auth_service.authenticate_user(credentials)
        
        # Refresh token
        new_tokens = await auth_service.refresh_access_token(initial_tokens.refresh_token)
        
        assert new_tokens.access_token != initial_tokens.access_token
        assert new_tokens.refresh_token is not None
        assert new_tokens.user.email == "<EMAIL>"
    
    @pytest.mark.asyncio
    async def test_refresh_invalid_token(self, db_session):
        """Test refreshing with invalid token."""
        auth_service = AuthService(db_session)
        
        with pytest.raises(AuthenticationError, match="Invalid refresh token"):
            await auth_service.refresh_access_token("invalid_token")
    
    @pytest.mark.asyncio
    async def test_change_password(self, db_session):
        """Test changing user password."""
        auth_service = AuthService(db_session)
        
        # Register user
        registration_data = UserRegister(
            email="<EMAIL>",
            password="oldpassword123",
            confirm_password="oldpassword123",
            first_name="Change",
        )
        
        user = await auth_service.register_user(registration_data)
        old_hash = user.hashed_password
        
        # Change password
        password_change = PasswordChange(
            current_password="oldpassword123",
            new_password="newpassword456",
            confirm_password="newpassword456"
        )
        
        updated_user = await auth_service.change_password(user.id, password_change)
        
        assert updated_user.hashed_password != old_hash
        assert updated_user.updated_at is not None
        
        # Verify old password no longer works
        credentials = UserLogin(
            email="<EMAIL>",
            password="oldpassword123"
        )
        
        with pytest.raises(AuthenticationError):
            await auth_service.authenticate_user(credentials)
        
        # Verify new password works
        new_credentials = UserLogin(
            email="<EMAIL>",
            password="newpassword456"
        )
        
        user, tokens = await auth_service.authenticate_user(new_credentials)
        assert user.email == "<EMAIL>"
    
    @pytest.mark.asyncio
    async def test_change_password_wrong_current(self, db_session):
        """Test changing password with wrong current password."""
        auth_service = AuthService(db_session)
        
        # Register user
        registration_data = UserRegister(
            email="<EMAIL>",
            password="correctpassword",
            confirm_password="correctpassword",
            first_name="Wrong",
        )
        
        user = await auth_service.register_user(registration_data)
        
        # Try to change with wrong current password
        password_change = PasswordChange(
            current_password="wrongpassword",
            new_password="newpassword456",
            confirm_password="newpassword456"
        )
        
        with pytest.raises(AuthenticationError, match="Current password is incorrect"):
            await auth_service.change_password(user.id, password_change)
    
    @pytest.mark.asyncio
    async def test_initiate_password_reset(self, db_session):
        """Test initiating password reset."""
        auth_service = AuthService(db_session)
        
        # Register user
        registration_data = UserRegister(
            email="<EMAIL>",
            password="testpassword123",
            confirm_password="testpassword123",
            first_name="Reset",
        )
        
        user = await auth_service.register_user(registration_data)
        
        # Initiate reset
        reset_token = await auth_service.initiate_password_reset("<EMAIL>")
        
        assert reset_token is not None
        
        # Refresh user to get updated token
        await db_session.refresh(user)
        assert user.reset_token == reset_token
        assert user.reset_token_expires is not None
        assert user.reset_token_expires > datetime.utcnow()
    
    @pytest.mark.asyncio
    async def test_initiate_password_reset_nonexistent_email(self, db_session):
        """Test password reset for non-existent email."""
        auth_service = AuthService(db_session)
        
        # Should return None but not raise error (security)
        reset_token = await auth_service.initiate_password_reset("<EMAIL>")
        assert reset_token is None
    
    @pytest.mark.asyncio
    async def test_reset_password(self, db_session):
        """Test completing password reset."""
        auth_service = AuthService(db_session)
        
        # Register user and initiate reset
        registration_data = UserRegister(
            email="<EMAIL>",
            password="oldpassword123",
            confirm_password="oldpassword123",
            first_name="Complete",
        )
        
        user = await auth_service.register_user(registration_data)
        reset_token = await auth_service.initiate_password_reset("<EMAIL>")
        
        # Complete reset
        reset_data = PasswordReset(
            token=reset_token,
            new_password="newpassword456",
            confirm_password="newpassword456"
        )
        
        updated_user = await auth_service.reset_password(reset_data)
        
        assert updated_user.reset_token is None
        assert updated_user.reset_token_expires is None
        
        # Verify new password works
        credentials = UserLogin(
            email="<EMAIL>",
            password="newpassword456"
        )
        
        user, tokens = await auth_service.authenticate_user(credentials)
        assert user.email == "<EMAIL>"
    
    @pytest.mark.asyncio
    async def test_reset_password_invalid_token(self, db_session):
        """Test password reset with invalid token."""
        auth_service = AuthService(db_session)
        
        reset_data = PasswordReset(
            token="invalid_token",
            new_password="newpassword456",
            confirm_password="newpassword456"
        )
        
        with pytest.raises(AuthenticationError, match="Invalid reset token"):
            await auth_service.reset_password(reset_data)
