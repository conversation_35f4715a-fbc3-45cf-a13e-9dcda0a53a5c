"""
Step definitions for ADHD focus management BDD tests.

Implements <PERSON><PERSON>kin scenarios for testing ADHD-specific focus features
including hyperfocus protection, energy management, and gentle interruptions.
"""

import asyncio
from datetime import datetime, timedelta
from behave import given, when, then, step
from httpx import AsyncClient
import pytest

from app.main import app
from app.models.user import User
from app.models.task import Task
from app.models.focus_session import FocusSession, SessionStatus
from tests.factories import UserFactory, TaskFactory, FocusSessionFactory


@given('I am an authenticated ADHD user')
async def step_authenticated_adhd_user(context):
    """Create and authenticate an ADHD user."""
    context.user = await UserFactory.create(
        context.db_session,
        adhd_diagnosed=True,
        preferences={
            "hyperfocus_protection": True,
            "energy_tracking_enabled": True,
            "gentle_reminders": True,
            "gamification_enabled": True
        }
    )
    
    # Create authenticated client
    async with Async<PERSON>lient(app=app, base_url="http://test") as client:
        auth_response = await client.post("/api/v1/auth/login", json={
            "email": context.user.email,
            "password": "testpassword123"
        })
        
        token = auth_response.json()["access_token"]
        context.headers = {"Authorization": f"Bearer {token}"}
        context.client = client


@given('I have hyperfocus protection enabled')
async def step_hyperfocus_protection_enabled(context):
    """Ensure hyperfocus protection is enabled for the user."""
    assert context.user.preferences.get("hyperfocus_protection") is True


@given('I have energy tracking enabled')
async def step_energy_tracking_enabled(context):
    """Ensure energy tracking is enabled for the user."""
    assert context.user.preferences.get("energy_tracking_enabled") is True


@given('I have a task "{task_title}"')
async def step_create_task(context, task_title):
    """Create a task with the given title."""
    context.task = await TaskFactory.create(
        context.db_session,
        user_id=context.user.id,
        title=task_title,
        energy_level="high",
        estimated_duration=90
    )


@given('the task requires "{energy_level}" energy level')
async def step_task_energy_level(context, energy_level):
    """Set the energy level requirement for the task."""
    context.task.energy_level = energy_level
    await context.db_session.commit()


@when('I start a deep work focus session')
async def step_start_deep_work_session(context):
    """Start a deep work focus session."""
    response = await context.client.post(
        "/api/v1/focus/sessions",
        headers=context.headers,
        json={
            "task_id": str(context.task.id),
            "session_type": "deep_work",
            "planned_duration": 90
        }
    )
    
    assert response.status_code == 201
    context.session_data = response.json()
    context.session_id = context.session_data["id"]


@when('I set the planned duration to {duration:d} minutes')
async def step_set_planned_duration(context, duration):
    """Set the planned duration for the session."""
    context.planned_duration = duration


@when('I enable hyperfocus protection with threshold {threshold:d} minutes')
async def step_enable_hyperfocus_protection(context, threshold):
    """Enable hyperfocus protection with specified threshold."""
    context.hyperfocus_threshold = threshold
    
    # Update session with hyperfocus protection
    response = await context.client.patch(
        f"/api/v1/focus/sessions/{context.session_id}",
        headers=context.headers,
        json={
            "enable_hyperfocus_protection": True,
            "hyperfocus_threshold": threshold
        }
    )
    
    assert response.status_code == 200


@then('the session should start successfully')
async def step_session_starts_successfully(context):
    """Verify the session started successfully."""
    start_response = await context.client.post(
        f"/api/v1/focus/sessions/{context.session_id}/start",
        headers=context.headers
    )
    
    assert start_response.status_code == 200
    context.session_start_data = start_response.json()


@then('hyperfocus monitoring should be active')
async def step_hyperfocus_monitoring_active(context):
    """Verify hyperfocus monitoring is active."""
    status_response = await context.client.get(
        f"/api/v1/focus/sessions/{context.session_id}/status",
        headers=context.headers
    )
    
    status_data = status_response.json()
    assert status_data["hyperfocus_protection_enabled"] is True
    assert status_data["hyperfocus_threshold"] == context.hyperfocus_threshold


@then('I should see a gentle reminder about the protection')
async def step_see_gentle_reminder(context):
    """Verify gentle reminder about hyperfocus protection."""
    assert "hyperfocus_protection_message" in context.session_start_data
    message = context.session_start_data["hyperfocus_protection_message"]
    
    # Check for gentle, supportive language
    gentle_words = ["gently", "remind", "support", "care", "wellbeing"]
    assert any(word in message.lower() for word in gentle_words)


@given('I am in an active focus session')
async def step_in_active_focus_session(context):
    """Set up an active focus session."""
    context.session = await FocusSessionFactory.create(
        context.db_session,
        user_id=context.user.id,
        status=SessionStatus.ACTIVE,
        actual_start_time=datetime.utcnow() - timedelta(minutes=80),
        enable_hyperfocus_protection=True,
        hyperfocus_threshold=75
    )


@given('the session has been running for {duration:d} minutes')
async def step_session_running_duration(context, duration):
    """Set the session as running for specified duration."""
    context.session.actual_start_time = datetime.utcnow() - timedelta(minutes=duration)
    await context.db_session.commit()


@given('my hyperfocus threshold is {threshold:d} minutes')
async def step_hyperfocus_threshold(context, threshold):
    """Set the hyperfocus threshold."""
    context.session.hyperfocus_threshold = threshold
    await context.db_session.commit()


@when('the hyperfocus protection system checks my status')
async def step_hyperfocus_system_check(context):
    """Trigger hyperfocus protection system check."""
    response = await context.client.get(
        f"/api/v1/focus/sessions/{context.session.id}/hyperfocus-check",
        headers=context.headers
    )
    
    assert response.status_code == 200
    context.hyperfocus_check_result = response.json()


@then('I should receive a gentle interruption notification')
async def step_receive_gentle_interruption(context):
    """Verify gentle interruption notification."""
    assert context.hyperfocus_check_result["hyperfocus_detected"] is True
    assert "gentle_interruption" in context.hyperfocus_check_result
    
    notification = context.hyperfocus_check_result["gentle_interruption"]
    assert notification["tone"] == "gentle"
    assert "break" in notification["message"].lower()


@then('the notification should suggest taking a break')
async def step_notification_suggests_break(context):
    """Verify notification suggests taking a break."""
    notification = context.hyperfocus_check_result["gentle_interruption"]
    assert "break" in notification["message"].lower()
    assert "rest" in notification["message"].lower() or "pause" in notification["message"].lower()


@then('the notification should not be jarring or stressful')
async def step_notification_not_jarring(context):
    """Verify notification is not jarring or stressful."""
    notification = context.hyperfocus_check_result["gentle_interruption"]
    
    # Check for absence of stressful language
    stressful_words = ["stop", "must", "immediately", "warning", "danger"]
    assert not any(word in notification["message"].lower() for word in stressful_words)
    
    # Check for gentle language
    gentle_words = ["consider", "might", "perhaps", "gently", "kindly"]
    assert any(word in notification["message"].lower() for word in gentle_words)


@then('I should have options to extend or take a break')
async def step_have_extend_or_break_options(context):
    """Verify options to extend or take a break."""
    notification = context.hyperfocus_check_result["gentle_interruption"]
    assert "options" in notification
    
    options = notification["options"]
    option_types = [opt["type"] for opt in options]
    assert "extend_session" in option_types
    assert "take_break" in option_types


@given('I receive a hyperfocus protection notification')
async def step_receive_hyperfocus_notification(context):
    """Set up receiving a hyperfocus protection notification."""
    context.hyperfocus_notification = {
        "type": "hyperfocus_protection",
        "message": "You've been focusing for 80 minutes. Consider taking a gentle break.",
        "options": [
            {"type": "extend_session", "duration": 30},
            {"type": "take_break", "duration": 15}
        ]
    }


@given('I feel I\'m still productive and not tired')
async def step_feel_productive_not_tired(context):
    """Set user state as productive and not tired."""
    context.user_state = {
        "energy_level": "medium",
        "productivity_feeling": "high",
        "fatigue_level": "low"
    }


@when('I choose to extend the session for {duration:d} minutes')
async def step_choose_extend_session(context, duration):
    """Choose to extend the focus session."""
    response = await context.client.post(
        f"/api/v1/focus/sessions/{context.session.id}/extend",
        headers=context.headers,
        json={
            "extension_duration": duration,
            "reason": "still_productive",
            "energy_check": context.user_state
        }
    )
    
    assert response.status_code == 200
    context.extension_result = response.json()


@then('the system should acknowledge my choice')
async def step_system_acknowledges_choice(context):
    """Verify system acknowledges the extension choice."""
    assert "acknowledgment" in context.extension_result
    assert "choice_respected" in context.extension_result["acknowledgment"]


@then('set a new protection checkpoint')
async def step_set_new_protection_checkpoint(context):
    """Verify new protection checkpoint is set."""
    assert "new_checkpoint" in context.extension_result
    assert context.extension_result["new_checkpoint"]["minutes"] > 0


@then('remind me to check in with my physical needs')
async def step_remind_physical_needs(context):
    """Verify reminder about physical needs."""
    assert "physical_needs_reminder" in context.extension_result
    reminder = context.extension_result["physical_needs_reminder"]
    
    physical_needs = ["hydration", "posture", "eye_rest", "movement"]
    assert any(need in reminder["message"].lower() for need in physical_needs)


@then('track this as a mindful extension')
async def step_track_mindful_extension(context):
    """Verify extension is tracked as mindful."""
    assert context.extension_result["extension_type"] == "mindful"
    assert "tracking_data" in context.extension_result


@given('I decide to take a break')
async def step_decide_take_break(context):
    """Set decision to take a break."""
    context.break_decision = True


@when('I pause the focus session')
async def step_pause_focus_session(context):
    """Pause the focus session."""
    response = await context.client.post(
        f"/api/v1/focus/sessions/{context.session.id}/pause",
        headers=context.headers,
        json={"reason": "hyperfocus_break"}
    )
    
    assert response.status_code == 200
    context.pause_result = response.json()


@then('the system should suggest break activities')
async def step_suggest_break_activities(context):
    """Verify system suggests break activities."""
    assert "break_suggestions" in context.pause_result
    suggestions = context.pause_result["break_suggestions"]
    assert len(suggestions) > 0
    
    # Verify suggestions are appropriate
    for suggestion in suggestions:
        assert "activity" in suggestion
        assert "duration" in suggestion
        assert suggestion["duration"] <= 20  # Reasonable break duration


@then('the suggestions should match my current energy level')
async def step_suggestions_match_energy(context):
    """Verify break suggestions match current energy level."""
    suggestions = context.pause_result["break_suggestions"]
    
    for suggestion in suggestions:
        # For tired users, suggest gentle activities
        if context.user_state.get("fatigue_level") == "high":
            gentle_activities = ["rest", "breathe", "stretch", "hydrate"]
            assert any(activity in suggestion["activity"].lower() for activity in gentle_activities)
