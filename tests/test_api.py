"""
Tests for FastAPI endpoints.

This module tests the ADHD-optimized task management API endpoints
to ensure they work correctly with proper validation and responses.
"""

import pytest
from fastapi.testclient import Test<PERSON>lient
from app.main import app


class TestTaskAPI:
    """Test suite for task management API endpoints."""
    
    @pytest.fixture
    def client(self):
        """Create test client for FastAPI app."""
        return TestClient(app)
    
    def test_root_endpoint(self, client):
        """Test root endpoint returns system information."""
        response = client.get("/")
        assert response.status_code == 200
        
        data = response.json()
        assert "message" in data
        assert "Project Chronos" in data["message"]
        assert "features" in data
        assert "ai_services" in data
    
    def test_health_check(self, client):
        """Test health check endpoint."""
        response = client.get("/health")
        assert response.status_code == 200
        
        data = response.json()
        assert data["status"] == "healthy"
        assert data["service"] == "task-management-ai-chunking"
    
    def test_features_endpoint(self, client):
        """Test features endpoint returns available functionality."""
        response = client.get("/api/v1/features")
        assert response.status_code == 200
        
        data = response.json()
        assert "task_management" in data
        assert "ai_chunking" in data
        assert "adaptive_filtering" in data
        assert "task_jar" in data
        
        # Check ADHD-specific features
        assert data["task_management"]["available"] is True
        assert "energy_level" in data["adaptive_filtering"]["filters"]
        assert "context_tags" in data["adaptive_filtering"]["filters"]
    
    def test_api_documentation_available(self, client):
        """Test that API documentation is accessible."""
        # Test OpenAPI schema
        response = client.get("/openapi.json")
        assert response.status_code == 200
        
        schema = response.json()
        assert "openapi" in schema
        assert "info" in schema
        assert "Project Chronos" in schema["info"]["title"]


class TestADHDFeatures:
    """Test ADHD-specific features in the API."""
    
    @pytest.fixture
    def client(self):
        """Create test client for FastAPI app."""
        return TestClient(app)
    
    def test_energy_level_filtering_parameter(self, client):
        """Test that energy level filtering is available."""
        # This would normally require a database, but we can test the endpoint exists
        response = client.get("/api/v1/tasks/?energy_level=low")
        # We expect this to fail due to no database, but the endpoint should exist
        assert response.status_code in [200, 500]  # 500 due to no DB connection
    
    def test_context_tags_parameter(self, client):
        """Test that context tags filtering is available."""
        response = client.get("/api/v1/tasks/?context_tags=home,computer")
        # We expect this to fail due to no database, but the endpoint should exist
        assert response.status_code in [200, 500]  # 500 due to no DB connection
    
    def test_task_jar_endpoint_exists(self, client):
        """Test that task jar endpoint exists for decision fatigue reduction."""
        response = client.get("/api/v1/tasks/jar/random")
        # We expect this to fail due to no database, but the endpoint should exist
        assert response.status_code in [200, 500]  # 500 due to no DB connection
    
    def test_adaptive_filtering_endpoint_exists(self, client):
        """Test that adaptive filtering endpoint exists."""
        response = client.get("/api/v1/tasks/adaptive/filtered?energy_level=medium")
        # We expect this to fail due to no database, but the endpoint should exist
        assert response.status_code in [200, 500]  # 500 due to no DB connection


class TestAIChunkingAPI:
    """Test AI chunking API functionality."""
    
    @pytest.fixture
    def client(self):
        """Create test client for FastAPI app."""
        return TestClient(app)
    
    def test_chunking_endpoint_exists(self, client):
        """Test that AI chunking endpoint exists."""
        # Test with a dummy task ID
        task_id = "550e8400-e29b-41d4-a716-446655440000"
        response = client.post(
            f"/api/v1/tasks/{task_id}/chunk",
            json={"chunk_size": "small", "context": "Test context"}
        )
        # We expect this to fail due to no database, but the endpoint should exist
        assert response.status_code in [200, 404, 500]


if __name__ == "__main__":
    pytest.main([__file__])
