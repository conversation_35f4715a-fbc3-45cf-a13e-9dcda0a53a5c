"""
Test data factories for Project Chronos.

This module provides factory classes for generating realistic test data
that reflects ADHD user patterns and behaviors, ensuring comprehensive
testing of ADHD-focused features.
"""

import factory
from factory import fuzzy
from datetime import datetime, timedelta
from uuid import uuid4
from typing import Dict, Any, List
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.user import User
from app.models.task import Task
from app.models.time_block import TimeBlock
from app.models.focus_session import FocusSession
from app.models.integration import Integration, IntegrationType, IntegrationStatus


class UserFactory(factory.Factory):
    """
    Factory for creating test users with ADHD-specific attributes.
    
    Generates realistic user profiles including ADHD diagnosis status,
    energy patterns, and personalized preferences for testing.
    """
    
    class Meta:
        model = User
    
    id = factory.LazyFunction(uuid4)
    email = factory.Sequence(lambda n: f"testuser{n}@chronos.test")
    username = factory.Sequence(lambda n: f"testuser{n}")
    full_name = factory.Faker("name")
    is_active = True
    is_verified = True
    
    # ADHD-specific attributes
    adhd_diagnosed = fuzzy.FuzzyChoice([True, False])
    diagnosis_date = factory.LazyFunction(
        lambda: datetime.utcnow() - timedelta(days=fuzzy.FuzzyInteger(30, 1095).fuzz())
    )
    
    # Default ADHD-optimized preferences
    preferences = factory.LazyAttribute(lambda obj: {
        "energy_patterns": {
            "morning": fuzzy.FuzzyChoice(["low", "medium", "high"]).fuzz(),
            "afternoon": fuzzy.FuzzyChoice(["low", "medium", "high"]).fuzz(),
            "evening": fuzzy.FuzzyChoice(["low", "medium", "high"]).fuzz(),
        },
        "notification_preferences": {
            "persistent": obj.adhd_diagnosed,
            "gentle": obj.adhd_diagnosed,
            "frequency": "high" if obj.adhd_diagnosed else "normal",
            "sound_enabled": not obj.adhd_diagnosed,
            "visual_emphasis": obj.adhd_diagnosed
        },
        "chunking_preferences": {
            "default_size": "small" if obj.adhd_diagnosed else "medium",
            "max_subtasks": 5 if obj.adhd_diagnosed else 8,
            "auto_chunk_threshold": 45 if obj.adhd_diagnosed else 90,
            "include_time_estimates": True
        },
        "focus_preferences": {
            "pomodoro_length": 25 if obj.adhd_diagnosed else 30,
            "break_length": 5,
            "long_break_length": 15 if obj.adhd_diagnosed else 10,
            "auto_start_breaks": obj.adhd_diagnosed
        }
    })
    
    created_at = factory.LazyFunction(datetime.utcnow)
    updated_at = factory.LazyFunction(datetime.utcnow)
    
    @classmethod
    async def create(
        cls, 
        db_session: AsyncSession, 
        **kwargs
    ) -> User:
        """
        Create and persist a user in the database.
        
        Args:
            db_session: Database session for persistence
            **kwargs: Override default factory attributes
            
        Returns:
            Created and persisted User instance
        """
        user_data = cls.build(**kwargs)
        user = User(**user_data.__dict__)
        
        db_session.add(user)
        await db_session.commit()
        await db_session.refresh(user)
        
        return user
    
    @classmethod
    def create_adhd_user(cls, **kwargs) -> Dict[str, Any]:
        """
        Create ADHD user with optimized defaults.
        
        Returns:
            User data dictionary with ADHD-specific optimizations
        """
        adhd_overrides = {
            "adhd_diagnosed": True,
            "preferences": {
                "energy_patterns": {
                    "morning": "high",
                    "afternoon": "medium", 
                    "evening": "low"
                },
                "notification_preferences": {
                    "persistent": True,
                    "gentle": True,
                    "frequency": "high"
                },
                "chunking_preferences": {
                    "default_size": "small",
                    "max_subtasks": 5,
                    "auto_chunk_threshold": 30
                }
            }
        }
        adhd_overrides.update(kwargs)
        return cls.build(**adhd_overrides)


class TaskFactory(factory.Factory):
    """
    Factory for creating test tasks with ADHD-optimized attributes.
    
    Generates realistic tasks with appropriate energy levels, durations,
    and context tags that reflect ADHD task management needs.
    """
    
    class Meta:
        model = Task
    
    id = factory.LazyFunction(uuid4)
    title = factory.Faker("sentence", nb_words=4)
    description = factory.Faker("text", max_nb_chars=200)
    
    # ADHD-specific task attributes
    priority = fuzzy.FuzzyChoice(["low", "medium", "high", "urgent"])
    energy_level = fuzzy.FuzzyChoice(["low", "medium", "high"])
    estimated_duration = fuzzy.FuzzyInteger(5, 180)  # 5 minutes to 3 hours
    
    # Context tags for ADHD filtering
    context_tags = factory.LazyFunction(lambda: [
        fuzzy.FuzzyChoice([
            "computer", "phone", "home", "office", "errands", 
            "creative", "admin", "health", "social", "learning"
        ]).fuzz() for _ in range(fuzzy.FuzzyInteger(1, 3).fuzz())
    ])
    
    # Task status and timing
    status = fuzzy.FuzzyChoice(["pending", "in_progress", "completed", "cancelled"])
    due_date = factory.LazyFunction(
        lambda: datetime.utcnow() + timedelta(
            days=fuzzy.FuzzyInteger(1, 30).fuzz(),
            hours=fuzzy.FuzzyInteger(0, 23).fuzz()
        )
    )
    
    # ADHD-specific tracking fields
    time_estimation_accuracy = None  # Will be calculated after completion
    procrastination_score = fuzzy.FuzzyFloat(0.0, 1.0)
    difficulty_rating = fuzzy.FuzzyInteger(1, 5)
    
    created_at = factory.LazyFunction(datetime.utcnow)
    updated_at = factory.LazyFunction(datetime.utcnow)
    
    @classmethod
    async def create(
        cls, 
        db_session: AsyncSession, 
        user_id: str = None,
        **kwargs
    ) -> Task:
        """
        Create and persist a task in the database.
        
        Args:
            db_session: Database session for persistence
            user_id: ID of the user who owns this task
            **kwargs: Override default factory attributes
            
        Returns:
            Created and persisted Task instance
        """
        if user_id:
            kwargs["user_id"] = user_id
            
        task_data = cls.build(**kwargs)
        task = Task(**task_data.__dict__)
        
        db_session.add(task)
        await db_session.commit()
        await db_session.refresh(task)
        
        return task
    
    @classmethod
    def create_large_task(cls, **kwargs) -> Dict[str, Any]:
        """
        Create large task that requires chunking.
        
        Returns:
            Task data for a complex task requiring ADHD chunking
        """
        large_task_overrides = {
            "estimated_duration": fuzzy.FuzzyInteger(120, 480).fuzz(),  # 2-8 hours
            "energy_level": "high",
            "priority": fuzzy.FuzzyChoice(["medium", "high"]).fuzz(),
            "context_tags": ["computer", "focus", "creative"],
            "difficulty_rating": fuzzy.FuzzyInteger(3, 5).fuzz()
        }
        large_task_overrides.update(kwargs)
        return cls.build(**large_task_overrides)
    
    @classmethod
    def create_quick_task(cls, **kwargs) -> Dict[str, Any]:
        """
        Create quick task suitable for low energy periods.
        
        Returns:
            Task data for a simple, low-energy task
        """
        quick_task_overrides = {
            "estimated_duration": fuzzy.FuzzyInteger(5, 30).fuzz(),  # 5-30 minutes
            "energy_level": "low",
            "priority": fuzzy.FuzzyChoice(["low", "medium"]).fuzz(),
            "context_tags": ["quick", "admin"],
            "difficulty_rating": fuzzy.FuzzyInteger(1, 2).fuzz()
        }
        quick_task_overrides.update(kwargs)
        return cls.build(**quick_task_overrides)
    
    @classmethod
    def create_urgent_task(cls, **kwargs) -> Dict[str, Any]:
        """
        Create urgent task with time pressure.
        
        Returns:
            Task data for an urgent, time-sensitive task
        """
        urgent_task_overrides = {
            "priority": "urgent",
            "due_date": datetime.utcnow() + timedelta(hours=fuzzy.FuzzyInteger(1, 24).fuzz()),
            "energy_level": fuzzy.FuzzyChoice(["medium", "high"]).fuzz(),
            "context_tags": ["urgent", "important"],
            "procrastination_score": fuzzy.FuzzyFloat(0.0, 0.3).fuzz()  # Less likely to procrastinate
        }
        urgent_task_overrides.update(kwargs)
        return cls.build(**urgent_task_overrides)


class ADHDTestDataBuilder:
    """
    Builder class for creating comprehensive ADHD test scenarios.
    
    Provides methods to create realistic test data sets that reflect
    common ADHD patterns and challenges.
    """
    
    @staticmethod
    async def create_adhd_user_with_varied_tasks(
        db_session: AsyncSession,
        task_count: int = 10
    ) -> Dict[str, Any]:
        """
        Create ADHD user with diverse task portfolio.
        
        Args:
            db_session: Database session
            task_count: Number of tasks to create
            
        Returns:
            Dictionary with user and categorized tasks
        """
        user = await UserFactory.create(db_session, adhd_diagnosed=True)
        
        tasks = {
            "large_projects": [],
            "quick_tasks": [],
            "urgent_tasks": [],
            "creative_tasks": [],
            "admin_tasks": []
        }
        
        # Create varied task types
        for i in range(task_count):
            if i < 2:  # Large projects
                task = await TaskFactory.create(
                    db_session,
                    user_id=user.id,
                    **TaskFactory.create_large_task()
                )
                tasks["large_projects"].append(task)
            elif i < 5:  # Quick tasks
                task = await TaskFactory.create(
                    db_session,
                    user_id=user.id,
                    **TaskFactory.create_quick_task()
                )
                tasks["quick_tasks"].append(task)
            elif i < 7:  # Urgent tasks
                task = await TaskFactory.create(
                    db_session,
                    user_id=user.id,
                    **TaskFactory.create_urgent_task()
                )
                tasks["urgent_tasks"].append(task)
            else:  # Regular tasks
                task = await TaskFactory.create(
                    db_session,
                    user_id=user.id
                )
                if "creative" in task.context_tags:
                    tasks["creative_tasks"].append(task)
                elif "admin" in task.context_tags:
                    tasks["admin_tasks"].append(task)
        
        return {
            "user": user,
            "tasks": tasks,
            "total_tasks": sum(len(task_list) for task_list in tasks.values())
        }
    
    @staticmethod
    def create_time_blindness_scenario() -> Dict[str, Any]:
        """
        Create test scenario for time blindness features.
        
        Returns:
            Test data for time estimation and tracking features
        """
        return {
            "underestimated_tasks": [
                TaskFactory.create_large_task(
                    estimated_duration=60,
                    title="Task that will take longer than expected"
                )
            ],
            "overestimated_tasks": [
                TaskFactory.create_quick_task(
                    estimated_duration=45,
                    title="Task that's simpler than expected"
                )
            ],
            "accurate_estimates": [
                TaskFactory.build(
                    estimated_duration=30,
                    title="Well-estimated task"
                )
            ]
        }


class TimeBlockFactory(factory.Factory):
    """Factory for creating test time blocks with ADHD considerations."""

    class Meta:
        model = TimeBlock

    id = factory.LazyFunction(uuid4)
    title = factory.Faker("sentence", nb_words=3)
    description = factory.Faker("text", max_nb_chars=100)

    # Time block scheduling
    start_time = factory.LazyFunction(
        lambda: datetime.utcnow().replace(minute=0, second=0, microsecond=0) +
                timedelta(hours=fuzzy.FuzzyInteger(1, 48).fuzz())
    )
    end_time = factory.LazyAttribute(
        lambda obj: obj.start_time + timedelta(
            minutes=fuzzy.FuzzyInteger(30, 180).fuzz()
        )
    )

    # ADHD-specific attributes
    block_type = fuzzy.FuzzyChoice(["work", "meeting", "break", "creative", "admin", "focus"])
    energy_level = fuzzy.FuzzyChoice(["low", "medium", "high"])
    buffer_before = fuzzy.FuzzyInteger(0, 15)
    buffer_after = fuzzy.FuzzyInteger(0, 15)

    # Visual and accessibility features
    color = fuzzy.FuzzyChoice(["#3B82F6", "#10B981", "#F59E0B", "#EF4444", "#8B5CF6"])
    is_flexible = fuzzy.FuzzyChoice([True, False])

    created_at = factory.LazyFunction(datetime.utcnow)
    updated_at = factory.LazyFunction(datetime.utcnow)

    @classmethod
    async def create(
        cls,
        db_session: AsyncSession,
        user_id: str = None,
        **kwargs
    ) -> TimeBlock:
        """Create and persist a time block."""
        if user_id:
            kwargs["user_id"] = user_id

        time_block_data = cls.build(**kwargs)
        time_block = TimeBlock(**time_block_data.__dict__)

        db_session.add(time_block)
        await db_session.commit()
        await db_session.refresh(time_block)

        return time_block


class FocusSessionFactory(factory.Factory):
    """Factory for creating test focus sessions."""

    class Meta:
        model = FocusSession

    id = factory.LazyFunction(uuid4)
    title = factory.Faker("sentence", nb_words=4)
    session_type = fuzzy.FuzzyChoice(["pomodoro", "deep_work", "creative", "admin"])

    # Session timing
    planned_duration = fuzzy.FuzzyInteger(15, 120)  # 15 minutes to 2 hours
    break_duration = fuzzy.FuzzyInteger(5, 15)

    # ADHD-specific features
    enable_hyperfocus_protection = True
    hyperfocus_threshold = fuzzy.FuzzyInteger(90, 180)

    # Session state
    status = fuzzy.FuzzyChoice(["planned", "active", "paused", "completed", "cancelled"])

    created_at = factory.LazyFunction(datetime.utcnow)
    updated_at = factory.LazyFunction(datetime.utcnow)

    @classmethod
    async def create(
        cls,
        db_session: AsyncSession,
        user_id: str = None,
        **kwargs
    ) -> FocusSession:
        """Create and persist a focus session."""
        if user_id:
            kwargs["user_id"] = user_id

        session_data = cls.build(**kwargs)
        session = FocusSession(**session_data.__dict__)

        db_session.add(session)
        await db_session.commit()
        await db_session.refresh(session)

        return session


class IntegrationFactory(factory.Factory):
    """Factory for creating test integrations."""

    class Meta:
        model = Integration

    id = factory.LazyFunction(uuid4)
    integration_type = fuzzy.FuzzyChoice([
        IntegrationType.GOOGLE_CALENDAR,
        IntegrationType.TODOIST,
        IntegrationType.SLACK,
        IntegrationType.NOTION
    ])
    name = factory.LazyAttribute(
        lambda obj: f"Test {obj.integration_type.value.replace('_', ' ').title()}"
    )
    description = factory.Faker("text", max_nb_chars=100)

    # Integration configuration
    config = factory.LazyFunction(lambda: {
        "sync_frequency": "real_time",
        "buffer_time": 5,
        "energy_detection": True
    })

    sync_settings = factory.LazyFunction(lambda: {
        "import_enabled": True,
        "export_enabled": True,
        "respect_focus_mode": True
    })

    # Status and health
    status = fuzzy.FuzzyChoice([
        IntegrationStatus.ACTIVE,
        IntegrationStatus.INACTIVE,
        IntegrationStatus.ERROR
    ])

    total_syncs = fuzzy.FuzzyInteger(0, 100)
    successful_syncs = factory.LazyAttribute(
        lambda obj: int(obj.total_syncs * fuzzy.FuzzyFloat(0.8, 1.0).fuzz())
    )
    error_count = fuzzy.FuzzyInteger(0, 5)

    created_at = factory.LazyFunction(datetime.utcnow)
    updated_at = factory.LazyFunction(datetime.utcnow)

    @classmethod
    async def create(
        cls,
        db_session: AsyncSession,
        user_id: str = None,
        **kwargs
    ) -> Integration:
        """Create and persist an integration."""
        if user_id:
            kwargs["user_id"] = user_id

        integration_data = cls.build(**kwargs)
        integration = Integration(**integration_data.__dict__)

        db_session.add(integration)
        await db_session.commit()
        await db_session.refresh(integration)

        return integration


class BodyDoublingSessionFactory(factory.Factory):
    """Factory for creating test body doubling sessions."""

    class Meta:
        model = BodyDoublingSession

    id = factory.LazyFunction(uuid4)
    title = factory.Faker("sentence", nb_words=4)
    description = factory.Faker("text", max_nb_chars=150)

    # Session scheduling
    scheduled_start = factory.LazyFunction(
        lambda: datetime.utcnow() + timedelta(
            hours=fuzzy.FuzzyInteger(1, 24).fuzz()
        )
    )
    duration_minutes = fuzzy.FuzzyInteger(30, 180)
    max_participants = fuzzy.FuzzyInteger(2, 8)

    # Session type and focus
    session_type = fuzzy.FuzzyChoice(["focus", "creative", "admin", "study", "mixed"])
    focus_level = fuzzy.FuzzyChoice(["light", "moderate", "deep"])

    # ADHD-friendly features
    allow_late_join = True
    gentle_accountability = True
    progress_sharing = fuzzy.FuzzyChoice([True, False])

    created_at = factory.LazyFunction(datetime.utcnow)
    updated_at = factory.LazyFunction(datetime.utcnow)

    @classmethod
    async def create(
        cls,
        db_session: AsyncSession,
        host_user_id: str = None,
        **kwargs
    ) -> BodyDoublingSession:
        """Create and persist a body doubling session."""
        if host_user_id:
            kwargs["host_user_id"] = host_user_id

        session_data = cls.build(**kwargs)
        session = BodyDoublingSession(**session_data.__dict__)

        db_session.add(session)
        await db_session.commit()
        await db_session.refresh(session)

        return session


class GamificationDataFactory(factory.Factory):
    """Factory for creating test gamification data."""

    class Meta:
        model = UserGamification

    id = factory.LazyFunction(uuid4)

    # Points and levels
    total_points = fuzzy.FuzzyInteger(0, 10000)
    level = factory.LazyAttribute(lambda obj: min(int(obj.total_points / 1000) + 1, 50))
    points_to_next_level = factory.LazyAttribute(
        lambda obj: (obj.level * 1000) - obj.total_points
    )

    # Streaks and achievements
    current_streak = fuzzy.FuzzyInteger(0, 30)
    longest_streak = factory.LazyAttribute(
        lambda obj: max(obj.current_streak, fuzzy.FuzzyInteger(obj.current_streak, 100).fuzz())
    )

    # ADHD-specific gamification preferences
    celebration_style = fuzzy.FuzzyChoice(["minimal", "moderate", "enthusiastic"])
    preferred_rewards = factory.LazyFunction(lambda: [
        fuzzy.FuzzyChoice([
            "points", "badges", "progress_bars", "celebrations",
            "social_recognition", "unlocks", "customization"
        ]).fuzz() for _ in range(fuzzy.FuzzyInteger(2, 5).fuzz())
    ])

    # Motivation tracking
    motivation_level = fuzzy.FuzzyChoice(["very_low", "low", "medium", "high", "very_high"])
    last_achievement_date = factory.LazyFunction(
        lambda: datetime.utcnow() - timedelta(days=fuzzy.FuzzyInteger(0, 7).fuzz())
    )

    created_at = factory.LazyFunction(datetime.utcnow)
    updated_at = factory.LazyFunction(datetime.utcnow)

    @classmethod
    async def create(
        cls,
        db_session: AsyncSession,
        user_id: str = None,
        **kwargs
    ) -> UserGamification:
        """Create and persist gamification data."""
        if user_id:
            kwargs["user_id"] = user_id

        gamification_data = cls.build(**kwargs)
        gamification = UserGamification(**gamification_data.__dict__)

        db_session.add(gamification)
        await db_session.commit()
        await db_session.refresh(gamification)

        return gamification


class ADHDScenarioFactory:
    """Factory for creating complex ADHD test scenarios."""

    @staticmethod
    async def create_hyperfocus_scenario(
        db_session: AsyncSession,
        user_id: str = None
    ) -> Dict[str, Any]:
        """Create scenario for testing hyperfocus protection."""
        if not user_id:
            user = await UserFactory.create(
                db_session,
                adhd_diagnosed=True,
                preferences={
                    "hyperfocus_protection": True,
                    "hyperfocus_threshold": 90
                }
            )
            user_id = user.id
        else:
            user = None

        # Create engaging task likely to trigger hyperfocus
        engaging_task = await TaskFactory.create(
            db_session,
            user_id=user_id,
            title="Design new creative project",
            energy_level="high",
            estimated_duration=60,
            context_tags=["creative", "computer", "design"]
        )

        # Create active focus session
        focus_session = await FocusSessionFactory.create(
            db_session,
            user_id=user_id,
            task_id=engaging_task.id,
            session_type="deep_work",
            planned_duration=90,
            enable_hyperfocus_protection=True,
            hyperfocus_threshold=90,
            status="active",
            actual_start_time=datetime.utcnow() - timedelta(minutes=95)
        )

        return {
            "user": user,
            "task": engaging_task,
            "focus_session": focus_session,
            "scenario_type": "hyperfocus_protection",
            "expected_intervention": True
        }

    @staticmethod
    async def create_energy_crash_scenario(
        db_session: AsyncSession,
        user_id: str = None
    ) -> Dict[str, Any]:
        """Create scenario for testing energy crash recovery."""
        if not user_id:
            user = await UserFactory.create(
                db_session,
                adhd_diagnosed=True,
                preferences={
                    "energy_tracking_enabled": True,
                    "gentle_reminders": True
                }
            )
            user_id = user.id
        else:
            user = None

        # Create tasks with different energy requirements
        high_energy_task = await TaskFactory.create(
            db_session,
            user_id=user_id,
            title="Complex analysis project",
            energy_level="high",
            estimated_duration=120
        )

        low_energy_tasks = []
        for i in range(3):
            task = await TaskFactory.create(
                db_session,
                user_id=user_id,
                title=f"Simple admin task {i+1}",
                energy_level="low",
                estimated_duration=15
            )
            low_energy_tasks.append(task)

        return {
            "user": user,
            "high_energy_task": high_energy_task,
            "low_energy_tasks": low_energy_tasks,
            "scenario_type": "energy_crash",
            "current_energy": "very_low",
            "recommended_action": "switch_to_low_energy_tasks"
        }

    @staticmethod
    async def create_overwhelming_task_scenario(
        db_session: AsyncSession,
        user_id: str = None
    ) -> Dict[str, Any]:
        """Create scenario for testing task chunking."""
        if not user_id:
            user = await UserFactory.create(
                db_session,
                adhd_diagnosed=True,
                preferences={
                    "ai_chunking_enabled": True,
                    "max_chunk_duration": 45
                }
            )
            user_id = user.id
        else:
            user = None

        # Create overwhelming task
        overwhelming_task = await TaskFactory.create(
            db_session,
            user_id=user_id,
            title="Complete quarterly business review presentation",
            description="Research data, create slides, practice presentation, gather feedback",
            estimated_duration=480,  # 8 hours
            energy_level="high",
            priority="high",
            context_tags=["computer", "presentation", "research", "creative"]
        )

        return {
            "user": user,
            "overwhelming_task": overwhelming_task,
            "scenario_type": "task_chunking",
            "expected_chunks": 6,
            "max_chunk_duration": 45,
            "chunking_needed": True
        }

    @staticmethod
    async def create_social_accountability_scenario(
        db_session: AsyncSession,
        participant_count: int = 3
    ) -> Dict[str, Any]:
        """Create scenario for testing social accountability features."""
        # Create multiple ADHD users
        users = []
        for i in range(participant_count):
            user = await UserFactory.create(
                db_session,
                adhd_diagnosed=True,
                email=f"participant{i+1}@test.com",
                preferences={
                    "social_features_enabled": True,
                    "accountability_partners": True
                }
            )
            users.append(user)

        # Create body doubling session
        session = await BodyDoublingSessionFactory.create(
            db_session,
            host_user_id=users[0].id,
            title="Morning Focus Session",
            session_type="focus",
            max_participants=participant_count,
            scheduled_start=datetime.utcnow() + timedelta(hours=1)
        )

        # Create tasks for each participant
        participant_tasks = {}
        for user in users:
            tasks = []
            for j in range(2):
                task = await TaskFactory.create(
                    db_session,
                    user_id=user.id,
                    title=f"Focus task {j+1} for {user.username}",
                    estimated_duration=45
                )
                tasks.append(task)
            participant_tasks[user.id] = tasks

        return {
            "users": users,
            "session": session,
            "participant_tasks": participant_tasks,
            "scenario_type": "social_accountability",
            "expected_participants": participant_count
        }
