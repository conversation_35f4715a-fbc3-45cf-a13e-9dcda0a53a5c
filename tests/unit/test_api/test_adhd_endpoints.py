"""
Unit tests for ADHD-specific API endpoints.

Tests all ADHD-focused endpoints including quick capture, AI chunking,
energy management, hyperfocus protection, and dopamine menu features.
"""

import pytest
from datetime import datetime, timedelta
from httpx import AsyncClient
from unittest.mock import patch, AsyncMock

from app.main import app
from app.models.user import User
from app.models.task import Task
from tests.factories import UserFactory, TaskFactory


@pytest.mark.unit
@pytest.mark.api
class TestQuickCaptureEndpoints:
    """Test quick task capture API endpoints."""

    @pytest.fixture
    async def adhd_client(self, db_session):
        """Create authenticated ADHD user client."""
        user = await UserFactory.create(
            db_session,
            adhd_diagnosed=True,
            preferences={
                "quick_capture_enabled": True,
                "ai_enhancement": True
            }
        )
        
        async with AsyncClient(app=app, base_url="http://test") as client:
            # Authenticate
            auth_response = await client.post("/api/v1/auth/login", json={
                "email": user.email,
                "password": "testpassword123"
            })
            
            token = auth_response.json()["access_token"]
            headers = {"Authorization": f"Bearer {token}"}
            
            yield client, user, headers

    async def test_quick_capture_text_input(self, adhd_client):
        """Test quick capture with text input."""
        client, user, headers = adhd_client
        
        response = await client.post(
            "/api/v1/tasks/quick-capture",
            headers=headers,
            json={
                "title": "Call dentist to schedule cleaning",
                "capture_method": "text",
                "auto_categorize": True,
                "auto_schedule": False
            }
        )
        
        assert response.status_code == 201
        data = response.json()
        
        assert data["title"] == "Call dentist to schedule cleaning"
        assert data["user_id"] == str(user.id)
        assert "ai_suggestions" in data
        assert data["ai_suggestions"]["category"] is not None
        assert data["ai_suggestions"]["estimated_duration"] > 0
        assert data["ai_suggestions"]["energy_level"] in ["low", "medium", "high"]

    async def test_quick_capture_voice_input(self, adhd_client):
        """Test quick capture with voice input simulation."""
        client, user, headers = adhd_client
        
        with patch('app.services.ai_service.AIService.transcribe_audio') as mock_transcribe:
            mock_transcribe.return_value = "Remember to buy groceries for dinner party tomorrow"
            
            response = await client.post(
                "/api/v1/tasks/quick-capture",
                headers=headers,
                json={
                    "audio_data": "base64_encoded_audio_data",
                    "capture_method": "voice",
                    "auto_categorize": True,
                    "auto_schedule": True
                }
            )
        
        assert response.status_code == 201
        data = response.json()
        
        assert "buy groceries" in data["title"].lower()
        assert data["ai_suggestions"]["category"] == "personal"
        assert "suggested_time_slots" in data

    async def test_quick_capture_with_context(self, adhd_client):
        """Test quick capture with contextual information."""
        client, user, headers = adhd_client
        
        response = await client.post(
            "/api/v1/tasks/quick-capture",
            headers=headers,
            json={
                "title": "Review quarterly reports",
                "context": {
                    "location": "office",
                    "current_energy": "high",
                    "available_time": 120,
                    "related_project": "Q4 Planning"
                },
                "auto_categorize": True
            }
        )
        
        assert response.status_code == 201
        data = response.json()
        
        assert data["ai_suggestions"]["energy_level"] == "high"
        assert data["ai_suggestions"]["estimated_duration"] <= 120
        assert "office" in data["ai_suggestions"]["suggested_contexts"]

    async def test_quick_capture_batch_mode(self, adhd_client):
        """Test quick capture in batch mode for brain dumps."""
        client, user, headers = adhd_client
        
        brain_dump_items = [
            "Email Sarah about project deadline",
            "Pick up dry cleaning",
            "Research vacation destinations",
            "Schedule car maintenance",
            "Call mom for birthday planning"
        ]
        
        response = await client.post(
            "/api/v1/tasks/quick-capture/batch",
            headers=headers,
            json={
                "items": brain_dump_items,
                "auto_categorize": True,
                "auto_prioritize": True,
                "source": "brain_dump"
            }
        )
        
        assert response.status_code == 201
        data = response.json()
        
        assert len(data["created_tasks"]) == 5
        assert all("ai_suggestions" in task for task in data["created_tasks"])
        assert "batch_insights" in data
        assert data["batch_insights"]["total_estimated_time"] > 0


@pytest.mark.unit
@pytest.mark.api
class TestAIChunkingEndpoints:
    """Test AI task chunking API endpoints."""

    async def test_chunk_overwhelming_task(self, adhd_client):
        """Test chunking an overwhelming task."""
        client, user, headers = adhd_client
        
        # Create overwhelming task
        task_response = await client.post(
            "/api/v1/tasks",
            headers=headers,
            json={
                "title": "Complete home office renovation",
                "description": "Paint walls, install shelving, organize files, set up equipment",
                "estimated_duration": 480,  # 8 hours
                "energy_level": "high",
                "complexity": "very_high"
            }
        )
        
        task_id = task_response.json()["id"]
        
        # Request chunking
        response = await client.post(
            f"/api/v1/tasks/{task_id}/chunk",
            headers=headers,
            json={
                "max_chunk_duration": 45,
                "user_energy_level": "medium",
                "available_time_slots": ["morning", "afternoon"],
                "context": "home_improvement"
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert "chunks" in data
        assert len(data["chunks"]) >= 4  # Should break into multiple chunks
        assert all(chunk["estimated_duration"] <= 45 for chunk in data["chunks"])
        assert all("energy_level" in chunk for chunk in data["chunks"])
        assert all("order" in chunk for chunk in data["chunks"])
        
        # Verify chunking quality
        assert "chunking_analysis" in data
        assert data["chunking_analysis"]["total_chunks"] == len(data["chunks"])
        assert data["chunking_analysis"]["complexity_reduction"] > 0

    async def test_chunk_with_dependencies(self, adhd_client):
        """Test chunking task with dependencies."""
        client, user, headers = adhd_client
        
        # Create task with dependencies
        task_response = await client.post(
            "/api/v1/tasks",
            headers=headers,
            json={
                "title": "Launch new product feature",
                "description": "Design, develop, test, and deploy new feature",
                "estimated_duration": 600,  # 10 hours
                "dependencies": ["design_approval", "api_ready", "testing_complete"]
            }
        )
        
        task_id = task_response.json()["id"]
        
        response = await client.post(
            f"/api/v1/tasks/{task_id}/chunk",
            headers=headers,
            json={
                "max_chunk_duration": 60,
                "respect_dependencies": True,
                "parallel_work_allowed": False
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # Verify dependency handling
        chunks = data["chunks"]
        assert len(chunks) >= 3
        
        # Check that dependencies are respected in ordering
        design_chunk = next(c for c in chunks if "design" in c["title"].lower())
        develop_chunk = next(c for c in chunks if "develop" in c["title"].lower())
        
        assert design_chunk["order"] < develop_chunk["order"]

    async def test_chunk_customization(self, adhd_client):
        """Test chunk customization and user preferences."""
        client, user, headers = adhd_client
        
        # Update user chunking preferences
        await client.patch(
            "/api/v1/user/preferences",
            headers=headers,
            json={
                "chunking_preferences": {
                    "preferred_chunk_size": 30,
                    "energy_matching": True,
                    "include_buffer_time": True,
                    "break_frequency": "every_chunk"
                }
            }
        )
        
        # Create task
        task_response = await client.post(
            "/api/v1/tasks",
            headers=headers,
            json={
                "title": "Write comprehensive project proposal",
                "estimated_duration": 180
            }
        )
        
        task_id = task_response.json()["id"]
        
        response = await client.post(
            f"/api/v1/tasks/{task_id}/chunk",
            headers=headers,
            json={"use_user_preferences": True}
        )
        
        assert response.status_code == 200
        data = response.json()
        
        chunks = data["chunks"]
        assert all(chunk["estimated_duration"] <= 35 for chunk in chunks)  # 30 + buffer
        assert all("break_suggestion" in chunk for chunk in chunks)

    async def test_rechunk_task(self, adhd_client):
        """Test re-chunking a task with different parameters."""
        client, user, headers = adhd_client
        
        # Create and chunk task initially
        task_response = await client.post(
            "/api/v1/tasks",
            headers=headers,
            json={
                "title": "Organize digital photo collection",
                "estimated_duration": 240
            }
        )
        
        task_id = task_response.json()["id"]
        
        # Initial chunking
        initial_response = await client.post(
            f"/api/v1/tasks/{task_id}/chunk",
            headers=headers,
            json={"max_chunk_duration": 60}
        )
        
        initial_chunks = initial_response.json()["chunks"]
        
        # Re-chunk with different parameters
        rechunk_response = await client.post(
            f"/api/v1/tasks/{task_id}/rechunk",
            headers=headers,
            json={
                "max_chunk_duration": 30,
                "reason": "chunks_too_long",
                "user_feedback": "60 minutes feels overwhelming"
            }
        )
        
        assert rechunk_response.status_code == 200
        rechunk_data = rechunk_response.json()
        
        new_chunks = rechunk_data["chunks"]
        assert len(new_chunks) > len(initial_chunks)  # More, smaller chunks
        assert all(chunk["estimated_duration"] <= 30 for chunk in new_chunks)
        
        # Verify rechunking tracking
        assert "rechunk_history" in rechunk_data
        assert rechunk_data["rechunk_history"]["previous_chunk_count"] == len(initial_chunks)


@pytest.mark.unit
@pytest.mark.api
class TestEnergyManagementEndpoints:
    """Test energy management API endpoints."""

    async def test_energy_level_update(self, adhd_client):
        """Test updating current energy level."""
        client, user, headers = adhd_client
        
        response = await client.post(
            "/api/v1/user/energy-update",
            headers=headers,
            json={
                "current_energy": "low",
                "energy_trend": "declining",
                "context": "post_lunch_crash",
                "mood": "tired",
                "physical_state": "sluggish"
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["energy_updated"] is True
        assert "recommendations" in data
        assert "energy_boost_activities" in data["recommendations"]
        assert "task_adjustments" in data["recommendations"]
        
        # Verify appropriate recommendations for low energy
        activities = data["recommendations"]["energy_boost_activities"]
        assert any("walk" in activity["name"].lower() for activity in activities)
        assert all(activity["duration"] <= 15 for activity in activities)

    async def test_energy_pattern_analysis(self, adhd_client):
        """Test energy pattern analysis."""
        client, user, headers = adhd_client
        
        # Simulate historical energy data
        energy_updates = [
            {"time": "09:00", "energy": "high", "day_offset": -7},
            {"time": "14:00", "energy": "medium", "day_offset": -7},
            {"time": "18:00", "energy": "low", "day_offset": -7},
            {"time": "09:00", "energy": "high", "day_offset": -6},
            {"time": "14:00", "energy": "low", "day_offset": -6},
            {"time": "18:00", "energy": "very_low", "day_offset": -6},
        ]
        
        # Submit historical data
        for update in energy_updates:
            await client.post(
                "/api/v1/user/energy-update",
                headers=headers,
                json={
                    "current_energy": update["energy"],
                    "timestamp": (datetime.utcnow() + timedelta(days=update["day_offset"])).isoformat(),
                    "context": "historical_data"
                }
            )
        
        # Get pattern analysis
        response = await client.get(
            "/api/v1/user/energy-patterns",
            headers=headers,
            params={"days_back": 7}
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert "patterns" in data
        assert "peak_energy_times" in data["patterns"]
        assert "low_energy_times" in data["patterns"]
        assert "recommendations" in data
        assert "optimal_scheduling" in data["recommendations"]

    async def test_energy_aware_task_suggestions(self, adhd_client):
        """Test getting energy-appropriate task suggestions."""
        client, user, headers = adhd_client
        
        # Create tasks with different energy requirements
        tasks_data = [
            {"title": "Write important proposal", "energy_level": "high", "estimated_duration": 90},
            {"title": "Respond to emails", "energy_level": "low", "estimated_duration": 30},
            {"title": "Review meeting notes", "energy_level": "medium", "estimated_duration": 20},
            {"title": "Brainstorm project ideas", "energy_level": "high", "estimated_duration": 45},
            {"title": "File documents", "energy_level": "low", "estimated_duration": 15}
        ]
        
        for task_data in tasks_data:
            await client.post("/api/v1/tasks", headers=headers, json=task_data)
        
        # Get suggestions for low energy
        response = await client.get(
            "/api/v1/tasks/energy-appropriate",
            headers=headers,
            params={
                "current_energy": "low",
                "available_time": 45,
                "max_tasks": 3
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        
        suggested_tasks = data["tasks"]
        assert len(suggested_tasks) <= 3
        assert all(task["energy_level"] == "low" for task in suggested_tasks)
        assert sum(task["estimated_duration"] for task in suggested_tasks) <= 45


@pytest.mark.unit
@pytest.mark.api
class TestHyperfocusProtectionEndpoints:
    """Test hyperfocus protection API endpoints."""

    async def test_hyperfocus_detection(self, adhd_client):
        """Test hyperfocus detection during active session."""
        client, user, headers = adhd_client
        
        # Create and start focus session
        session_response = await client.post(
            "/api/v1/focus/sessions",
            headers=headers,
            json={
                "session_type": "deep_work",
                "planned_duration": 90,
                "enable_hyperfocus_protection": True,
                "hyperfocus_threshold": 75
            }
        )
        
        session_id = session_response.json()["id"]
        
        # Start session
        await client.post(f"/api/v1/focus/sessions/{session_id}/start", headers=headers)
        
        # Simulate time passing (in real app, this would be tracked automatically)
        with patch('app.services.focus_session_service.datetime') as mock_datetime:
            mock_datetime.utcnow.return_value = datetime.utcnow() + timedelta(minutes=80)
            
            response = await client.get(
                f"/api/v1/focus/sessions/{session_id}/hyperfocus-check",
                headers=headers
            )
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["hyperfocus_detected"] is True
        assert data["session_duration_minutes"] == 80
        assert data["threshold_exceeded_by"] == 5
        assert "intervention_suggestions" in data
        assert data["intervention_suggestions"]["urgency"] in ["gentle", "moderate", "strong"]

    async def test_hyperfocus_intervention_options(self, adhd_client):
        """Test hyperfocus intervention options."""
        client, user, headers = adhd_client
        
        # Create active session in hyperfocus state
        session_response = await client.post(
            "/api/v1/focus/sessions",
            headers=headers,
            json={
                "session_type": "flow",
                "enable_hyperfocus_protection": True,
                "hyperfocus_threshold": 60
            }
        )
        
        session_id = session_response.json()["id"]
        await client.post(f"/api/v1/focus/sessions/{session_id}/start", headers=headers)
        
        # Get intervention options
        response = await client.get(
            f"/api/v1/focus/sessions/{session_id}/intervention-options",
            headers=headers
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert "options" in data
        options = data["options"]
        
        # Verify intervention options
        option_types = [opt["type"] for opt in options]
        assert "gentle_reminder" in option_types
        assert "break_suggestion" in option_types
        assert "extend_mindfully" in option_types
        assert "end_session" in option_types
        
        # Verify each option has required fields
        for option in options:
            assert "description" in option
            assert "consequences" in option
            assert "recommended" in option

    async def test_mindful_session_extension(self, adhd_client):
        """Test mindful session extension feature."""
        client, user, headers = adhd_client
        
        # Create session and trigger hyperfocus protection
        session_response = await client.post(
            "/api/v1/focus/sessions",
            headers=headers,
            json={
                "session_type": "deep_work",
                "planned_duration": 60,
                "enable_hyperfocus_protection": True
            }
        )
        
        session_id = session_response.json()["id"]
        await client.post(f"/api/v1/focus/sessions/{session_id}/start", headers=headers)
        
        # Request mindful extension
        response = await client.post(
            f"/api/v1/focus/sessions/{session_id}/extend-mindfully",
            headers=headers,
            json={
                "extension_duration": 30,
                "energy_check": "still_high",
                "physical_check": "comfortable",
                "motivation_check": "strong",
                "reason": "in_flow_state"
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["extension_granted"] is True
        assert data["new_end_time"] is not None
        assert "health_reminders" in data
        assert "next_check_in" in data
        
        # Verify health reminders are included
        reminders = data["health_reminders"]
        assert any("hydration" in reminder.lower() for reminder in reminders)
        assert any("posture" in reminder.lower() for reminder in reminders)
