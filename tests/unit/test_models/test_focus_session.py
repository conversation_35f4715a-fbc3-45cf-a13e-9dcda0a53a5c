"""
Unit tests for FocusSession model.

Tests ADHD-specific focus session features including hyperfocus protection,
energy-aware session planning, and gentle interruption mechanisms.
"""

import pytest
from datetime import datetime, timedelta
from unittest.mock import patch, MagicMock

from app.models.focus_session import FocusSession, SessionType, SessionStatus
from app.models.user import User
from app.models.task import Task
from tests.factories import UserFactory, TaskFactory, FocusSessionFactory


@pytest.mark.unit
class TestFocusSessionModel:
    """Test FocusSession model functionality."""

    async def test_create_focus_session(self, db_session):
        """Test creating a basic focus session."""
        user = await UserFactory.create(db_session)
        task = await TaskFactory.create(db_session, user_id=user.id)
        
        session = FocusSession(
            user_id=user.id,
            task_id=task.id,
            session_type=SessionType.POMODORO,
            planned_duration=25,
            break_duration=5
        )
        
        db_session.add(session)
        await db_session.commit()
        await db_session.refresh(session)
        
        assert session.id is not None
        assert session.user_id == user.id
        assert session.task_id == task.id
        assert session.session_type == SessionType.POMODORO
        assert session.status == SessionStatus.PLANNED

    async def test_adhd_hyperfocus_protection_enabled(self, db_session):
        """Test that hyperfocus protection is enabled by default for ADHD users."""
        adhd_user = await UserFactory.create(
            db_session, 
            adhd_diagnosed=True,
            preferences={
                "hyperfocus_protection": True,
                "hyperfocus_threshold_minutes": 90
            }
        )
        
        session = FocusSession(
            user_id=adhd_user.id,
            session_type=SessionType.DEEP_WORK,
            planned_duration=120
        )
        
        assert session.enable_hyperfocus_protection is True
        assert session.hyperfocus_threshold == 90

    async def test_session_duration_validation(self, db_session):
        """Test session duration validation for different types."""
        user = await UserFactory.create(db_session)
        
        # Pomodoro sessions should be 15-45 minutes
        with pytest.raises(ValueError, match="Pomodoro sessions must be between"):
            FocusSession(
                user_id=user.id,
                session_type=SessionType.POMODORO,
                planned_duration=60  # Too long for pomodoro
            )
        
        # Deep work sessions can be longer
        session = FocusSession(
            user_id=user.id,
            session_type=SessionType.DEEP_WORK,
            planned_duration=120
        )
        assert session.planned_duration == 120

    async def test_start_session(self, db_session):
        """Test starting a focus session."""
        session = await FocusSessionFactory.create(
            db_session,
            status=SessionStatus.PLANNED
        )
        
        start_time = datetime.utcnow()
        with patch('app.models.focus_session.datetime') as mock_datetime:
            mock_datetime.utcnow.return_value = start_time
            
            session.start()
        
        assert session.status == SessionStatus.ACTIVE
        assert session.actual_start_time == start_time
        assert session.current_segment == 1

    async def test_pause_and_resume_session(self, db_session):
        """Test pausing and resuming a session."""
        session = await FocusSessionFactory.create(
            db_session,
            status=SessionStatus.ACTIVE,
            actual_start_time=datetime.utcnow() - timedelta(minutes=10)
        )
        
        # Pause session
        pause_time = datetime.utcnow()
        with patch('app.models.focus_session.datetime') as mock_datetime:
            mock_datetime.utcnow.return_value = pause_time
            
            session.pause()
        
        assert session.status == SessionStatus.PAUSED
        assert session.total_pause_time == timedelta(0)
        
        # Resume session
        resume_time = pause_time + timedelta(minutes=5)
        with patch('app.models.focus_session.datetime') as mock_datetime:
            mock_datetime.utcnow.return_value = resume_time
            
            session.resume()
        
        assert session.status == SessionStatus.ACTIVE
        assert session.total_pause_time == timedelta(minutes=5)

    async def test_complete_session(self, db_session):
        """Test completing a focus session."""
        start_time = datetime.utcnow() - timedelta(minutes=25)
        session = await FocusSessionFactory.create(
            db_session,
            status=SessionStatus.ACTIVE,
            actual_start_time=start_time,
            planned_duration=25
        )
        
        completion_time = start_time + timedelta(minutes=23)
        with patch('app.models.focus_session.datetime') as mock_datetime:
            mock_datetime.utcnow.return_value = completion_time
            
            session.complete()
        
        assert session.status == SessionStatus.COMPLETED
        assert session.actual_end_time == completion_time
        assert session.actual_duration == 23

    async def test_hyperfocus_detection(self, db_session):
        """Test hyperfocus detection and warnings."""
        session = await FocusSessionFactory.create(
            db_session,
            status=SessionStatus.ACTIVE,
            actual_start_time=datetime.utcnow() - timedelta(minutes=95),
            enable_hyperfocus_protection=True,
            hyperfocus_threshold=90
        )
        
        assert session.is_in_hyperfocus() is True
        assert session.hyperfocus_duration_minutes == 95

    async def test_break_suggestions(self, db_session):
        """Test break suggestion logic."""
        session = await FocusSessionFactory.create(
            db_session,
            session_type=SessionType.POMODORO,
            planned_duration=25,
            break_duration=5,
            current_segment=1
        )
        
        suggestion = session.get_break_suggestion()
        
        assert suggestion["type"] == "short_break"
        assert suggestion["duration"] == 5
        assert "Take a 5-minute break" in suggestion["message"]

    async def test_energy_tracking(self, db_session):
        """Test energy level tracking during sessions."""
        session = await FocusSessionFactory.create(db_session)
        
        # Record energy levels
        session.record_energy_level("start", "high")
        session.record_energy_level("mid", "medium")
        session.record_energy_level("end", "low")
        
        energy_data = session.energy_tracking
        assert energy_data["start"] == "high"
        assert energy_data["mid"] == "medium"
        assert energy_data["end"] == "low"

    async def test_session_analytics(self, db_session):
        """Test session analytics calculation."""
        session = await FocusSessionFactory.create(
            db_session,
            status=SessionStatus.COMPLETED,
            planned_duration=25,
            actual_duration=23,
            total_pause_time=timedelta(minutes=2),
            interruption_count=1
        )
        
        analytics = session.get_analytics()
        
        assert analytics["completion_rate"] == pytest.approx(0.92, rel=0.01)  # 23/25
        assert analytics["focus_efficiency"] == pytest.approx(0.91, rel=0.01)  # (23-2)/23
        assert analytics["interruption_rate"] == pytest.approx(0.04, rel=0.01)  # 1/25

    async def test_adhd_friendly_session_end(self, db_session):
        """Test ADHD-friendly session ending with celebration."""
        session = await FocusSessionFactory.create(
            db_session,
            status=SessionStatus.COMPLETED,
            actual_duration=25
        )
        
        celebration = session.get_completion_celebration()
        
        assert "congratulations" in celebration["message"].lower()
        assert celebration["points_earned"] > 0
        assert "next_steps" in celebration
        assert len(celebration["achievements"]) >= 0

    async def test_session_difficulty_adjustment(self, db_session):
        """Test automatic difficulty adjustment based on completion patterns."""
        user = await UserFactory.create(db_session, adhd_diagnosed=True)
        
        # Create multiple completed sessions
        sessions = []
        for i in range(5):
            session = await FocusSessionFactory.create(
                db_session,
                user_id=user.id,
                status=SessionStatus.COMPLETED,
                planned_duration=25,
                actual_duration=20 + i  # Gradually improving
            )
            sessions.append(session)
        
        # Test difficulty adjustment suggestion
        latest_session = sessions[-1]
        suggestion = latest_session.suggest_next_session_adjustment()
        
        assert suggestion["recommended_duration"] >= 25
        assert "confidence" in suggestion
        assert suggestion["reasoning"] is not None
