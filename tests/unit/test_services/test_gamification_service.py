"""
Unit tests for GamificationService.

Tests ADHD-optimized gamification features including dopamine-friendly rewards,
achievement systems, and motivation tracking.
"""

import pytest
from datetime import datetime, timedelta
from unittest.mock import AsyncMock, patch, MagicMock

from app.services.gamification_service import GamificationService
from app.services.achievement_service import AchievementService
from app.models.gamification import UserGamification, PointsAward, Achievement
from app.models.user import User
from app.schemas.gamification import PointsAwardCreate, AchievementUnlock
from tests.factories import UserFactory, TaskFactory


@pytest.mark.unit
class TestGamificationService:
    """Test GamificationService functionality."""

    @pytest.fixture
    async def gamification_service(self, db_session):
        """Create gamification service instance."""
        return GamificationService(db_session)

    @pytest.fixture
    async def adhd_user(self, db_session):
        """Create ADHD user with gamification preferences."""
        return await UserFactory.create(
            db_session,
            adhd_diagnosed=True,
            preferences={
                "gamification_enabled": True,
                "celebration_style": "moderate",
                "preferred_rewards": ["points", "badges", "progress_bars"],
                "dopamine_sensitivity": "high"
            }
        )

    async def test_initialize_user_gamification(self, gamification_service, adhd_user):
        """Test initializing gamification for a new user."""
        profile = await gamification_service.initialize_user_gamification(adhd_user.id)
        
        assert profile.user_id == adhd_user.id
        assert profile.total_points == 0
        assert profile.level == 1
        assert profile.gamification_enabled is True
        assert profile.celebration_style == "moderate"

    async def test_award_points_for_task_completion(self, gamification_service, adhd_user):
        """Test awarding points for completing tasks."""
        task = await TaskFactory.create(
            gamification_service.db,
            user_id=adhd_user.id,
            energy_level="high",
            estimated_duration=60
        )
        
        points_award = await gamification_service.award_points(
            user_id=adhd_user.id,
            action="task_completed",
            context={
                "task_id": str(task.id),
                "energy_level": "high",
                "duration": 60,
                "on_time": True
            }
        )
        
        assert points_award.points_awarded > 0
        assert points_award.action == "task_completed"
        assert "high_energy_bonus" in points_award.bonus_reasons

    async def test_dopamine_optimized_rewards(self, gamification_service, adhd_user):
        """Test dopamine-optimized reward calculation."""
        # Test immediate small reward
        small_reward = await gamification_service.calculate_reward(
            user_id=adhd_user.id,
            action="quick_task_completed",
            context={"duration": 5, "difficulty": "easy"}
        )
        
        assert small_reward["immediate_feedback"] is True
        assert small_reward["celebration_intensity"] == "gentle"
        assert small_reward["points"] >= 5
        
        # Test larger reward for significant achievement
        big_reward = await gamification_service.calculate_reward(
            user_id=adhd_user.id,
            action="focus_session_completed",
            context={"duration": 120, "no_interruptions": True}
        )
        
        assert big_reward["celebration_intensity"] == "enthusiastic"
        assert big_reward["points"] >= 50
        assert "streak_bonus" in big_reward.get("bonuses", [])

    async def test_level_progression(self, gamification_service, adhd_user):
        """Test level progression system."""
        # Award enough points to trigger level up
        await gamification_service.award_points(
            user_id=adhd_user.id,
            action="bulk_test_points",
            points_override=1000
        )
        
        profile = await gamification_service.get_user_profile(adhd_user.id)
        
        assert profile.level > 1
        assert profile.total_points >= 1000
        
        # Check level up celebration
        level_up_data = await gamification_service.check_level_up(adhd_user.id)
        if level_up_data:
            assert "congratulations" in level_up_data["message"].lower()
            assert level_up_data["new_level"] > 1
            assert "unlocked_features" in level_up_data

    async def test_achievement_system(self, gamification_service, adhd_user):
        """Test achievement unlocking system."""
        # Create achievement progress
        await gamification_service.track_achievement_progress(
            user_id=adhd_user.id,
            achievement_type="task_completionist",
            progress_data={"tasks_completed": 10}
        )
        
        # Check for achievement unlock
        unlocked = await gamification_service.check_achievement_unlocks(adhd_user.id)
        
        if unlocked:
            assert len(unlocked) > 0
            achievement = unlocked[0]
            assert achievement["type"] == "task_completionist"
            assert achievement["celebration_message"] is not None

    async def test_streak_tracking(self, gamification_service, adhd_user):
        """Test streak tracking and rewards."""
        # Simulate daily task completion streak
        for day in range(7):
            completion_date = datetime.utcnow() - timedelta(days=6-day)
            
            with patch('app.services.gamification_service.datetime') as mock_datetime:
                mock_datetime.utcnow.return_value = completion_date
                
                await gamification_service.update_streak(
                    user_id=adhd_user.id,
                    streak_type="daily_tasks",
                    action_completed=True
                )
        
        streak_data = await gamification_service.get_user_streaks(adhd_user.id)
        daily_streak = next(s for s in streak_data if s["type"] == "daily_tasks")
        
        assert daily_streak["current_streak"] == 7
        assert daily_streak["is_active"] is True

    async def test_gentle_failure_handling(self, gamification_service, adhd_user):
        """Test gentle handling of missed streaks and failures."""
        # Break a streak
        await gamification_service.update_streak(
            user_id=adhd_user.id,
            streak_type="daily_tasks",
            action_completed=False
        )
        
        response = await gamification_service.handle_streak_break(
            user_id=adhd_user.id,
            streak_type="daily_tasks"
        )
        
        # Should be encouraging, not punitive
        assert "tomorrow" in response["message"].lower()
        assert "try again" in response["message"].lower()
        assert response["points_lost"] == 0  # No punishment
        assert "encouragement" in response

    async def test_personalized_challenges(self, gamification_service, adhd_user):
        """Test personalized challenge generation."""
        # Set user activity patterns
        await gamification_service.update_user_patterns(
            user_id=adhd_user.id,
            patterns={
                "most_productive_time": "morning",
                "average_focus_duration": 25,
                "preferred_task_types": ["creative", "analytical"]
            }
        )
        
        challenges = await gamification_service.generate_personalized_challenges(adhd_user.id)
        
        assert len(challenges) > 0
        challenge = challenges[0]
        assert challenge["difficulty"] in ["easy", "medium", "hard"]
        assert challenge["estimated_duration"] <= 30  # ADHD-appropriate
        assert "reward_preview" in challenge

    async def test_motivation_analytics(self, gamification_service, adhd_user):
        """Test motivation analytics and insights."""
        # Create some activity data
        activities = [
            {"action": "task_completed", "points": 10, "timestamp": datetime.utcnow() - timedelta(hours=1)},
            {"action": "focus_session", "points": 25, "timestamp": datetime.utcnow() - timedelta(hours=2)},
            {"action": "achievement_unlocked", "points": 50, "timestamp": datetime.utcnow() - timedelta(hours=3)}
        ]
        
        for activity in activities:
            await gamification_service.record_activity(adhd_user.id, activity)
        
        analytics = await gamification_service.get_motivation_analytics(adhd_user.id)
        
        assert "total_points_today" in analytics
        assert "motivation_trend" in analytics
        assert "most_rewarding_activities" in analytics
        assert analytics["total_points_today"] == 85

    async def test_celebration_customization(self, gamification_service, adhd_user):
        """Test celebration style customization."""
        # Test different celebration styles
        styles = ["minimal", "moderate", "enthusiastic"]
        
        for style in styles:
            await gamification_service.update_celebration_style(adhd_user.id, style)
            
            celebration = await gamification_service.create_celebration(
                user_id=adhd_user.id,
                achievement_type="task_completed",
                points_earned=25
            )
            
            if style == "minimal":
                assert len(celebration["animations"]) <= 1
                assert celebration["sound_enabled"] is False
            elif style == "enthusiastic":
                assert len(celebration["animations"]) >= 2
                assert "confetti" in celebration["visual_effects"]

    async def test_dopamine_menu_integration(self, gamification_service, adhd_user):
        """Test integration with dopamine menu system."""
        # User completes a dopamine activity
        activity_completion = {
            "activity_id": "quick_walk",
            "duration": 5,
            "energy_before": "low",
            "energy_after": "medium",
            "satisfaction": 4
        }
        
        reward = await gamification_service.reward_dopamine_activity(
            user_id=adhd_user.id,
            completion_data=activity_completion
        )
        
        assert reward["points_awarded"] > 0
        assert "energy_boost_bonus" in reward["bonus_reasons"]
        assert reward["encouragement_message"] is not None

    async def test_social_features(self, gamification_service, adhd_user, db_session):
        """Test social gamification features."""
        # Create another user for comparison
        friend = await UserFactory.create(db_session, adhd_diagnosed=True)
        
        # Add friend connection
        await gamification_service.add_accountability_partner(adhd_user.id, friend.id)
        
        # Get social comparison data
        social_data = await gamification_service.get_social_motivation_data(adhd_user.id)
        
        assert "accountability_partners" in social_data
        assert len(social_data["accountability_partners"]) == 1
        assert "shared_challenges" in social_data

    async def test_burnout_prevention(self, gamification_service, adhd_user):
        """Test burnout prevention in gamification."""
        # Simulate high activity that might lead to burnout
        for _ in range(20):  # Many activities in short time
            await gamification_service.award_points(
                user_id=adhd_user.id,
                action="task_completed",
                points_override=10
            )
        
        burnout_check = await gamification_service.check_burnout_risk(adhd_user.id)
        
        if burnout_check["risk_level"] == "high":
            assert "rest_recommendation" in burnout_check
            assert burnout_check["suggested_break_duration"] > 0
            assert "gentle_reminder" in burnout_check["message"]

    async def test_accessibility_features(self, gamification_service, adhd_user):
        """Test accessibility features in gamification."""
        # Update user accessibility preferences
        await gamification_service.update_accessibility_preferences(
            user_id=adhd_user.id,
            preferences={
                "reduce_motion": True,
                "high_contrast": True,
                "screen_reader_friendly": True,
                "simplified_interface": True
            }
        )
        
        celebration = await gamification_service.create_accessible_celebration(
            user_id=adhd_user.id,
            achievement_type="focus_session_completed"
        )
        
        assert celebration["animations_disabled"] is True
        assert celebration["high_contrast_colors"] is True
        assert celebration["screen_reader_text"] is not None
