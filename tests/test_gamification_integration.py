"""
Integration tests for gamification features.

This module tests the integration between gamification and other
systems like task management.
"""

import pytest
from datetime import datetime
from uuid import uuid4

from app.utils.gamification_utils import GamificationIntegration
from app.models.task import Task
from app.models.user import User
from app.services.gamification_service import GamificationService
from app.services.achievement_service import AchievementService


class TestGamificationIntegration:
    """Test suite for gamification integration."""
    
    @pytest.fixture
    async def mock_user(self, db_session):
        """Create a mock user for testing."""
        user = User(
            email="<EMAIL>",
            hashed_password="test_hash",
            first_name="Test",
            last_name="User",
            is_active=True,
            adhd_diagnosis=True
        )
        db_session.add(user)
        await db_session.commit()
        await db_session.refresh(user)
        return user
    
    @pytest.fixture
    async def sample_task(self, mock_user, db_session):
        """Create a sample task for testing."""
        task = Task(
            title="Test Task",
            description="A test task for gamification",
            priority="medium",
            energy_level="medium",
            estimated_duration=30,
            status="pending",
            user_id=mock_user.id
        )
        db_session.add(task)
        await db_session.commit()
        await db_session.refresh(task)
        return task
    
    @pytest.fixture
    def gamification_integration(self, db_session):
        """Create a gamification integration instance."""
        return GamificationIntegration(db_session)
    
    @pytest.fixture
    async def initialized_achievements(self, db_session):
        """Initialize achievements for testing."""
        achievement_service = AchievementService(db_session)
        await achievement_service.initialize_achievements()
    
    async def test_task_completion_awards_points(
        self, gamification_integration, mock_user, sample_task, db_session
    ):
        """Test that completing a task awards points."""
        # Mark task as completed
        sample_task.mark_completed()
        
        # Handle gamification
        results = await gamification_integration.handle_task_completion(
            task=sample_task,
            user_id=mock_user.id
        )
        
        assert results["points_awarded"] > 0
        assert "level_up" in results
        assert "achievements_unlocked" in results
        assert "streak_updated" in results
    
    async def test_task_start_awards_points(
        self, gamification_integration, mock_user, sample_task, db_session
    ):
        """Test that starting a task awards points."""
        # Handle task start gamification
        results = await gamification_integration.handle_task_start(
            task=sample_task,
            user_id=mock_user.id
        )
        
        assert results["points_awarded"] > 0
        assert "achievements_unlocked" in results
    
    async def test_high_priority_task_bonus(
        self, gamification_integration, mock_user, db_session
    ):
        """Test that high priority tasks get bonus points."""
        high_priority_task = Task(
            title="Urgent Task",
            priority="urgent",
            energy_level="high",
            estimated_duration=60,
            status="completed",
            user_id=mock_user.id
        )
        db_session.add(high_priority_task)
        await db_session.commit()
        
        results = await gamification_integration.handle_task_completion(
            task=high_priority_task,
            user_id=mock_user.id
        )
        
        # Should get more points than a medium priority task
        assert results["points_awarded"] > 30  # Base + priority + energy bonuses
    
    async def test_low_energy_completion_bonus(
        self, gamification_integration, mock_user, db_session
    ):
        """Test bonus for completing tasks during low energy."""
        task = Task(
            title="Low Energy Task",
            priority="medium",
            energy_level="medium",
            status="completed",
            user_id=mock_user.id
        )
        db_session.add(task)
        await db_session.commit()
        
        # Simulate low energy context
        context = {"user_energy_level": "low"}
        
        results = await gamification_integration.handle_task_completion(
            task=task,
            user_id=mock_user.id,
            completion_context=context
        )
        
        # Should get bonus for pushing through low energy
        assert results["points_awarded"] > 30
    
    async def test_streak_tracking(
        self, gamification_integration, mock_user, sample_task, db_session
    ):
        """Test that task completion updates streaks."""
        sample_task.mark_completed()
        
        results = await gamification_integration.handle_task_completion(
            task=sample_task,
            user_id=mock_user.id
        )
        
        # Should have updated streak
        streak = results["streak_updated"]
        if streak:  # Streak might be None if no change
            assert streak.current_streak >= 1
            assert streak.streak_type == "daily_tasks"
    
    async def test_achievement_unlocking(
        self, gamification_integration, mock_user, sample_task, 
        initialized_achievements, db_session
    ):
        """Test that achievements are unlocked on task completion."""
        sample_task.mark_completed()
        
        results = await gamification_integration.handle_task_completion(
            task=sample_task,
            user_id=mock_user.id
        )
        
        # Should unlock "first_task" achievement
        achievements = results["achievements_unlocked"]
        if achievements:  # Might be empty if already unlocked
            assert len(achievements) > 0
    
    async def test_level_up_detection(
        self, gamification_integration, mock_user, db_session
    ):
        """Test level up detection when enough points are earned."""
        # Create a gamification service to pre-award points
        gamification_service = GamificationService(db_session)
        
        # Award 90 points to get close to level 2 (100 points)
        await gamification_service.award_points(
            user_id=mock_user.id,
            points=90,
            reason="Setup for level up test"
        )
        
        # Create a task that should trigger level up
        task = Task(
            title="Level Up Task",
            priority="medium",
            energy_level="medium",
            status="completed",
            user_id=mock_user.id
        )
        db_session.add(task)
        await db_session.commit()
        
        results = await gamification_integration.handle_task_completion(
            task=task,
            user_id=mock_user.id
        )
        
        # Should trigger level up
        level_up = results["level_up"]
        if level_up:
            assert level_up.old_level == 1
            assert level_up.new_level == 2
    
    async def test_motivation_boost_suggestions(
        self, gamification_integration, mock_user, db_session
    ):
        """Test getting motivation boost suggestions."""
        boost = await gamification_integration.get_user_motivation_boost(
            user_id=mock_user.id,
            current_energy="medium"
        )
        
        assert "current_level" in boost
        assert "total_points" in boost
        assert "suggestions" in boost
        assert isinstance(boost["suggestions"], list)
    
    async def test_base_points_calculation(
        self, gamification_integration
    ):
        """Test base points calculation for different task types."""
        # Low priority, low energy task
        low_task = Task(
            title="Easy Task",
            priority="low",
            energy_level="low",
            estimated_duration=15
        )
        
        low_points = gamification_integration._calculate_base_points(low_task)
        
        # High priority, high energy task
        high_task = Task(
            title="Difficult Task",
            priority="urgent",
            energy_level="high",
            estimated_duration=120
        )
        
        high_points = gamification_integration._calculate_base_points(high_task)
        
        # High priority task should get more points
        assert high_points > low_points
    
    async def test_chunked_task_bonus(
        self, gamification_integration, mock_user, db_session
    ):
        """Test that chunked subtasks get bonus points."""
        # Create parent task
        parent_task = Task(
            title="Parent Task",
            priority="medium",
            energy_level="medium",
            user_id=mock_user.id
        )
        db_session.add(parent_task)
        await db_session.commit()
        
        # Create chunked subtask
        subtask = Task(
            title="Subtask",
            priority="medium",
            energy_level="medium",
            parent_task_id=parent_task.id,
            status="completed",
            user_id=mock_user.id
        )
        db_session.add(subtask)
        await db_session.commit()
        
        results = await gamification_integration.handle_task_completion(
            task=subtask,
            user_id=mock_user.id
        )
        
        # Should get bonus for being a chunked task
        base_points = gamification_integration._calculate_base_points(subtask)
        assert base_points > 25  # Should include chunked task bonus
    
    async def test_time_based_multipliers(
        self, gamification_integration, mock_user, db_session
    ):
        """Test time-based point multipliers."""
        task = Task(
            title="Time Test Task",
            priority="medium",
            energy_level="medium",
            status="completed",
            user_id=mock_user.id
        )
        
        # Test early morning context
        morning_context = {"completion_time": "07:30"}
        multiplier = await gamification_integration._calculate_task_multiplier(
            task, morning_context
        )
        
        # Should get morning bonus
        assert multiplier > 1.0
