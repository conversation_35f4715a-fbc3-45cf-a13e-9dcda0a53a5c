"""
Tests for task service functionality.

This module tests the core ADHD-optimized task management features
including CRUD operations, AI chunking, and adaptive filtering.
"""

import pytest
from uuid import uuid4
from datetime import datetime, timedelta
from app.services.task_service import TaskService
from app.schemas.task import TaskCreate, TaskUpdate
from app.models.task import Task
from app.models.user import User


class TestTaskService:
    """Test suite for TaskService with ADHD-specific features."""
    
    @pytest.fixture
    async def task_service(self):
        """Create task service instance."""
        return TaskService()
    
    @pytest.fixture
    async def sample_user_id(self):
        """Create sample user ID for testing."""
        return uuid4()
    
    @pytest.fixture
    async def sample_task_data(self):
        """Create sample task data for testing."""
        return TaskCreate(
            title="Test Task for ADHD User",
            description="A test task with ADHD-optimized features",
            priority="medium",
            energy_level="low",
            estimated_duration=30,
            context_tags=["home", "computer"],
            due_date=datetime.utcnow() + timedelta(days=1)
        )
    
    async def test_create_task_with_adhd_features(
        self,
        task_service: TaskService,
        sample_task_data: TaskCreate,
        sample_user_id
    ):
        """Test task creation with ADHD-specific fields."""
        # This would require a database session in a real test
        # For now, we'll test the data validation
        
        assert sample_task_data.energy_level in ["low", "medium", "high"]
        assert sample_task_data.priority in ["low", "medium", "high", "urgent"]
        assert sample_task_data.estimated_duration > 0
        assert len(sample_task_data.context_tags) > 0
    
    async def test_task_urgency_calculation(self):
        """Test urgency score calculation for ADHD users."""
        # Create a mock task
        task = Task(
            title="Urgent Test Task",
            priority="high",
            energy_level="low",
            due_date=datetime.utcnow() + timedelta(hours=2),  # Due soon
            user_id=uuid4()
        )
        
        urgency_score = task.calculate_urgency_score()
        
        # Should have high urgency due to priority and due date
        assert urgency_score > 3.0
    
    async def test_energy_level_validation(self, sample_task_data: TaskCreate):
        """Test energy level validation for ADHD optimization."""
        valid_energy_levels = ["low", "medium", "high"]
        
        assert sample_task_data.energy_level in valid_energy_levels
        
        # Test that invalid energy levels would be rejected
        # (This would be handled by Pydantic validation)
    
    async def test_context_tags_functionality(self, sample_task_data: TaskCreate):
        """Test context tags for adaptive filtering."""
        assert isinstance(sample_task_data.context_tags, list)
        assert len(sample_task_data.context_tags) > 0
        
        # Context tags should be useful for ADHD users
        common_contexts = ["home", "office", "computer", "phone", "errands"]
        has_common_context = any(
            tag in common_contexts for tag in sample_task_data.context_tags
        )
        assert has_common_context
    
    async def test_task_completion_tracking(self):
        """Test task completion with time tracking for ADHD users."""
        task = Task(
            title="Completion Test Task",
            status="pending",
            user_id=uuid4()
        )
        
        # Test starting work
        task.start_work()
        assert task.status == "in_progress"
        assert task.started_at is not None
        
        # Test completion
        task.mark_completed()
        assert task.status == "completed"
        assert task.completed_at is not None
        assert task.is_completed is True
    
    async def test_soft_delete_for_adhd_users(self):
        """Test soft delete functionality for ADHD users."""
        task = Task(
            title="Soft Delete Test",
            user_id=uuid4()
        )
        
        # Initially not deleted
        assert not task.is_deleted
        
        # Soft delete
        task.soft_delete()
        assert task.is_deleted
        assert task.deleted_at is not None
        
        # Restore
        task.restore()
        assert not task.is_deleted
        assert task.deleted_at is None


class TestADHDSpecificFeatures:
    """Test ADHD-specific features and optimizations."""
    
    async def test_time_estimation_accuracy(self):
        """Test time estimation tracking for ADHD users."""
        task = Task(
            title="Time Estimation Test",
            estimated_duration=30,  # 30 minutes estimated
            actual_duration=45,     # 45 minutes actual
            user_id=uuid4()
        )
        
        accuracy = task.duration_accuracy
        assert accuracy is not None
        assert 0.5 < accuracy < 1.0  # Underestimated time (common for ADHD)
    
    async def test_overdue_detection(self):
        """Test overdue task detection for ADHD users."""
        # Overdue task
        overdue_task = Task(
            title="Overdue Task",
            due_date=datetime.utcnow() - timedelta(hours=1),
            status="pending",
            user_id=uuid4()
        )
        assert overdue_task.is_overdue
        
        # Not overdue task
        future_task = Task(
            title="Future Task",
            due_date=datetime.utcnow() + timedelta(hours=1),
            status="pending",
            user_id=uuid4()
        )
        assert not future_task.is_overdue
        
        # Completed task (not overdue even if past due date)
        completed_task = Task(
            title="Completed Task",
            due_date=datetime.utcnow() - timedelta(hours=1),
            status="completed",
            user_id=uuid4()
        )
        assert not completed_task.is_overdue
    
    async def test_energy_level_matching(self):
        """Test energy level matching for ADHD task selection."""
        low_energy_task = Task(
            title="Low Energy Task",
            energy_level="low",
            estimated_duration=15,
            user_id=uuid4()
        )
        
        high_energy_task = Task(
            title="High Energy Task",
            energy_level="high",
            estimated_duration=90,
            user_id=uuid4()
        )
        
        # Low energy tasks should be shorter and less demanding
        assert low_energy_task.estimated_duration < high_energy_task.estimated_duration
        assert low_energy_task.energy_level == "low"
        assert high_energy_task.energy_level == "high"


if __name__ == "__main__":
    pytest.main([__file__])
