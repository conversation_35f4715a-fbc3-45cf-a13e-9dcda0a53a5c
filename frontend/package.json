{"name": "@chronos/frontend", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite --host 0.0.0.0 --port 3000", "build": "tsc && vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "type-check": "tsc --noEmit", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "adhd-audit": "node scripts/adhd-accessibility-audit.js"}, "dependencies": {"react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@storybook/addon-a11y": "^9.0.12", "@storybook/addon-essentials": "9.0.0-alpha.12", "@storybook/addon-interactions": "9.0.0-alpha.10", "@storybook/addon-links": "^9.0.12", "@storybook/react-vite": "^9.0.12", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "@vitest/coverage-v8": "^3.2.4", "@vitest/ui": "^3.2.4", "eslint": "^9.25.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "jsdom": "^26.1.0", "storybook": "^9.0.12", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5", "vitest": "^3.2.4", "zustand": "^5.0.5"}}