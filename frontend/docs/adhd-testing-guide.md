# ADHD Component Testing Guide

This guide outlines testing standards and patterns specifically designed for ADHD-optimized React components in Project Chronos.

## Overview

ADHD users have unique needs that require specialized testing approaches:
- **Cognitive Load Management**: Components should adapt to different cognitive states
- **Energy Level Adaptation**: Interface complexity should match user energy levels
- **Attention Span Considerations**: Components should support varying attention spans
- **Focus Management**: Proper keyboard navigation and focus handling
- **Performance Sensitivity**: Fast rendering and minimal delays

## Testing Categories

### 1. Basic Functionality Tests
Standard React component tests ensuring core functionality works.

```typescript
describe('Basic Functionality', () => {
  it('should render without crashing', () => {
    render(<YourComponent />);
    expect(screen.getByTestId('your-component')).toBeInTheDocument();
  });
});
```

### 2. Cognitive Load Management Tests
Test how components adapt to different cognitive load states.

```typescript
describe('Cognitive Load Management', () => {
  it('should simplify interface when cognitive load is high', () => {
    adhdTestUtils.simulateHighCognitiveLoad();
    render(<YourComponent />);
    
    const component = screen.getByTestId('your-component');
    expect(component).toHaveLowCognitiveLoad();
  });
});
```

### 3. Energy Level Adaptation Tests
Verify components respond appropriately to user energy levels.

```typescript
describe('Energy Level Adaptation', () => {
  it('should show minimal options when energy is low', () => {
    adhdTestUtils.simulateLowEnergy();
    render(<YourComponent />);
    
    // Check for simplified interface
    expect(screen.queryByText('Advanced Options')).not.toBeInTheDocument();
  });
});
```

### 4. Focus Mode Support Tests
Ensure components work well in focus mode.

```typescript
describe('Focus Mode Support', () => {
  it('should hide distracting elements in focus mode', () => {
    adhdTestUtils.simulateFocusMode();
    render(<YourComponent />);
    
    // Check that non-essential elements are hidden
    expect(screen.queryByTestId('decorative-animation')).not.toBeInTheDocument();
  });
});
```

### 5. Accessibility Tests
Verify ADHD-specific accessibility requirements.

```typescript
describe('Accessibility', () => {
  it('should support keyboard navigation', () => {
    const mockAction = vi.fn();
    render(<YourComponent onAction={mockAction} />);
    
    const button = screen.getByRole('button');
    fireEvent.keyDown(button, { key: 'Enter' });
    expect(mockAction).toHaveBeenCalled();
  });

  it('should have proper ARIA labels', () => {
    render(<YourComponent />);
    const button = screen.getByRole('button');
    expect(button).toBeADHDAccessible();
  });
});
```

### 6. Performance Tests
Ensure components meet ADHD performance requirements.

```typescript
describe('Performance', () => {
  it('should render quickly for ADHD users', async () => {
    const startTime = performance.now();
    render(<YourComponent />);
    const endTime = performance.now();
    
    // ADHD users benefit from fast rendering (< 100ms)
    expect(endTime - startTime).toBeLessThan(100);
  });
});
```

## Custom Matchers

### toBeADHDAccessible()
Checks if an element meets ADHD accessibility requirements:
- Has proper ARIA labels
- Supports keyboard navigation
- Has appropriate contrast ratios

### toHaveLowCognitiveLoad()
Verifies an element doesn't overwhelm ADHD users:
- Maximum 10 child elements
- Maximum 5 interactive elements
- Clear visual hierarchy

## ADHD Test Utilities

### Energy Level Simulation
```typescript
adhdTestUtils.simulateLowEnergy();    // Energy level: 2/10
adhdTestUtils.simulateHighEnergy();   // Energy level: 8/10
```

### Cognitive Load Simulation
```typescript
adhdTestUtils.simulateHighCognitiveLoad();  // High mental load
adhdTestUtils.simulateLowCognitiveLoad();   // Low mental load
```

### Focus Mode Simulation
```typescript
adhdTestUtils.simulateFocusMode();    // Focus mode enabled
adhdTestUtils.simulateNormalMode();   // Normal mode
```

### Attention Span Simulation
```typescript
adhdTestUtils.simulateShortAttentionSpan();  // Short attention span
adhdTestUtils.simulateLongAttentionSpan();   // Long attention span
```

### Motion Preferences
```typescript
adhdTestUtils.simulateReducedMotion();  // Prefers reduced motion
```

### Distraction Level
```typescript
adhdTestUtils.simulateHighDistraction();  // High distraction environment
adhdTestUtils.simulateLowDistraction();   // Low distraction environment
```

## Testing Patterns

### Progressive Disclosure Testing
```typescript
it('should implement progressive disclosure', () => {
  render(<ComplexForm />);
  
  // Initially show only essential fields
  expect(screen.getByLabelText('Name')).toBeInTheDocument();
  expect(screen.queryByLabelText('Advanced Setting')).not.toBeInTheDocument();
  
  // Show more options when requested
  fireEvent.click(screen.getByText('Show More Options'));
  expect(screen.getByLabelText('Advanced Setting')).toBeInTheDocument();
});
```

### Error Handling Testing
```typescript
it('should provide ADHD-friendly error messages', () => {
  render(<FormComponent />);
  
  // Trigger an error
  fireEvent.click(screen.getByText('Submit'));
  
  const errorMessage = screen.getByRole('alert');
  expect(errorMessage).toHaveTextContent(/clear and actionable message/i);
  expect(errorMessage).not.toHaveTextContent(/technical jargon/i);
});
```

### Loading State Testing
```typescript
it('should show progress indicators for long operations', async () => {
  render(<AsyncComponent />);
  
  fireEvent.click(screen.getByText('Load Data'));
  
  // Should show progress indicator
  expect(screen.getByRole('progressbar')).toBeInTheDocument();
  
  // Should show completion
  await waitFor(() => {
    expect(screen.getByText('Data loaded successfully')).toBeInTheDocument();
  });
});
```

## Best Practices

### 1. Test Real User Scenarios
- Test with actual ADHD user workflows
- Consider different times of day (energy levels vary)
- Test with distractions present

### 2. Performance Testing
- Always test render times
- Monitor memory usage
- Test with slow devices/connections

### 3. Accessibility Testing
- Test with screen readers
- Verify keyboard navigation
- Check color contrast

### 4. State Management Testing
- Test state persistence across sessions
- Verify preference storage
- Test state recovery after errors

### 5. Integration Testing
- Test component interactions
- Verify data flow between components
- Test with real API responses

## Running ADHD Tests

```bash
# Run all tests
pnpm test

# Run tests with coverage
pnpm test:coverage

# Run tests in UI mode
pnpm test:ui

# Run ADHD accessibility audit
pnpm adhd-audit
```

## Continuous Integration

Ensure all ADHD tests pass in CI:
- Performance benchmarks
- Accessibility checks
- Cognitive load validation
- Cross-browser testing

## Reporting

Generate ADHD-specific test reports:
- Cognitive load scores
- Performance metrics
- Accessibility compliance
- User experience ratings

## Next Steps

1. Implement user testing with ADHD individuals
2. Set up automated accessibility testing
3. Create performance monitoring dashboards
4. Establish ADHD UX metrics

For more information, see the main Project Chronos documentation.
