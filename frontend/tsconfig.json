{"compilerOptions": {"target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/components/*": ["./src/components/*"], "@/adhd/*": ["./src/components/adhd/*"], "@/hooks/*": ["./src/hooks/*"], "@/stores/*": ["./src/stores/*"], "@/services/*": ["./src/services/*"], "@/utils/*": ["./src/utils/*"], "@/types/*": ["./src/types/*"]}}, "include": ["src"]}