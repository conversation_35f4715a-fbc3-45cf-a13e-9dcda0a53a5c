# Traefik Configuration for ADHD-Optimized Project Chronos
# This is a reference configuration - adapt for your environment

# Global configuration
global:
  checkNewVersion: false
  sendAnonymousUsage: false

# API and dashboard configuration
api:
  dashboard: true
  debug: true

# Entry points
entryPoints:
  web:
    address: ":80"
    # Redirect HTTP to HTTPS in production
    http:
      redirections:
        entrypoint:
          to: websecure
          scheme: https
          permanent: true
  websecure:
    address: ":443"

# Certificate resolvers
certificatesResolvers:
  letsencrypt:
    acme:
      email: <EMAIL>
      storage: /letsencrypt/acme.json
      httpChallenge:
        entryPoint: web
      # Use DNS challenge for wildcard certificates
      # dnsChallenge:
      #   provider: cloudflare
      #   resolvers:
      #     - "*******:53"
      #     - "*******:53"

# Providers
providers:
  docker:
    endpoint: "unix:///var/run/docker.sock"
    exposedByDefault: false
    network: traefik
  file:
    filename: /etc/traefik/dynamic.yml
    watch: true

# Logging
log:
  level: INFO
  format: json

# Access logs with ADHD-specific fields
accessLog:
  format: json
  fields:
    defaultMode: keep
    names:
      ClientUsername: drop
    headers:
      defaultMode: keep
      names:
        User-Agent: keep
        Authorization: drop
        X-ADHD-Optimized: keep
        X-Cognitive-Load: keep
        X-Response-Time: keep

# Metrics
metrics:
  prometheus:
    addEntryPointsLabels: true
    addServicesLabels: true
    addRoutersLabels: true

# Pilot (optional - for Traefik Cloud)
# pilot:
#   token: "your-pilot-token"

# Tracing (optional)
# tracing:
#   jaeger:
#     samplingServerURL: http://jaeger:14268/api/sampling
#     localAgentHostPort: jaeger:6831
