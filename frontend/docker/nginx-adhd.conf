# ADHD-Optimized Nginx Configuration
# Designed for fast loading and reduced cognitive load

server {
    listen 80;
    server_name localhost;
    root /usr/share/nginx/html;
    index index.html;

    # ADHD-specific optimizations for immediate response
    
    # Aggressive caching for static assets to reduce wait times
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header X-ADHD-Cached "true";
        
        # Enable compression for faster loading
        gzip_static on;
        
        # Add CORS headers for ADHD-friendly cross-origin requests
        add_header Access-Control-Allow-Origin "*";
        add_header Access-Control-Allow-Methods "GET, OPTIONS";
        add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range";
    }

    # Compression configuration for ADHD users (faster loading)
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json
        application/xml
        image/svg+xml;

    # Security headers with ADHD considerations
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # ADHD-specific headers
    add_header X-ADHD-Optimized "true" always;
    add_header X-Cognitive-Load "managed" always;
    add_header X-Response-Time $request_time always;

    # Main SPA routing with ADHD optimization
    location / {
        try_files $uri $uri/ /index.html;
        
        # Add ADHD-specific headers for main app
        add_header X-ADHD-Route "spa" always;
        add_header Cache-Control "no-cache, no-store, must-revalidate" always;
        add_header Pragma "no-cache" always;
        add_header Expires "0" always;
        
        # Enable compression for HTML
        gzip on;
    }

    # Health check endpoint for ADHD performance monitoring
    location /health.json {
        access_log off;
        add_header Content-Type application/json;
        add_header X-ADHD-Health "ok" always;
        expires -1;
    }

    # Legacy health check endpoint
    location /health {
        access_log off;
        return 200 '{"status":"healthy","adhd_optimized":true}';
        add_header Content-Type application/json;
        add_header X-ADHD-Health "ok" always;
    }

    # API proxy configuration for development/staging
    location /api/ {
        # Proxy to backend API
        proxy_pass http://chronos-api:8000/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # ADHD-specific proxy headers
        proxy_set_header X-ADHD-Request "proxied";
        proxy_set_header X-Cognitive-Load-Context "api";
        
        # Timeout settings optimized for ADHD users (shorter timeouts)
        proxy_connect_timeout 5s;
        proxy_send_timeout 10s;
        proxy_read_timeout 10s;
        
        # Enable compression for API responses
        gzip on;
        gzip_proxied any;
    }

    # WebSocket proxy for real-time features
    location /ws/ {
        proxy_pass http://chronos-ws:8001/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # ADHD-specific WebSocket headers
        proxy_set_header X-ADHD-WebSocket "enabled";
        
        # WebSocket timeout settings
        proxy_read_timeout 86400;
    }

    # Error pages with ADHD-friendly messaging
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
    
    location = /404.html {
        internal;
        add_header X-ADHD-Error "not-found" always;
    }
    
    location = /50x.html {
        internal;
        add_header X-ADHD-Error "server-error" always;
    }

    # Disable access logs for static assets to reduce I/O
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        access_log off;
    }

    # Security: Deny access to sensitive files
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    location ~ ~$ {
        deny all;
        access_log off;
        log_not_found off;
    }
}
