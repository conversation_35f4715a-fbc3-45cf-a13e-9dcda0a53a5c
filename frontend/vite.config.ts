import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { resolve } from 'path'

export default defineConfig({
  plugins: [
    react(),
    // ADHD-specific build optimizations
    {
      name: 'adhd-bundle-analyzer',
      generateBundle(options, bundle) {
        // Analyze bundle for cognitive load impact
        for (const [fileName, chunk] of Object.entries(bundle)) {
          if (chunk.type === 'chunk') {
            const size = chunk.code.length;

            // Warn about large chunks that might impact ADHD users
            if (size > 100000) { // 100KB
              console.warn(`⚠️  Large chunk detected: ${fileName} (${Math.round(size/1024)}KB)`);
              console.warn('   Consider code splitting for better ADHD user experience');
            }
          }
        }
      }
    }
  ],
  resolve: {
    alias: {
      '@': resolve(__dirname, './src'),
      '@/components': resolve(__dirname, './src/components'),
      '@/adhd': resolve(__dirname, './src/components/adhd'),
      '@/hooks': resolve(__dirname, './src/hooks'),
      '@/stores': resolve(__dirname, './src/stores'),
      '@/services': resolve(__dirname, './src/services'),
      '@/utils': resolve(__dirname, './src/utils'),
      '@/types': resolve(__dirname, './src/types')
    }
  },
  server: {
    host: '0.0.0.0',
    port: 3000,
    hmr: {
      port: 3001
    }
  },
  build: {
    target: 'esnext',
    sourcemap: true,
    rollupOptions: {
      output: {
        manualChunks: {
          'adhd-components': ['./src/components/adhd'],
          'vendor': ['react', 'react-dom'],
          'utils': ['./src/utils']
        }
      }
    }
  },
  define: {
    __ADHD_OPTIMIZED__: true,
    __COGNITIVE_LOAD_TRACKING__: process.env.NODE_ENV === 'development'
  }
})
