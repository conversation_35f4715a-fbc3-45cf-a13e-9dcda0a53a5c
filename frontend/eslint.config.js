import js from '@eslint/js'
import globals from 'globals'
import reactHooks from 'eslint-plugin-react-hooks'
import reactRefresh from 'eslint-plugin-react-refresh'
import jsxA11y from 'eslint-plugin-jsx-a11y'
import tseslint from 'typescript-eslint'
import adhdAccessibility from './scripts/eslint-plugin-adhd-accessibility.js'

export default tseslint.config(
  { ignores: ['dist', 'node_modules'] },
  {
    extends: [
      js.configs.recommended,
      ...tseslint.configs.recommended
    ],
    files: ['**/*.{ts,tsx}'],
    languageOptions: {
      ecmaVersion: 2020,
      globals: globals.browser,
    },
    plugins: {
      'react-hooks': reactHooks,
      'react-refresh': reactRefresh,
      'jsx-a11y': jsxA11y,
      'adhd-accessibility': adhdAccessibility,
    },
    rules: {
      ...reactHooks.configs.recommended.rules,
      'react-refresh/only-export-components': [
        'warn',
        { allowConstantExport: true },
      ],
      // ADHD-specific rules
      'adhd-accessibility/cognitive-load-limit': ['warn', { max: 7 }],
      'adhd-accessibility/attention-span-warning': ['error', { maxElements: 15 }],
      'adhd-accessibility/focus-management': 'error',
      // JSX A11y rules (manually configured for flat config)
      'jsx-a11y/no-autofocus': 'off', // Allow autofocus for ADHD users
      'jsx-a11y/no-distracting-elements': 'error'
    },
  },
)
