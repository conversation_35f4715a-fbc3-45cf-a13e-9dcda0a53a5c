//#region src/composable-filters.d.ts
type StringOrRegExp = string | RegExp;
// Inline this type to avoid import it from `rolldown`.
// The only downside is we need to keep it in sync with `rolldown` manually,
// it is alright since it is pretty stable now.
type PluginModuleType = "js" | "jsx" | "ts" | "tsx" | "json" | "text" | "base64" | "dataurl" | "binary" | "empty" | (string & {});
type FilterExpressionKind = FilterExpression["kind"];
type FilterExpression = And | Or | Not | Id | ModuleType | Code | Query;
type TopLevelFilterExpression = Include | Exclude;
declare class And {
  kind: "and";
  args: FilterExpression[];
  constructor(...args: FilterExpression[]);
}
declare class Or {
  kind: "or";
  args: FilterExpression[];
  constructor(...args: FilterExpression[]);
}
declare class Not {
  kind: "not";
  expr: FilterExpression;
  constructor(expr: FilterExpression);
}
interface QueryFilterObject {
  [key: string]: StringOrRegExp | boolean;
}
interface IdParams {
  cleanUrl?: boolean;
}
declare class Id {
  kind: "id";
  pattern: StringOrRegExp;
  params: IdParams;
  constructor(pattern: StringOrRegExp, params?: IdParams);
}
declare class ModuleType {
  kind: "moduleType";
  pattern: PluginModuleType;
  constructor(pattern: PluginModuleType);
}
declare class Code {
  kind: "code";
  pattern: StringOrRegExp;
  constructor(expr: StringOrRegExp);
}
declare class Query {
  kind: "query";
  key: string;
  pattern: StringOrRegExp | boolean;
  constructor(key: string, pattern: StringOrRegExp | boolean);
}
declare class Include {
  kind: "include";
  expr: FilterExpression;
  constructor(expr: FilterExpression);
}
declare class Exclude {
  kind: "exclude";
  expr: FilterExpression;
  constructor(expr: FilterExpression);
}
declare function and(...args: FilterExpression[]): And;
declare function or(...args: FilterExpression[]): Or;
declare function not(expr: FilterExpression): Not;
declare function id(pattern: StringOrRegExp, params?: IdParams): Id;
declare function moduleType(pattern: PluginModuleType): ModuleType;
declare function code(pattern: StringOrRegExp): Code;
/*
* There are three kinds of conditions are supported:
* 1. `boolean`: if the value is `true`, the key must exist and be truthy. if the value is `false`, the key must not exist or be falsy.
* 2. `string`: the key must exist and be equal to the value.
* 3. `RegExp`: the key must exist and match the value.
*/
declare function query(key: string, pattern: StringOrRegExp | boolean): Query;
declare function include(expr: FilterExpression): Include;
declare function exclude(expr: FilterExpression): Exclude;
/**
* convert a queryObject to FilterExpression like
* ```js
*   and(query(k1, v1), query(k2, v2))
* ```
* @param queryFilterObject The query filter object needs to be matched.
* @returns a `And` FilterExpression
*/
declare function queries(queryFilter: QueryFilterObject): And;
declare function interpreter(exprs: TopLevelFilterExpression | TopLevelFilterExpression[], code?: string, id?: string, moduleType?: PluginModuleType): boolean;
interface InterpreterCtx {
  urlSearchParamsCache?: URLSearchParams;
}
declare function interpreterImpl(expr: TopLevelFilterExpression[], code?: string, id?: string, moduleType?: PluginModuleType, ctx?: InterpreterCtx): boolean;
declare function exprInterpreter(expr: FilterExpression, code?: string, id?: string, moduleType?: PluginModuleType, ctx?: InterpreterCtx): boolean;
//#endregion
//#region src/simple-filters.d.ts
/**
* Constructs a RegExp that matches the exact string specified.
*
* This is useful for plugin hook filters.
*
* @param str the string to match.
* @param flags flags for the RegExp.
*/
declare function exactRegex(str: string, flags?: string): RegExp;
/**
* Constructs a RegExp that matches a value that has the specified prefix.
*
* This is useful for plugin hook filters.
*
* @param str the string to match.
* @param flags flags for the RegExp.
*/
declare function prefixRegex(str: string, flags?: string): RegExp;
type WidenString<T> = T extends string ? string : T;
/**
* Converts a id filter to match with an id with a query.
*
* @param input the id filters to convert.
*/
declare function makeIdFiltersToMatchWithQuery<T extends string | RegExp>(input: T): WidenString<T>;
declare function makeIdFiltersToMatchWithQuery<T extends string | RegExp>(input: readonly T[]): WidenString<T>[];
declare function makeIdFiltersToMatchWithQuery(input: string | RegExp | readonly (string | RegExp)[]): string | RegExp | (string | RegExp)[];
//#endregion
export { FilterExpression, FilterExpressionKind, QueryFilterObject, TopLevelFilterExpression, and, code, exactRegex, exclude, exprInterpreter, id, include, interpreter, interpreterImpl, makeIdFiltersToMatchWithQuery, moduleType, not, or, prefixRegex, queries, query };