export type Options = [
    {
        lib?: 'always' | 'never';
        path?: 'always' | 'never';
        types?: 'always' | 'never' | 'prefer-import';
    }
];
export type MessageIds = 'tripleSlashReference';
declare const _default: import("@typescript-eslint/utils/ts-eslint").RuleModule<"tripleSlashReference", Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
export default _default;
//# sourceMappingURL=triple-slash-reference.d.ts.map