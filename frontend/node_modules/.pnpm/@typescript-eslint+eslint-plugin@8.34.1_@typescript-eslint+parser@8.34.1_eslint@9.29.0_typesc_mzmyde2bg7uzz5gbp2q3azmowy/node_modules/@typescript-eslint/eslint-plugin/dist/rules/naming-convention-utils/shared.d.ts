import type { IndividualAndMetaSelectorsString, MetaSelectorsString, Selectors, SelectorsString } from './enums';
import { MetaSelectors } from './enums';
export declare function selectorTypeToMessageString(selectorType: SelectorsString): string;
export declare function isMetaSelector(selector: IndividualAndMetaSelectorsString | MetaSelectors | Selectors): selector is MetaSelectorsString;
export declare function isMethodOrPropertySelector(selector: IndividualAndMetaSelectorsString | MetaSelectors | Selectors): boolean;
//# sourceMappingURL=shared.d.ts.map