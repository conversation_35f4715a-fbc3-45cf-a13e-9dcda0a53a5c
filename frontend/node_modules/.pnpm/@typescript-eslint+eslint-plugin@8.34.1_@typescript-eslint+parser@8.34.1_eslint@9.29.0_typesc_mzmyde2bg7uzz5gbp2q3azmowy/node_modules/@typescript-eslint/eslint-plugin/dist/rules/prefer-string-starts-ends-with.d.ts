import type { TSESLint } from '@typescript-eslint/utils';
type AllowedSingleElementEquality = 'always' | 'never';
export type Options = [
    {
        allowSingleElementEquality?: AllowedSingleElementEquality;
    }
];
export type MessageIds = 'preferEndsWith' | 'preferStartsWith';
declare const _default: TSESLint.RuleModule<MessageIds, Options, import("../../rules").ESLintPluginDocs, TSESLint.RuleListener>;
export default _default;
//# sourceMappingURL=prefer-string-starts-ends-with.d.ts.map