import type { TSESLint } from '@typescript-eslint/utils';
export type MessageIds = 'preferIndexSignature' | 'preferIndexSignatureSuggestion' | 'preferRecord';
export type Options = ['index-signature' | 'record'];
declare const _default: TSESLint.RuleModule<MessageIds, Options, import("../../rules").ESLintPluginDocs, TSESLint.RuleListener>;
export default _default;
//# sourceMappingURL=consistent-indexed-object-style.d.ts.map