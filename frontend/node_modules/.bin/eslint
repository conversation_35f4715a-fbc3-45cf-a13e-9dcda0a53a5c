#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/home/<USER>/dev/10Baht/Day1-Experiment-Chronos/frontend/node_modules/.pnpm/eslint@9.29.0/node_modules/eslint/bin/node_modules:/home/<USER>/dev/10Baht/Day1-Experiment-Chronos/frontend/node_modules/.pnpm/eslint@9.29.0/node_modules/eslint/node_modules:/home/<USER>/dev/10Baht/Day1-Experiment-Chronos/frontend/node_modules/.pnpm/eslint@9.29.0/node_modules:/home/<USER>/dev/10Baht/Day1-Experiment-Chronos/frontend/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/home/<USER>/dev/10Baht/Day1-Experiment-Chronos/frontend/node_modules/.pnpm/eslint@9.29.0/node_modules/eslint/bin/node_modules:/home/<USER>/dev/10Baht/Day1-Experiment-Chronos/frontend/node_modules/.pnpm/eslint@9.29.0/node_modules/eslint/node_modules:/home/<USER>/dev/10Baht/Day1-Experiment-Chronos/frontend/node_modules/.pnpm/eslint@9.29.0/node_modules:/home/<USER>/dev/10Baht/Day1-Experiment-Chronos/frontend/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../eslint/bin/eslint.js" "$@"
else
  exec node  "$basedir/../eslint/bin/eslint.js" "$@"
fi
