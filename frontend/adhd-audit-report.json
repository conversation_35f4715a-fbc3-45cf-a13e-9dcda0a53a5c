{"cognitiveLoad": [{"file": "./src/App.tsx", "score": 7.5, "recommendation": "Break into smaller components or simplify logic"}, {"file": "./src/test/templates/adhd-component.test.tsx", "score": 17, "recommendation": "Break into smaller components or simplify logic"}], "attentionSpan": [], "focusManagement": [{"file": "./src/App.tsx", "issues": ["1 click handlers without keyboard support", "1 buttons without ARIA labels"], "recommendation": "Improve keyboard navigation and focus management"}], "recommendations": ["Consider using React.memo() for complex components to reduce re-renders", "Implement progressive disclosure patterns for complex interfaces", "Use focus-trap-react for modal components", "Implement consistent keyboard navigation patterns"], "summary": {"totalFiles": 3, "issuesFound": 3, "score": 80}}