import '@testing-library/jest-dom';
import { vi, beforeEach, expect } from 'vitest';

// Mock ADHD-specific APIs and browser features
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
  matches: false,
  media: query,
  onchange: null,
  addListener: vi.fn(),
  removeListener: vi.fn(),
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  dispatchEvent: vi.fn(),
  }))
});

// Mock Intersection Observer for ADHD attention tracking
Object.defineProperty(window, 'IntersectionObserver', {
  writable: true,
  value: vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
  }))
});

// Mock Web Speech API for ADHD audio features
Object.defineProperty(window, 'speechSynthesis', {
  value: {
    speak: vi.fn(),
    cancel: vi.fn(),
    pause: vi.fn(),
    resume: vi.fn(),
    getVoices: vi.fn().mockReturnValue([]),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
  },
  writable: true,
});

// Mock ResizeObserver for responsive ADHD components
Object.defineProperty(window, 'ResizeObserver', {
  writable: true,
  value: vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
  }))
});

// Mock localStorage for ADHD preferences
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

// Mock sessionStorage for temporary ADHD state
const sessionStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};
Object.defineProperty(window, 'sessionStorage', {
  value: sessionStorageMock,
});

// ADHD-specific test utilities
export const adhdTestUtils = {
  // Simulate different energy levels
  simulateLowEnergy: () => {
    localStorageMock.getItem.mockImplementation((key: string) => {
      if (key === 'adhd-energy-level') return '2';
      return null;
    });
  },
  
  simulateHighEnergy: () => {
    localStorageMock.getItem.mockImplementation((key: string) => {
      if (key === 'adhd-energy-level') return '8';
      return null;
    });
  },
  
  // Simulate cognitive load states
  simulateHighCognitiveLoad: () => {
    localStorageMock.getItem.mockImplementation((key: string) => {
      if (key === 'adhd-cognitive-load') return 'high';
      return null;
    });
  },
  
  simulateLowCognitiveLoad: () => {
    localStorageMock.getItem.mockImplementation((key: string) => {
      if (key === 'adhd-cognitive-load') return 'low';
      return null;
    });
  },
  
  // Simulate focus mode
  simulateFocusMode: () => {
    localStorageMock.getItem.mockImplementation((key: string) => {
      if (key === 'adhd-focus-mode') return 'true';
      return null;
    });
  },
  
  simulateNormalMode: () => {
    localStorageMock.getItem.mockImplementation((key: string) => {
      if (key === 'adhd-focus-mode') return 'false';
      return null;
    });
  },
  
  // Simulate attention span preferences
  simulateShortAttentionSpan: () => {
    localStorageMock.getItem.mockImplementation((key: string) => {
      if (key === 'adhd-attention-span') return 'short';
      return null;
    });
  },
  
  simulateLongAttentionSpan: () => {
    localStorageMock.getItem.mockImplementation((key: string) => {
      if (key === 'adhd-attention-span') return 'long';
      return null;
    });
  },
  
  // Simulate motion preferences
  simulateReducedMotion: () => {
    Object.defineProperty(window, 'matchMedia', {
      writable: true,
      value: vi.fn().mockImplementation(query => ({
        matches: query === '(prefers-reduced-motion: reduce)',
        media: query,
        onchange: null,
        addListener: vi.fn(),
        removeListener: vi.fn(),
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn(),
      }))
    });
  },
  
  // Simulate distraction level
  simulateHighDistraction: () => {
    localStorageMock.getItem.mockImplementation((key: string) => {
      if (key === 'adhd-distraction-level') return 'high';
      return null;
    });
  },
  
  simulateLowDistraction: () => {
    localStorageMock.getItem.mockImplementation((key: string) => {
      if (key === 'adhd-distraction-level') return 'low';
      return null;
    });
  },
  
  // Reset all ADHD state
  resetADHDState: () => {
    localStorageMock.getItem.mockReturnValue(null);
    sessionStorageMock.getItem.mockReturnValue(null);
    Object.defineProperty(window, 'matchMedia', {
      writable: true,
      value: vi.fn().mockImplementation(query => ({
        matches: false,
        media: query,
        onchange: null,
        addListener: vi.fn(),
        removeListener: vi.fn(),
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn(),
      }))
    });
  },
  
  // Utility to wait for ADHD-related async operations
  waitForADHDUpdate: async (timeout = 1000) => {
    return new Promise(resolve => setTimeout(resolve, timeout));
  },
  
  // Mock ADHD performance metrics
  mockPerformanceMetrics: () => {
    Object.defineProperty(window, 'performance', {
      value: {
        now: vi.fn().mockReturnValue(Date.now()),
        mark: vi.fn(),
        measure: vi.fn(),
        getEntriesByType: vi.fn().mockReturnValue([]),
        getEntriesByName: vi.fn().mockReturnValue([]),
      },
      writable: true,
    });
  }
};

// Global test setup
beforeEach(() => {
  // Reset all mocks before each test
  vi.clearAllMocks();
  adhdTestUtils.resetADHDState();
});

// Extend expect with custom matchers
declare module 'vitest' {
  interface Assertion {
    toBeADHDAccessible(): any;
    toHaveLowCognitiveLoad(): any;
  }
}

// Add custom matchers for ADHD testing
expect.extend({
  toBeADHDAccessible(received: HTMLElement) {
    // Custom matcher to check ADHD accessibility
    const hasAriaLabel = received.getAttribute('aria-label') || received.getAttribute('aria-labelledby');
    const hasKeyboardSupport = received.getAttribute('tabindex') !== null ||
                              ['button', 'a', 'input', 'select', 'textarea'].includes(received.tagName.toLowerCase());

    const pass = Boolean(hasAriaLabel && hasKeyboardSupport);

    return {
      message: () =>
        pass
          ? `Expected element not to be ADHD accessible`
          : `Expected element to be ADHD accessible (needs aria-label and keyboard support)`,
      pass,
    };
  },

  toHaveLowCognitiveLoad(received: HTMLElement) {
    // Custom matcher to check cognitive load
    const childCount = received.children.length;
    const interactiveElements = received.querySelectorAll('button, a, input, select, textarea').length;

    const pass = childCount <= 10 && interactiveElements <= 5;

    return {
      message: () =>
        pass
          ? `Expected element to have high cognitive load`
          : `Expected element to have low cognitive load (max 10 children, 5 interactive elements)`,
      pass,
    };
  }
});
