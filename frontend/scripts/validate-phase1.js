#!/usr/bin/env node

// Phase 1 Validation Script
// Validates all success criteria for Phase 1: Foundation Infrastructure

import { readFileSync, existsSync, statSync } from 'fs';
import { join } from 'path';
import { execSync } from 'child_process';

class Phase1Validator {
  constructor() {
    this.results = {
      passed: 0,
      failed: 0,
      warnings: 0,
      details: []
    };
  }

  async validate() {
    console.log('🧠 Validating Phase 1: Foundation Infrastructure');
    console.log('==============================================\n');

    // Technical Infrastructure Validation
    await this.validateTechnicalInfrastructure();
    
    // ADHD Development Tools Validation
    await this.validateADHDTools();
    
    // Performance & Quality Validation
    await this.validatePerformanceQuality();
    
    // Documentation Validation
    await this.validateDocumentation();

    // Generate final report
    this.generateReport();
  }

  async validateTechnicalInfrastructure() {
    console.log('🔧 Technical Infrastructure');
    console.log('---------------------------');

    // React 18+ with TypeScript 5.0+
    this.checkReactVersion();
    this.checkTypeScriptVersion();
    
    // Vite 4.0+ build system
    this.checkViteVersion();
    
    // pnpm package manager
    this.checkPnpmSetup();
    
    // Docker containers
    this.checkDockerSetup();
    
    // Traefik integration
    this.checkTraefikSetup();
    
    console.log('');
  }

  async validateADHDTools() {
    console.log('🧠 ADHD Development Tools');
    console.log('-------------------------');

    // Custom ESLint plugin
    this.checkESLintPlugin();
    
    // Performance monitoring
    this.checkPerformanceMonitoring();
    
    // Accessibility audit tools
    this.checkAccessibilityTools();
    
    // Testing framework
    this.checkTestingFramework();
    
    console.log('');
  }

  async validatePerformanceQuality() {
    console.log('⚡ Performance & Quality');
    console.log('-----------------------');

    // Build performance
    this.checkBuildPerformance();
    
    // Bundle optimization
    this.checkBundleOptimization();
    
    // Test coverage
    this.checkTestCoverage();
    
    console.log('');
  }

  async validateDocumentation() {
    console.log('📚 Documentation');
    console.log('----------------');

    // Setup documentation
    this.checkSetupDocs();
    
    // ADHD guidelines
    this.checkADHDGuidelines();
    
    // API documentation
    this.checkAPIDocumentation();
    
    console.log('');
  }

  checkReactVersion() {
    try {
      const packageJson = JSON.parse(readFileSync('package.json', 'utf-8'));
      const reactVersion = packageJson.dependencies?.react || packageJson.devDependencies?.react;
      
      if (reactVersion && reactVersion.includes('19')) {
        this.pass('React 19+ installed');
      } else {
        this.fail(`React version issue: ${reactVersion || 'not found'}`);
      }
    } catch (error) {
      this.fail('Could not check React version');
    }
  }

  checkTypeScriptVersion() {
    try {
      const packageJson = JSON.parse(readFileSync('package.json', 'utf-8'));
      const tsVersion = packageJson.devDependencies?.typescript;
      
      if (tsVersion && (tsVersion.includes('5.') || tsVersion.includes('^5'))) {
        this.pass('TypeScript 5.0+ installed');
      } else {
        this.fail(`TypeScript version issue: ${tsVersion || 'not found'}`);
      }
    } catch (error) {
      this.fail('Could not check TypeScript version');
    }
  }

  checkViteVersion() {
    try {
      const packageJson = JSON.parse(readFileSync('package.json', 'utf-8'));
      const viteVersion = packageJson.devDependencies?.vite;
      
      if (viteVersion && (viteVersion.includes('6.') || viteVersion.includes('^6'))) {
        this.pass('Vite 6.0+ installed');
      } else {
        this.fail(`Vite version issue: ${viteVersion || 'not found'}`);
      }
    } catch (error) {
      this.fail('Could not check Vite version');
    }
  }

  checkPnpmSetup() {
    if (existsSync('pnpm-lock.yaml')) {
      this.pass('pnpm package manager configured');
    } else {
      this.fail('pnpm-lock.yaml not found');
    }
  }

  checkDockerSetup() {
    const dockerFiles = [
      'docker/Dockerfile.dev',
      'docker/Dockerfile.prod',
      'docker-compose.dev.yml',
      'docker-compose.prod.yml'
    ];

    let dockerCount = 0;
    dockerFiles.forEach(file => {
      if (existsSync(file)) {
        dockerCount++;
      }
    });

    if (dockerCount === dockerFiles.length) {
      this.pass('Docker configuration complete');
    } else {
      this.fail(`Docker setup incomplete: ${dockerCount}/${dockerFiles.length} files found`);
    }
  }

  checkTraefikSetup() {
    const traefikFiles = [
      'docker/docker-compose.traefik.yml',
      'docker/traefik.yml',
      'docker/dynamic.yml'
    ];

    let traefikCount = 0;
    traefikFiles.forEach(file => {
      if (existsSync(file)) {
        traefikCount++;
      }
    });

    if (traefikCount === traefikFiles.length) {
      this.pass('Traefik integration configured');
    } else {
      this.warn(`Traefik setup: ${traefikCount}/${traefikFiles.length} files found`);
    }
  }

  checkESLintPlugin() {
    if (existsSync('scripts/eslint-plugin-adhd-accessibility.js')) {
      this.pass('ADHD ESLint plugin created');
    } else {
      this.fail('ADHD ESLint plugin not found');
    }
  }

  checkPerformanceMonitoring() {
    const perfFiles = [
      'scripts/vite-plugin-adhd-performance.ts',
      'src/components/adhd/ADHDPerformanceMonitor.tsx',
      'scripts/adhd-bundle-analyzer.js'
    ];

    let perfCount = 0;
    perfFiles.forEach(file => {
      if (existsSync(file)) {
        perfCount++;
      }
    });

    if (perfCount === perfFiles.length) {
      this.pass('Performance monitoring tools implemented');
    } else {
      this.fail(`Performance tools incomplete: ${perfCount}/${perfFiles.length} files found`);
    }
  }

  checkAccessibilityTools() {
    if (existsSync('scripts/adhd-accessibility-audit.js')) {
      this.pass('ADHD accessibility audit tool created');
    } else {
      this.fail('ADHD accessibility audit tool not found');
    }
  }

  checkTestingFramework() {
    const testFiles = [
      'vitest.config.ts',
      'src/test/setup.ts',
      'src/test/templates/adhd-component.test.tsx'
    ];

    let testCount = 0;
    testFiles.forEach(file => {
      if (existsSync(file)) {
        testCount++;
      }
    });

    if (testCount === testFiles.length) {
      this.pass('ADHD testing framework configured');
    } else {
      this.fail(`Testing framework incomplete: ${testCount}/${testFiles.length} files found`);
    }
  }

  checkBuildPerformance() {
    try {
      const startTime = Date.now();
      execSync('pnpm build', { stdio: 'pipe' });
      const buildTime = Date.now() - startTime;

      if (buildTime < 30000) { // 30 seconds
        this.pass(`Build performance good: ${buildTime}ms`);
      } else {
        this.warn(`Build time slow: ${buildTime}ms`);
      }
    } catch (error) {
      this.fail('Build failed');
    }
  }

  checkBundleOptimization() {
    if (existsSync('adhd-performance-report.json')) {
      try {
        const report = JSON.parse(readFileSync('adhd-performance-report.json', 'utf-8'));
        const score = report.adhdOptimizationScore;

        if (score >= 80) {
          this.pass(`ADHD optimization score: ${score}/100`);
        } else {
          this.warn(`ADHD optimization score needs improvement: ${score}/100`);
        }
      } catch (error) {
        this.warn('Could not read ADHD performance report');
      }
    } else {
      this.warn('ADHD performance report not found - run build first');
    }
  }

  checkTestCoverage() {
    try {
      execSync('pnpm vitest run', { stdio: 'pipe' });
      this.pass('All tests passing');
    } catch (error) {
      this.fail('Some tests failing');
    }
  }

  checkSetupDocs() {
    const docFiles = [
      'docs/traefik-setup.md',
      'docs/adhd-testing-guide.md'
    ];

    let docCount = 0;
    docFiles.forEach(file => {
      if (existsSync(file)) {
        docCount++;
      }
    });

    if (docCount === docFiles.length) {
      this.pass('Setup documentation complete');
    } else {
      this.warn(`Documentation incomplete: ${docCount}/${docFiles.length} files found`);
    }
  }

  checkADHDGuidelines() {
    if (existsSync('docs/adhd-testing-guide.md')) {
      this.pass('ADHD testing guidelines documented');
    } else {
      this.fail('ADHD testing guidelines not found');
    }
  }

  checkAPIDocumentation() {
    // Check if components are documented
    const componentDirs = ['src/components/adhd'];
    let hasComponents = false;

    componentDirs.forEach(dir => {
      if (existsSync(dir)) {
        hasComponents = true;
      }
    });

    if (hasComponents) {
      this.pass('ADHD components structure created');
    } else {
      this.warn('ADHD components directory not found');
    }
  }

  pass(message) {
    console.log(`✅ ${message}`);
    this.results.passed++;
    this.results.details.push({ type: 'pass', message });
  }

  fail(message) {
    console.log(`❌ ${message}`);
    this.results.failed++;
    this.results.details.push({ type: 'fail', message });
  }

  warn(message) {
    console.log(`⚠️  ${message}`);
    this.results.warnings++;
    this.results.details.push({ type: 'warn', message });
  }

  generateReport() {
    const total = this.results.passed + this.results.failed + this.results.warnings;
    const successRate = Math.round((this.results.passed / total) * 100);

    console.log('📊 Phase 1 Validation Summary');
    console.log('=============================');
    console.log(`✅ Passed: ${this.results.passed}`);
    console.log(`❌ Failed: ${this.results.failed}`);
    console.log(`⚠️  Warnings: ${this.results.warnings}`);
    console.log(`📈 Success Rate: ${successRate}%\n`);

    if (this.results.failed === 0) {
      console.log('🎉 Phase 1 validation PASSED!');
      console.log('Ready to proceed to Phase 2: Core ADHD Components\n');
    } else {
      console.log('❌ Phase 1 validation FAILED!');
      console.log('Please address the failed items before proceeding.\n');
    }

    if (this.results.warnings > 0) {
      console.log('⚠️  Consider addressing warnings for optimal setup.\n');
    }

    console.log('🧠 ADHD-optimized foundation infrastructure validation complete!');
  }
}

// Run validation if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  new Phase1Validator().validate().catch(console.error);
}

export default Phase1Validator;
