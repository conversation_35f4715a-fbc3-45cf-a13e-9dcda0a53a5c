#!/usr/bin/env node

// ADHD Accessibility Audit Script
// Analyzes React components for ADHD-friendly patterns

import { readdir, readFile, writeFile } from 'fs/promises';
import { join, extname } from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

class ADHDAudit {
  constructor() {
    this.results = {
      cognitiveLoad: [],
      attentionSpan: [],
      focusManagement: [],
      recommendations: [],
      summary: {
        totalFiles: 0,
        issuesFound: 0,
        score: 0
      }
    };
  }

  async auditProject() {
    console.log('🧠 Starting ADHD Accessibility Audit...\n');
    
    const components = await this.findComponents();
    this.results.summary.totalFiles = components.length;
    
    for (const component of components) {
      try {
        const content = await readFile(component, 'utf-8');
        await this.auditComponent(component, content);
      } catch (error) {
        console.warn(`⚠️  Could not audit ${component}: ${error.message}`);
      }
    }
    
    await this.generateReport();
  }

  async findComponents() {
    const components = [];
    
    async function scanDirectory(dir) {
      try {
        const entries = await readdir(dir, { withFileTypes: true });
        
        for (const entry of entries) {
          const fullPath = join(dir, entry.name);
          
          if (entry.isDirectory() && !entry.name.startsWith('.') && entry.name !== 'node_modules') {
            await scanDirectory(fullPath);
          } else if (entry.isFile() && ['.tsx', '.jsx'].includes(extname(entry.name))) {
            components.push(fullPath);
          }
        }
      } catch (error) {
        // Skip directories we can't read
      }
    }
    
    await scanDirectory(join(__dirname, '../src'));
    return components;
  }

  async auditComponent(filePath, content) {
    const relativePath = filePath.replace(process.cwd(), '.');
    
    // Analyze cognitive load
    const cognitiveLoad = this.analyzeCognitiveLoad(content);
    if (cognitiveLoad > 7) {
      this.results.cognitiveLoad.push({
        file: relativePath,
        score: cognitiveLoad,
        recommendation: 'Break into smaller components or simplify logic'
      });
      this.results.summary.issuesFound++;
    }
    
    // Check attention span considerations
    const attentionSpan = this.analyzeAttentionSpan(content);
    if (attentionSpan.risk === 'high') {
      this.results.attentionSpan.push({
        file: relativePath,
        issues: attentionSpan.issues,
        recommendation: 'Add progressive disclosure or reduce visual complexity'
      });
      this.results.summary.issuesFound++;
    }
    
    // Verify focus management
    const focusIssues = this.analyzeFocusManagement(content);
    if (focusIssues.length > 0) {
      this.results.focusManagement.push({
        file: relativePath,
        issues: focusIssues,
        recommendation: 'Improve keyboard navigation and focus management'
      });
      this.results.summary.issuesFound++;
    }
  }

  analyzeCognitiveLoad(content) {
    let score = 0;
    
    // Count decision points (weighted for ADHD impact)
    score += (content.match(/if\s*\(/g) || []).length * 2;
    score += (content.match(/\?\s*:/g) || []).length * 1.5;
    score += (content.match(/switch\s*\(/g) || []).length * 3;
    score += (content.match(/&&/g) || []).length * 1;
    score += (content.match(/\|\|/g) || []).length * 1;
    
    // Count visual elements (JSX tags)
    score += (content.match(/<\w+/g) || []).length * 0.5;
    
    // Count state variables (cognitive overhead)
    score += (content.match(/useState|useReducer/g) || []).length * 1;
    score += (content.match(/useEffect/g) || []).length * 1.5;
    
    // Count props (interface complexity)
    const propsMatch = content.match(/interface\s+\w+Props\s*{([^}]*)}/);
    if (propsMatch) {
      const propsCount = (propsMatch[1].match(/\w+\s*:/g) || []).length;
      score += propsCount * 0.3;
    }
    
    return Math.round(score * 10) / 10;
  }

  analyzeAttentionSpan(content) {
    const issues = [];
    let risk = 'low';
    
    // Check for long lists without virtualization
    if (content.includes('.map(') && !content.includes('react-window') && !content.includes('react-virtualized')) {
      const mapCount = (content.match(/\.map\(/g) || []).length;
      if (mapCount > 2) {
        issues.push(`Multiple list renderings (${mapCount}) without virtualization`);
        risk = 'medium';
      }
    }
    
    // Check for auto-playing content
    if (content.includes('autoplay') || content.includes('autoPlay')) {
      issues.push('Auto-playing content detected');
      risk = 'high';
    }
    
    // Check for excessive animations
    const animationCount = (content.match(/animate|transition|motion\.|framer-motion/g) || []).length;
    if (animationCount > 5) {
      issues.push(`Excessive animations (${animationCount})`);
      risk = 'medium';
    }
    
    // Check for complex forms
    const inputCount = (content.match(/<input|<select|<textarea/g) || []).length;
    if (inputCount > 8) {
      issues.push(`Complex form with ${inputCount} inputs - consider multi-step approach`);
      risk = 'high';
    }
    
    // Check for information density
    const textElements = (content.match(/<p|<span|<div.*>.*text|<h[1-6]/g) || []).length;
    if (textElements > 20) {
      issues.push(`High information density (${textElements} text elements)`);
      risk = risk === 'high' ? 'high' : 'medium';
    }
    
    return { risk, issues };
  }

  analyzeFocusManagement(content) {
    const issues = [];
    
    // Check for modals without focus trap
    if ((content.includes('modal') || content.includes('Modal')) && 
        !content.includes('focus-trap') && !content.includes('focusTrap')) {
      issues.push('Modal without focus trap');
    }
    
    // Check for click handlers without keyboard support
    if (content.includes('onClick') && !content.includes('onKeyDown') && !content.includes('onKeyPress')) {
      const clickCount = (content.match(/onClick/g) || []).length;
      const keyCount = (content.match(/onKey(Down|Press|Up)/g) || []).length;
      if (clickCount > keyCount) {
        issues.push(`${clickCount - keyCount} click handlers without keyboard support`);
      }
    }
    
    // Check for missing ARIA labels on interactive elements
    if (content.includes('<button') && !content.includes('aria-label') && !content.includes('aria-labelledby')) {
      const buttonCount = (content.match(/<button/g) || []).length;
      const ariaCount = (content.match(/aria-label/g) || []).length;
      if (buttonCount > ariaCount) {
        issues.push(`${buttonCount - ariaCount} buttons without ARIA labels`);
      }
    }
    
    return issues;
  }

  async generateReport() {
    const totalIssues = this.results.summary.issuesFound;
    const totalFiles = this.results.summary.totalFiles;
    const score = totalFiles > 0 ? Math.max(0, 100 - (totalIssues / totalFiles * 20)) : 100;
    
    this.results.summary.score = Math.round(score);
    
    // Console output
    console.log('📊 ADHD Accessibility Audit Results');
    console.log('=====================================\n');
    
    console.log(`📁 Files audited: ${totalFiles}`);
    console.log(`⚠️  Issues found: ${totalIssues}`);
    console.log(`🎯 ADHD Score: ${this.results.summary.score}/100\n`);
    
    if (this.results.cognitiveLoad.length > 0) {
      console.log('🧠 Cognitive Load Issues:');
      this.results.cognitiveLoad.forEach(issue => {
        console.log(`   ${issue.file} (score: ${issue.score}) - ${issue.recommendation}`);
      });
      console.log();
    }
    
    if (this.results.attentionSpan.length > 0) {
      console.log('👁️  Attention Span Issues:');
      this.results.attentionSpan.forEach(issue => {
        console.log(`   ${issue.file}:`);
        issue.issues.forEach(i => console.log(`     - ${i}`));
        console.log(`     💡 ${issue.recommendation}`);
      });
      console.log();
    }
    
    if (this.results.focusManagement.length > 0) {
      console.log('🎯 Focus Management Issues:');
      this.results.focusManagement.forEach(issue => {
        console.log(`   ${issue.file}:`);
        issue.issues.forEach(i => console.log(`     - ${i}`));
        console.log(`     💡 ${issue.recommendation}`);
      });
      console.log();
    }
    
    // Generate recommendations
    this.generateRecommendations();
    
    if (this.results.recommendations.length > 0) {
      console.log('💡 General Recommendations:');
      this.results.recommendations.forEach(rec => {
        console.log(`   - ${rec}`);
      });
      console.log();
    }
    
    // Save detailed report
    await this.saveDetailedReport();
    
    console.log('📄 Detailed report saved to: adhd-audit-report.json');
    console.log('🎉 Audit complete!\n');
  }

  generateRecommendations() {
    const recs = this.results.recommendations;
    
    if (this.results.cognitiveLoad.length > 0) {
      recs.push('Consider using React.memo() for complex components to reduce re-renders');
      recs.push('Implement progressive disclosure patterns for complex interfaces');
    }
    
    if (this.results.attentionSpan.length > 0) {
      recs.push('Add loading states and skeleton screens to manage attention');
      recs.push('Implement "focus mode" toggles to hide non-essential UI elements');
    }
    
    if (this.results.focusManagement.length > 0) {
      recs.push('Use focus-trap-react for modal components');
      recs.push('Implement consistent keyboard navigation patterns');
    }
    
    if (this.results.summary.score < 80) {
      recs.push('Consider ADHD user testing sessions');
      recs.push('Implement user preference settings for cognitive load management');
    }
  }

  async saveDetailedReport() {
    const reportPath = join(process.cwd(), 'adhd-audit-report.json');
    await writeFile(reportPath, JSON.stringify(this.results, null, 2));
  }
}

// Run audit if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  new ADHDAudit().auditProject().catch(console.error);
}

export default ADHDAudit;
