#!/bin/bash

# ADHD-Optimized Docker Build Script
# Builds Docker images with performance optimizations for ADHD users

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="chronos-frontend"
REGISTRY="${DOCKER_REGISTRY:-localhost:5000}"
VERSION="${VERSION:-latest}"
BUILD_DATE=$(date -u +'%Y-%m-%dT%H:%M:%SZ')
GIT_COMMIT=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")

echo -e "${BLUE}🧠 ADHD-Optimized Docker Build for Project Chronos${NC}"
echo -e "${BLUE}=================================================${NC}"
echo ""

# Function to print status
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to build image
build_image() {
    local dockerfile=$1
    local tag_suffix=$2
    local context=${3:-.}
    
    local image_name="${REGISTRY}/${PROJECT_NAME}:${VERSION}-${tag_suffix}"
    local latest_name="${REGISTRY}/${PROJECT_NAME}:latest-${tag_suffix}"
    
    echo -e "${BLUE}Building ${tag_suffix} image...${NC}"
    
    docker build \
        --file "${dockerfile}" \
        --tag "${image_name}" \
        --tag "${latest_name}" \
        --build-arg BUILD_DATE="${BUILD_DATE}" \
        --build-arg GIT_COMMIT="${GIT_COMMIT}" \
        --build-arg VERSION="${VERSION}" \
        --label "adhd.optimized=true" \
        --label "adhd.build-date=${BUILD_DATE}" \
        --label "adhd.git-commit=${GIT_COMMIT}" \
        --label "adhd.version=${VERSION}" \
        "${context}"
    
    print_status "Built ${tag_suffix} image: ${image_name}"
    
    # Run ADHD performance audit on the built image
    echo -e "${BLUE}Running ADHD performance audit...${NC}"
    docker run --rm "${image_name}" sh -c "
        if [ -f package.json ]; then
            echo 'Image size optimization check:'
            du -sh /app 2>/dev/null || echo 'App size: N/A'
            echo 'Node modules check:'
            du -sh /app/node_modules 2>/dev/null || echo 'Node modules: Not present (good for production)'
        fi
    " || print_warning "Could not run performance audit"
}

# Parse command line arguments
ENVIRONMENT="development"
BUILD_ALL=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -a|--all)
            BUILD_ALL=true
            shift
            ;;
        -h|--help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  -e, --environment ENV    Build for environment (development|production|all)"
            echo "  -a, --all               Build all images"
            echo "  -h, --help              Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0 -e development       Build development image"
            echo "  $0 -e production        Build production image"
            echo "  $0 -a                   Build all images"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Check if Docker is running
if ! docker info >/dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    print_error "package.json not found. Please run this script from the frontend directory."
    exit 1
fi

print_status "Starting ADHD-optimized build process..."
echo "Environment: ${ENVIRONMENT}"
echo "Version: ${VERSION}"
echo "Registry: ${REGISTRY}"
echo ""

# Build based on environment
case $ENVIRONMENT in
    development)
        build_image "docker/Dockerfile.dev" "dev"
        build_image "docker/Dockerfile.storybook" "storybook"
        ;;
    production)
        build_image "docker/Dockerfile.prod" "prod"
        ;;
    all)
        BUILD_ALL=true
        ;;
esac

if [ "$BUILD_ALL" = true ]; then
    print_status "Building all images..."
    build_image "docker/Dockerfile.dev" "dev"
    build_image "docker/Dockerfile.prod" "prod"
    build_image "docker/Dockerfile.storybook" "storybook"
fi

echo ""
print_status "Build complete! 🎉"
echo ""
echo -e "${BLUE}Built images:${NC}"
docker images | grep "${PROJECT_NAME}" | head -10

echo ""
echo -e "${BLUE}Next steps:${NC}"
echo "• Run development: docker-compose -f docker-compose.dev.yml up"
echo "• Run production: docker-compose -f docker-compose.prod.yml up"
echo "• Push to registry: docker push ${REGISTRY}/${PROJECT_NAME}:${VERSION}-*"
echo ""
echo -e "${GREEN}🧠 ADHD-optimized build completed successfully!${NC}"
