// Vite Plugin for ADHD Performance Monitoring
// Analyzes build performance and provides ADHD-specific optimizations

import type { Plugin, ResolvedConfig } from 'vite';
import { writeFileSync } from 'fs';
import { join } from 'path';

interface ADHDPerformanceOptions {
  enabled?: boolean;
  cognitiveLoadThreshold?: number;
  bundleSizeThreshold?: number;
  reportPath?: string;
  enableRealTimeMonitoring?: boolean;
}

interface PerformanceMetrics {
  buildTime: number;
  bundleSize: number;
  chunkCount: number;
  cognitiveLoadScore: number;
  adhdOptimizationScore: number;
  recommendations: string[];
  warnings: string[];
}

interface ChunkAnalysis {
  name: string;
  size: number;
  cognitiveComplexity: number;
  adhdFriendly: boolean;
  recommendations: string[];
}

export function adhdPerformancePlugin(options: ADHDPerformanceOptions = {}): Plugin {
  const {
    enabled = true,
    cognitiveLoadThreshold = 100000, // 100KB
    bundleSizeThreshold = 500000,    // 500KB
    reportPath = './adhd-performance-report.json',
    enableRealTimeMonitoring = true
  } = options;

  let config: ResolvedConfig;
  let buildStartTime: number;
  let metrics: PerformanceMetrics;

  return {
    name: 'adhd-performance',
    configResolved(resolvedConfig) {
      config = resolvedConfig;
    },

    buildStart() {
      if (!enabled) return;
      
      buildStartTime = Date.now();
      console.log('🧠 Starting ADHD-optimized build analysis...');
      
      metrics = {
        buildTime: 0,
        bundleSize: 0,
        chunkCount: 0,
        cognitiveLoadScore: 0,
        adhdOptimizationScore: 100,
        recommendations: [],
        warnings: []
      };
    },

    generateBundle(options, bundle) {
      if (!enabled) return;

      const chunks: ChunkAnalysis[] = [];
      let totalSize = 0;
      let totalCognitiveLoad = 0;

      // Analyze each chunk
      for (const [fileName, chunk] of Object.entries(bundle)) {
        if (chunk.type === 'chunk') {
          const chunkSize = chunk.code.length;
          const cognitiveComplexity = this.analyzeCognitiveComplexity(chunk.code);
          
          totalSize += chunkSize;
          totalCognitiveLoad += cognitiveComplexity;

          const chunkAnalysis: ChunkAnalysis = {
            name: fileName,
            size: chunkSize,
            cognitiveComplexity,
            adhdFriendly: this.isADHDFriendly(chunkSize, cognitiveComplexity),
            recommendations: this.generateChunkRecommendations(fileName, chunkSize, cognitiveComplexity)
          };

          chunks.push(chunkAnalysis);

          // Real-time warnings for large chunks
          if (chunkSize > cognitiveLoadThreshold) {
            const sizeKB = Math.round(chunkSize / 1024);
            console.warn(`⚠️  Large chunk detected: ${fileName} (${sizeKB}KB)`);
            console.warn('   This may impact ADHD users with slower connections');
            metrics.warnings.push(`Large chunk: ${fileName} (${sizeKB}KB)`);
            metrics.adhdOptimizationScore -= 10;
          }

          // Check for ADHD-unfriendly patterns
          this.checkADHDPatterns(chunk.code, fileName);
        }
      }

      // Update metrics
      metrics.bundleSize = totalSize;
      metrics.chunkCount = chunks.length;
      metrics.cognitiveLoadScore = totalCognitiveLoad;

      // Generate overall recommendations
      this.generateOverallRecommendations(chunks, totalSize);

      // Log immediate feedback
      this.logImmediateFeedback(chunks, totalSize);
    },

    buildEnd() {
      if (!enabled) return;

      metrics.buildTime = Date.now() - buildStartTime;
      
      // Calculate final ADHD optimization score
      metrics.adhdOptimizationScore = this.calculateADHDScore(metrics);

      // Generate and save report
      this.generateReport(metrics);

      console.log('✅ ADHD performance analysis complete!');
      console.log(`📊 ADHD Optimization Score: ${metrics.adhdOptimizationScore}/100`);
      
      if (metrics.adhdOptimizationScore < 70) {
        console.warn('⚠️  Consider implementing ADHD optimizations for better user experience');
      }
    },

    // Custom methods for analysis
    analyzeCognitiveComplexity(code: string): number {
      let complexity = 0;

      // Count decision points (higher cognitive load)
      complexity += (code.match(/if\s*\(/g) || []).length * 2;
      complexity += (code.match(/\?\s*:/g) || []).length * 1.5;
      complexity += (code.match(/switch\s*\(/g) || []).length * 3;
      complexity += (code.match(/for\s*\(/g) || []).length * 1;
      complexity += (code.match(/while\s*\(/g) || []).length * 1;

      // Count async operations (can cause waiting/frustration)
      complexity += (code.match(/async\s+/g) || []).length * 1;
      complexity += (code.match(/await\s+/g) || []).length * 1;
      complexity += (code.match(/\.then\(/g) || []).length * 1;

      // Count DOM manipulations (visual complexity)
      complexity += (code.match(/document\./g) || []).length * 0.5;
      complexity += (code.match(/querySelector/g) || []).length * 0.5;

      // Count state management (cognitive overhead)
      complexity += (code.match(/useState|useReducer|useEffect/g) || []).length * 1;

      return Math.round(complexity);
    },

    isADHDFriendly(size: number, complexity: number): boolean {
      return size < cognitiveLoadThreshold && complexity < 50;
    },

    generateChunkRecommendations(fileName: string, size: number, complexity: number): string[] {
      const recommendations: string[] = [];

      if (size > cognitiveLoadThreshold) {
        recommendations.push('Consider code splitting to reduce chunk size');
        recommendations.push('Use dynamic imports for non-critical code');
      }

      if (complexity > 50) {
        recommendations.push('Break down complex functions into smaller components');
        recommendations.push('Consider using React.memo() to prevent unnecessary re-renders');
      }

      if (fileName.includes('vendor') && size > bundleSizeThreshold) {
        recommendations.push('Consider using a CDN for large vendor libraries');
        recommendations.push('Evaluate if all vendor dependencies are necessary');
      }

      return recommendations;
    },

    checkADHDPatterns(code: string, fileName: string): void {
      // Check for potential memory leaks (frustrating for ADHD users)
      if (code.includes('setInterval') && !code.includes('clearInterval')) {
        console.warn(`⚠️  Potential memory leak in ${fileName} - missing clearInterval`);
        metrics.warnings.push(`Potential memory leak in ${fileName}`);
        metrics.adhdOptimizationScore -= 5;
      }

      // Check for blocking operations
      if (code.includes('document.write')) {
        console.warn(`⚠️  Blocking operation in ${fileName} - document.write detected`);
        metrics.warnings.push(`Blocking operation in ${fileName}`);
        metrics.adhdOptimizationScore -= 10;
      }

      // Check for accessibility issues
      if (code.includes('onClick') && !code.includes('onKeyDown')) {
        metrics.recommendations.push(`Add keyboard support in ${fileName} for better ADHD accessibility`);
      }
    },

    generateOverallRecommendations(chunks: ChunkAnalysis[], totalSize: number): void {
      if (totalSize > bundleSizeThreshold) {
        metrics.recommendations.push('Overall bundle size is large - consider lazy loading');
        metrics.recommendations.push('Implement progressive loading for better ADHD user experience');
      }

      if (chunks.length > 10) {
        metrics.recommendations.push('Many chunks detected - consider consolidating related code');
      }

      const largeChunks = chunks.filter(chunk => chunk.size > cognitiveLoadThreshold);
      if (largeChunks.length > 0) {
        metrics.recommendations.push(`${largeChunks.length} chunks exceed ADHD-friendly size limits`);
      }

      // ADHD-specific recommendations
      metrics.recommendations.push('Consider implementing skeleton screens for loading states');
      metrics.recommendations.push('Add progress indicators for long operations');
      metrics.recommendations.push('Implement error boundaries with ADHD-friendly error messages');
    },

    logImmediateFeedback(chunks: ChunkAnalysis[], totalSize: number): void {
      const totalSizeKB = Math.round(totalSize / 1024);
      console.log(`📦 Total bundle size: ${totalSizeKB}KB`);
      console.log(`🧩 Number of chunks: ${chunks.length}`);

      const adhdFriendlyChunks = chunks.filter(chunk => chunk.adhdFriendly).length;
      const adhdFriendlyPercentage = Math.round((adhdFriendlyChunks / chunks.length) * 100);
      console.log(`🧠 ADHD-friendly chunks: ${adhdFriendlyChunks}/${chunks.length} (${adhdFriendlyPercentage}%)`);

      if (adhdFriendlyPercentage < 80) {
        console.warn('⚠️  Consider optimizing chunks for better ADHD user experience');
      }
    },

    calculateADHDScore(metrics: PerformanceMetrics): number {
      let score = 100;

      // Penalize large bundle size
      if (metrics.bundleSize > bundleSizeThreshold) {
        score -= 20;
      }

      // Penalize high cognitive load
      if (metrics.cognitiveLoadScore > 100) {
        score -= 15;
      }

      // Penalize slow build times (developer experience affects ADHD users)
      if (metrics.buildTime > 30000) { // 30 seconds
        score -= 10;
      }

      // Penalize warnings
      score -= metrics.warnings.length * 5;

      return Math.max(0, Math.round(score));
    },

    generateReport(metrics: PerformanceMetrics): void {
      const report = {
        timestamp: new Date().toISOString(),
        adhdOptimizationScore: metrics.adhdOptimizationScore,
        metrics: {
          buildTime: `${metrics.buildTime}ms`,
          bundleSize: `${Math.round(metrics.bundleSize / 1024)}KB`,
          chunkCount: metrics.chunkCount,
          cognitiveLoadScore: metrics.cognitiveLoadScore
        },
        adhdOptimizations: {
          recommendations: metrics.recommendations,
          warnings: metrics.warnings,
          score: metrics.adhdOptimizationScore,
          grade: this.getADHDGrade(metrics.adhdOptimizationScore)
        },
        nextSteps: this.generateNextSteps(metrics)
      };

      try {
        writeFileSync(join(config.root, reportPath), JSON.stringify(report, null, 2));
        console.log(`📄 ADHD performance report saved to: ${reportPath}`);
      } catch (error) {
        console.warn('⚠️  Could not save ADHD performance report:', error);
      }
    },

    getADHDGrade(score: number): string {
      if (score >= 90) return 'A+ (Excellent ADHD optimization)';
      if (score >= 80) return 'A (Good ADHD optimization)';
      if (score >= 70) return 'B (Acceptable ADHD optimization)';
      if (score >= 60) return 'C (Needs ADHD improvements)';
      return 'D (Poor ADHD optimization)';
    },

    generateNextSteps(metrics: PerformanceMetrics): string[] {
      const steps: string[] = [];

      if (metrics.adhdOptimizationScore < 70) {
        steps.push('Review and implement ADHD accessibility guidelines');
        steps.push('Consider user testing with ADHD individuals');
      }

      if (metrics.bundleSize > bundleSizeThreshold) {
        steps.push('Implement code splitting and lazy loading');
        steps.push('Optimize vendor dependencies');
      }

      if (metrics.warnings.length > 0) {
        steps.push('Address all performance warnings');
        steps.push('Review code for ADHD-unfriendly patterns');
      }

      steps.push('Monitor real-world performance metrics');
      steps.push('Implement progressive enhancement strategies');

      return steps;
    }
  };
}
