// ESLint Plugin for ADHD Accessibility
// Helps ensure components are optimized for ADHD users

function calculateCognitiveComplexity(node) {
  let complexity = 0;
  
  // Count decision points
  if (node.type === 'IfStatement') complexity += 2;
  if (node.type === 'ConditionalExpression') complexity += 1.5;
  if (node.type === 'SwitchStatement') complexity += 3;
  if (node.type === 'LogicalExpression') complexity += 1;
  
  // Count nested structures
  if (node.body && Array.isArray(node.body)) {
    complexity += node.body.length * 0.5;
  }
  
  // Recursively check child nodes
  if (node.body) {
    if (Array.isArray(node.body)) {
      node.body.forEach(child => {
        complexity += calculateCognitiveComplexity(child);
      });
    } else if (typeof node.body === 'object') {
      complexity += calculateCognitiveComplexity(node.body);
    }
  }
  
  return complexity;
}

function countJSXElements(node) {
  let count = 0;
  
  function traverse(n) {
    if (!n) return;
    
    if (n.type === 'JSXElement') {
      count++;
    }
    
    // Traverse children
    if (n.children) {
      n.children.forEach(traverse);
    }
    if (n.body) {
      if (Array.isArray(n.body)) {
        n.body.forEach(traverse);
      } else {
        traverse(n.body);
      }
    }
    if (n.consequent) traverse(n.consequent);
    if (n.alternate) traverse(n.alternate);
  }
  
  traverse(node);
  return count;
}

module.exports = {
  rules: {
    'cognitive-load-limit': {
      meta: {
        type: 'suggestion',
        docs: {
          description: 'Limit cognitive load in components for ADHD users',
          category: 'ADHD Accessibility',
          recommended: true
        },
        schema: [
          {
            type: 'object',
            properties: {
              max: {
                type: 'integer',
                minimum: 1
              }
            },
            additionalProperties: false
          }
        ]
      },
      create(context) {
        const options = context.options[0] || {};
        const maxComplexity = options.max || 7;
        
        return {
          FunctionDeclaration(node) {
            const complexity = calculateCognitiveComplexity(node);
            if (complexity > maxComplexity) {
              context.report({
                node,
                message: `Component cognitive load (${Math.round(complexity)}) exceeds ADHD-friendly limit (${maxComplexity}). Consider breaking into smaller components.`
              });
            }
          },
          ArrowFunctionExpression(node) {
            // Only check React components (functions that return JSX)
            if (node.parent && node.parent.type === 'VariableDeclarator') {
              const complexity = calculateCognitiveComplexity(node);
              if (complexity > maxComplexity) {
                context.report({
                  node,
                  message: `Component cognitive load (${Math.round(complexity)}) exceeds ADHD-friendly limit (${maxComplexity}). Consider breaking into smaller components.`
                });
              }
            }
          }
        };
      }
    },
    
    'attention-span-warning': {
      meta: {
        type: 'problem',
        docs: {
          description: 'Warn about components that may exceed ADHD attention spans',
          category: 'ADHD Accessibility',
          recommended: true
        },
        schema: [
          {
            type: 'object',
            properties: {
              maxElements: {
                type: 'integer',
                minimum: 1
              }
            },
            additionalProperties: false
          }
        ]
      },
      create(context) {
        const options = context.options[0] || {};
        const maxElements = options.maxElements || 15;
        
        return {
          JSXElement(node) {
            // Only check root JSX elements (components)
            if (node.parent && node.parent.type === 'ReturnStatement') {
              const elementCount = countJSXElements(node);
              if (elementCount > maxElements) {
                context.report({
                  node,
                  message: `Component has ${elementCount} elements, which may overwhelm ADHD users (limit: ${maxElements}). Consider progressive disclosure or component splitting.`
                });
              }
            }
          }
        };
      }
    },
    
    'focus-management': {
      meta: {
        type: 'error',
        docs: {
          description: 'Ensure proper focus management for ADHD users',
          category: 'ADHD Accessibility',
          recommended: true
        }
      },
      create(context) {
        return {
          JSXOpeningElement(node) {
            const elementName = node.name.name;
            
            // Check for modals without focus trap
            if (elementName && elementName.toLowerCase().includes('modal')) {
              const hasFocusTrap = node.attributes.some(attr => 
                attr.name && attr.name.name && 
                (attr.name.name.includes('focusTrap') || attr.name.name.includes('focus-trap'))
              );
              
              if (!hasFocusTrap) {
                context.report({
                  node,
                  message: 'Modal components should include focus trap for ADHD accessibility. Add focusTrap prop or use focus-trap library.'
                });
              }
            }
            
            // Check for click handlers without keyboard support
            const hasOnClick = node.attributes.some(attr => 
              attr.name && attr.name.name === 'onClick'
            );
            const hasKeyboardHandler = node.attributes.some(attr => 
              attr.name && attr.name.name && 
              (attr.name.name === 'onKeyDown' || attr.name.name === 'onKeyPress')
            );
            
            if (hasOnClick && !hasKeyboardHandler && 
                !['button', 'a', 'input', 'select', 'textarea'].includes(elementName)) {
              context.report({
                node,
                message: 'Interactive elements should support keyboard navigation for ADHD accessibility. Add onKeyDown handler or use semantic HTML elements.'
              });
            }
          }
        };
      }
    }
  }
};
