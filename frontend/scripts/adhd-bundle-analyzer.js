#!/usr/bin/env node

// ADHD Bundle Analyzer
// Analyzes webpack/vite bundles for ADHD-specific optimizations

import { readFileSync, writeFileSync, readdirSync, statSync } from 'fs';
import { join, extname } from 'path';
import { gzipSync } from 'zlib';

class ADHDBundleAnalyzer {
  constructor(options = {}) {
    this.options = {
      distPath: './dist',
      threshold: {
        chunkSize: 100 * 1024,      // 100KB
        totalSize: 500 * 1024,      // 500KB
        cognitiveLoad: 50,          // Cognitive complexity score
        loadTime: 3000,             // 3 seconds
      },
      outputPath: './adhd-bundle-analysis.json',
      ...options
    };
    
    this.results = {
      summary: {},
      chunks: [],
      recommendations: [],
      adhdScore: 0,
      issues: []
    };
  }

  async analyze() {
    console.log('🧠 Starting ADHD Bundle Analysis...\n');
    
    try {
      await this.scanDistDirectory();
      this.calculateADHDScore();
      this.generateRecommendations();
      this.generateReport();
      this.displayResults();
    } catch (error) {
      console.error('❌ Analysis failed:', error.message);
      process.exit(1);
    }
  }

  async scanDistDirectory() {
    const distPath = this.options.distPath;
    
    if (!this.fileExists(distPath)) {
      throw new Error(`Distribution directory not found: ${distPath}`);
    }

    console.log(`📁 Scanning ${distPath}...`);
    
    const files = this.getAllFiles(distPath);
    let totalSize = 0;
    let totalGzipSize = 0;

    for (const file of files) {
      const stats = statSync(file);
      const content = readFileSync(file);
      const gzipSize = gzipSync(content).length;
      
      totalSize += stats.size;
      totalGzipSize += gzipSize;

      const chunk = {
        name: file.replace(distPath + '/', ''),
        size: stats.size,
        gzipSize: gzipSize,
        type: this.getFileType(file),
        cognitiveLoad: this.calculateCognitiveLoad(content, file),
        adhdFriendly: this.isADHDFriendly(stats.size, content, file),
        recommendations: []
      };

      // Analyze chunk for ADHD-specific issues
      this.analyzeChunk(chunk, content);
      this.results.chunks.push(chunk);
    }

    this.results.summary = {
      totalFiles: files.length,
      totalSize,
      totalGzipSize,
      compressionRatio: ((totalSize - totalGzipSize) / totalSize * 100).toFixed(1),
      estimatedLoadTime: this.estimateLoadTime(totalGzipSize)
    };

    console.log(`✅ Analyzed ${files.length} files`);
    console.log(`📦 Total size: ${this.formatBytes(totalSize)} (${this.formatBytes(totalGzipSize)} gzipped)`);
  }

  getAllFiles(dir, files = []) {
    const entries = readdirSync(dir);
    
    for (const entry of entries) {
      const fullPath = join(dir, entry);
      const stat = statSync(fullPath);
      
      if (stat.isDirectory()) {
        this.getAllFiles(fullPath, files);
      } else {
        files.push(fullPath);
      }
    }
    
    return files;
  }

  getFileType(file) {
    const ext = extname(file).toLowerCase();
    const typeMap = {
      '.js': 'javascript',
      '.css': 'stylesheet',
      '.html': 'html',
      '.png': 'image',
      '.jpg': 'image',
      '.jpeg': 'image',
      '.gif': 'image',
      '.svg': 'image',
      '.woff': 'font',
      '.woff2': 'font',
      '.ttf': 'font',
      '.eot': 'font'
    };
    
    return typeMap[ext] || 'other';
  }

  calculateCognitiveLoad(content, file) {
    if (this.getFileType(file) !== 'javascript') return 0;
    
    const code = content.toString();
    let load = 0;

    // Count complexity indicators
    load += (code.match(/function\s+/g) || []).length * 2;
    load += (code.match(/=>\s*{/g) || []).length * 1;
    load += (code.match(/if\s*\(/g) || []).length * 1.5;
    load += (code.match(/for\s*\(/g) || []).length * 1;
    load += (code.match(/while\s*\(/g) || []).length * 1;
    load += (code.match(/switch\s*\(/g) || []).length * 2;
    load += (code.match(/try\s*{/g) || []).length * 1;
    
    // Count React-specific complexity
    load += (code.match(/useState/g) || []).length * 0.5;
    load += (code.match(/useEffect/g) || []).length * 1;
    load += (code.match(/useReducer/g) || []).length * 2;
    
    // Count async operations (can cause waiting/frustration)
    load += (code.match(/async\s+/g) || []).length * 1;
    load += (code.match(/await\s+/g) || []).length * 0.5;
    load += (code.match(/\.then\(/g) || []).length * 0.5;
    
    return Math.round(load);
  }

  isADHDFriendly(size, content, file) {
    const type = this.getFileType(file);
    
    // Size thresholds by type
    const thresholds = {
      javascript: 100 * 1024,  // 100KB
      stylesheet: 50 * 1024,   // 50KB
      image: 200 * 1024,       // 200KB
      font: 100 * 1024,        // 100KB
      other: 50 * 1024         // 50KB
    };
    
    const threshold = thresholds[type] || thresholds.other;
    const cognitiveLoad = this.calculateCognitiveLoad(content, file);
    
    return size <= threshold && cognitiveLoad <= this.options.threshold.cognitiveLoad;
  }

  analyzeChunk(chunk, content) {
    const { size, type, cognitiveLoad } = chunk;
    
    // Size analysis
    if (size > this.options.threshold.chunkSize) {
      chunk.recommendations.push(`Large ${type} file - consider code splitting`);
      this.results.issues.push(`${chunk.name}: Exceeds ADHD-friendly size limit`);
    }
    
    // Cognitive load analysis
    if (cognitiveLoad > this.options.threshold.cognitiveLoad) {
      chunk.recommendations.push('High cognitive complexity - simplify logic');
      this.results.issues.push(`${chunk.name}: High cognitive load (${cognitiveLoad})`);
    }
    
    // Type-specific analysis
    if (type === 'javascript') {
      this.analyzeJavaScript(chunk, content.toString());
    } else if (type === 'stylesheet') {
      this.analyzeCSS(chunk, content.toString());
    }
  }

  analyzeJavaScript(chunk, code) {
    // Check for ADHD-unfriendly patterns
    if (code.includes('setInterval') && !code.includes('clearInterval')) {
      chunk.recommendations.push('Potential memory leak - missing clearInterval');
      this.results.issues.push(`${chunk.name}: Potential memory leak`);
    }
    
    if (code.includes('document.write')) {
      chunk.recommendations.push('Blocking operation - avoid document.write');
      this.results.issues.push(`${chunk.name}: Blocking operation detected`);
    }
    
    // Check for large dependencies
    if (code.includes('lodash') && code.length > 50000) {
      chunk.recommendations.push('Large utility library - consider tree shaking');
    }
    
    // Check for unoptimized React patterns
    if (code.includes('React.createElement') && !code.includes('React.memo')) {
      chunk.recommendations.push('Consider React.memo for performance');
    }
  }

  analyzeCSS(chunk, css) {
    // Check for unused CSS (simplified)
    const selectors = css.match(/\.[a-zA-Z][a-zA-Z0-9_-]*/g) || [];
    if (selectors.length > 1000) {
      chunk.recommendations.push('Many CSS selectors - consider purging unused styles');
    }
    
    // Check for complex animations (can be distracting for ADHD)
    if (css.includes('@keyframes') && css.includes('infinite')) {
      chunk.recommendations.push('Infinite animations may distract ADHD users');
      this.results.issues.push(`${chunk.name}: Infinite animations detected`);
    }
  }

  calculateADHDScore() {
    let score = 100;
    const { totalSize, totalGzipSize, estimatedLoadTime } = this.results.summary;
    
    // Penalize large bundle size
    if (totalGzipSize > this.options.threshold.totalSize) {
      score -= 20;
    }
    
    // Penalize slow load times
    if (estimatedLoadTime > this.options.threshold.loadTime) {
      score -= 15;
    }
    
    // Penalize issues
    score -= this.results.issues.length * 5;
    
    // Bonus for ADHD-friendly chunks
    const adhdFriendlyChunks = this.results.chunks.filter(c => c.adhdFriendly).length;
    const adhdFriendlyRatio = adhdFriendlyChunks / this.results.chunks.length;
    if (adhdFriendlyRatio > 0.8) {
      score += 10;
    }
    
    this.results.adhdScore = Math.max(0, Math.round(score));
  }

  generateRecommendations() {
    const recs = this.results.recommendations;
    const { totalGzipSize, estimatedLoadTime } = this.results.summary;
    
    if (totalGzipSize > this.options.threshold.totalSize) {
      recs.push('🎯 Implement code splitting for better ADHD user experience');
      recs.push('📦 Use dynamic imports for non-critical features');
    }
    
    if (estimatedLoadTime > this.options.threshold.loadTime) {
      recs.push('⚡ Optimize loading performance for ADHD attention spans');
      recs.push('🔄 Add loading states and progress indicators');
    }
    
    const largeChunks = this.results.chunks.filter(c => c.size > this.options.threshold.chunkSize);
    if (largeChunks.length > 0) {
      recs.push(`📊 ${largeChunks.length} chunks exceed ADHD-friendly size limits`);
    }
    
    if (this.results.issues.length > 0) {
      recs.push('🔧 Address performance issues for better ADHD experience');
    }
    
    // ADHD-specific recommendations
    recs.push('🧠 Consider implementing skeleton screens for loading states');
    recs.push('🎨 Ensure animations respect prefers-reduced-motion');
    recs.push('⚠️ Add error boundaries with ADHD-friendly error messages');
  }

  estimateLoadTime(sizeBytes) {
    // Estimate load time based on average connection speeds
    const connectionSpeeds = {
      slow3G: 400 * 1024,      // 400 Kbps
      fast3G: 1.6 * 1024 * 1024, // 1.6 Mbps
      wifi: 10 * 1024 * 1024    // 10 Mbps
    };
    
    // Use slow 3G as baseline for ADHD users (often have attention issues with slow loading)
    return Math.round((sizeBytes * 8) / connectionSpeeds.slow3G * 1000); // milliseconds
  }

  formatBytes(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  }

  fileExists(path) {
    try {
      statSync(path);
      return true;
    } catch {
      return false;
    }
  }

  generateReport() {
    const report = {
      timestamp: new Date().toISOString(),
      adhdScore: this.results.adhdScore,
      summary: this.results.summary,
      chunks: this.results.chunks,
      issues: this.results.issues,
      recommendations: this.results.recommendations,
      grade: this.getADHDGrade(this.results.adhdScore)
    };
    
    writeFileSync(this.options.outputPath, JSON.stringify(report, null, 2));
  }

  getADHDGrade(score) {
    if (score >= 90) return 'A+ (Excellent for ADHD users)';
    if (score >= 80) return 'A (Good for ADHD users)';
    if (score >= 70) return 'B (Acceptable for ADHD users)';
    if (score >= 60) return 'C (Needs ADHD improvements)';
    return 'D (Poor for ADHD users)';
  }

  displayResults() {
    console.log('\n🧠 ADHD Bundle Analysis Results');
    console.log('================================\n');
    
    console.log(`📊 ADHD Score: ${this.results.adhdScore}/100`);
    console.log(`🎯 Grade: ${this.getADHDGrade(this.results.adhdScore)}\n`);
    
    console.log('📦 Bundle Summary:');
    console.log(`   Files: ${this.results.summary.totalFiles}`);
    console.log(`   Size: ${this.formatBytes(this.results.summary.totalSize)}`);
    console.log(`   Gzipped: ${this.formatBytes(this.results.summary.totalGzipSize)}`);
    console.log(`   Compression: ${this.results.summary.compressionRatio}%`);
    console.log(`   Est. Load Time: ${this.results.summary.estimatedLoadTime}ms\n`);
    
    if (this.results.issues.length > 0) {
      console.log('⚠️  ADHD Issues Found:');
      this.results.issues.forEach(issue => console.log(`   - ${issue}`));
      console.log('');
    }
    
    if (this.results.recommendations.length > 0) {
      console.log('💡 ADHD Recommendations:');
      this.results.recommendations.forEach(rec => console.log(`   ${rec}`));
      console.log('');
    }
    
    console.log(`📄 Detailed report saved to: ${this.options.outputPath}`);
    console.log('🎉 Analysis complete!\n');
  }
}

// CLI usage
if (import.meta.url === `file://${process.argv[1]}`) {
  const analyzer = new ADHDBundleAnalyzer();
  analyzer.analyze().catch(console.error);
}

export default ADHDBundleAnalyzer;
