#!/bin/bash

# ADHD-Optimized Development Startup Script
# Starts the complete development environment with Traefik

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Configuration
TRAEFIK_NETWORK="traefik"
PROJECT_NAME="chronos"

echo -e "${BLUE}🧠 Starting ADHD-Optimized Development Environment${NC}"
echo -e "${BLUE}=================================================${NC}"
echo ""

# Function to print status
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
echo -e "${BLUE}Checking prerequisites...${NC}"

if ! command_exists docker; then
    print_error "Docker is not installed. Please install Docker and try again."
    exit 1
fi

if ! command_exists docker-compose; then
    print_error "Docker Compose is not installed. Please install Docker Compose and try again."
    exit 1
fi

if ! docker info >/dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

print_status "Prerequisites check passed"

# Create Traefik network if it doesn't exist
echo ""
echo -e "${BLUE}Setting up Traefik network...${NC}"

if ! docker network ls | grep -q "$TRAEFIK_NETWORK"; then
    docker network create "$TRAEFIK_NETWORK"
    print_status "Created Traefik network: $TRAEFIK_NETWORK"
else
    print_info "Traefik network already exists: $TRAEFIK_NETWORK"
fi

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    print_error "package.json not found. Please run this script from the frontend directory."
    exit 1
fi

# Parse command line arguments
START_TRAEFIK=true
START_FRONTEND=true
BUILD_IMAGES=false
DETACHED=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --no-traefik)
            START_TRAEFIK=false
            shift
            ;;
        --no-frontend)
            START_FRONTEND=false
            shift
            ;;
        --build)
            BUILD_IMAGES=true
            shift
            ;;
        -d|--detached)
            DETACHED=true
            shift
            ;;
        -h|--help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --no-traefik    Don't start Traefik (assume it's already running)"
            echo "  --no-frontend   Don't start frontend services"
            echo "  --build         Build images before starting"
            echo "  -d, --detached  Run in detached mode"
            echo "  -h, --help      Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0                    Start everything"
            echo "  $0 --build           Build and start everything"
            echo "  $0 --no-traefik      Start only frontend (Traefik already running)"
            echo "  $0 -d                Start in background"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Build images if requested
if [ "$BUILD_IMAGES" = true ]; then
    echo ""
    echo -e "${BLUE}Building Docker images...${NC}"
    ./scripts/docker-build.sh -e development
    print_status "Images built successfully"
fi

# Start Traefik if requested
if [ "$START_TRAEFIK" = true ]; then
    echo ""
    echo -e "${BLUE}Starting Traefik reverse proxy...${NC}"
    
    if docker ps | grep -q "traefik"; then
        print_info "Traefik is already running"
    else
        docker-compose -f docker/docker-compose.traefik.yml up -d
        print_status "Traefik started"
        
        # Wait for Traefik to be ready
        echo -e "${BLUE}Waiting for Traefik to be ready...${NC}"
        sleep 5
        
        if curl -s http://localhost:8080/ping >/dev/null 2>&1; then
            print_status "Traefik is ready"
        else
            print_warning "Traefik may not be fully ready yet"
        fi
    fi
fi

# Start frontend services if requested
if [ "$START_FRONTEND" = true ]; then
    echo ""
    echo -e "${BLUE}Starting frontend services...${NC}"
    
    COMPOSE_ARGS=""
    if [ "$DETACHED" = true ]; then
        COMPOSE_ARGS="-d"
    fi
    
    docker-compose -f docker-compose.dev.yml up $COMPOSE_ARGS
    
    if [ "$DETACHED" = true ]; then
        print_status "Frontend services started in background"
    fi
fi

# Show service URLs
echo ""
echo -e "${PURPLE}🎉 ADHD-Optimized Development Environment Ready!${NC}"
echo -e "${PURPLE}================================================${NC}"
echo ""
echo -e "${GREEN}Available Services:${NC}"
echo -e "  🌐 Main App:      ${BLUE}https://app.autism.localhost${NC}"
echo -e "  📚 Storybook:     ${BLUE}https://storybook.autism.localhost${NC}"
echo -e "  🔄 HMR:           ${BLUE}http://hmr.autism.localhost${NC}"
echo -e "  🚦 Traefik:       ${BLUE}http://localhost:8080${NC}"
echo -e "  🧪 Test Service:  ${BLUE}https://whoami.autism.localhost${NC}"
echo ""
echo -e "${GREEN}ADHD Optimizations Active:${NC}"
echo -e "  ⚡ Fast loading with compression"
echo -e "  🧠 Cognitive load monitoring"
echo -e "  🎯 Focus-friendly rate limiting"
echo -e "  🔒 Security headers optimized"
echo -e "  📊 Performance metrics enabled"
echo ""

if [ "$DETACHED" = true ]; then
    echo -e "${BLUE}Services are running in the background.${NC}"
    echo -e "${BLUE}Use 'docker-compose -f docker-compose.dev.yml logs -f' to view logs.${NC}"
    echo -e "${BLUE}Use 'docker-compose -f docker-compose.dev.yml down' to stop services.${NC}"
else
    echo -e "${BLUE}Press Ctrl+C to stop all services.${NC}"
fi

echo ""
echo -e "${GREEN}🧠 Happy ADHD-optimized coding! 🚀${NC}"
