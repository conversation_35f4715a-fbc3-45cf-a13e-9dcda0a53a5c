API Schemas
===========

This section documents all Pydantic schemas used in the Project Chronos API for request/response validation and serialization.

.. currentmodule:: app.schemas

Gamification Schemas
--------------------

Profile Schemas
~~~~~~~~~~~~~~~

.. autoclass:: app.schemas.gamification.GamificationProfileBase
   :members:
   :show-inheritance:

.. autoclass:: app.schemas.gamification.GamificationProfileCreate
   :members:
   :show-inheritance:

.. autoclass:: app.schemas.gamification.GamificationProfileUpdate
   :members:
   :show-inheritance:

.. autoclass:: app.schemas.gamification.GamificationProfileResponse
   :members:
   :show-inheritance:

Points Schemas
~~~~~~~~~~~~~~

.. autoclass:: app.schemas.gamification.PointsAwardBase
   :members:
   :show-inheritance:

.. autoclass:: app.schemas.gamification.PointsAwardCreate
   :members:
   :show-inheritance:

.. autoclass:: app.schemas.gamification.PointsAwardResponse
   :members:
   :show-inheritance:

.. autoclass:: app.schemas.gamification.PointsCalculationRequest
   :members:
   :show-inheritance:

.. autoclass:: app.schemas.gamification.PointsCalculationResponse
   :members:
   :show-inheritance:

Achievement Schemas
~~~~~~~~~~~~~~~~~~

.. autoclass:: app.schemas.gamification.AchievementBase
   :members:
   :show-inheritance:

.. autoclass:: app.schemas.gamification.AchievementCreate
   :members:
   :show-inheritance:

.. autoclass:: app.schemas.gamification.AchievementUpdate
   :members:
   :show-inheritance:

.. autoclass:: app.schemas.gamification.AchievementResponse
   :members:
   :show-inheritance:

.. autoclass:: app.schemas.gamification.UserAchievementResponse
   :members:
   :show-inheritance:

.. autoclass:: app.schemas.gamification.AchievementUnlockResponse
   :members:
   :show-inheritance:

Streak Schemas
~~~~~~~~~~~~~~

.. autoclass:: app.schemas.gamification.StreakBase
   :members:
   :show-inheritance:

.. autoclass:: app.schemas.gamification.StreakUpdate
   :members:
   :show-inheritance:

.. autoclass:: app.schemas.gamification.StreakResponse
   :members:
   :show-inheritance:

Dashboard Schemas
~~~~~~~~~~~~~~~~

.. autoclass:: app.schemas.gamification.GamificationStatsResponse
   :members:
   :show-inheritance:

.. autoclass:: app.schemas.gamification.GamificationDashboardResponse
   :members:
   :show-inheritance:

.. autoclass:: app.schemas.gamification.LevelUpCelebration
   :members:
   :show-inheritance:

Motivation Schemas
------------------

Activity Schemas
~~~~~~~~~~~~~~~

.. autoclass:: app.schemas.motivation.DopamineActivityBase
   :members:
   :show-inheritance:

.. autoclass:: app.schemas.motivation.DopamineActivityCreate
   :members:
   :show-inheritance:

.. autoclass:: app.schemas.motivation.DopamineActivityUpdate
   :members:
   :show-inheritance:

.. autoclass:: app.schemas.motivation.DopamineActivityResponse
   :members:
   :show-inheritance:

Menu Schemas
~~~~~~~~~~~~

.. autoclass:: app.schemas.motivation.DopamineMenuRequest
   :members:
   :show-inheritance:

.. autoclass:: app.schemas.motivation.DopamineMenuResponse
   :members:
   :show-inheritance:

Preference Schemas
~~~~~~~~~~~~~~~~~

.. autoclass:: app.schemas.motivation.UserDopaminePreferenceResponse
   :members:
   :show-inheritance:

.. autoclass:: app.schemas.motivation.CustomActivityRequest
   :members:
   :show-inheritance:

Completion Schemas
~~~~~~~~~~~~~~~~~

.. autoclass:: app.schemas.motivation.DopamineActivityCompletionCreate
   :members:
   :show-inheritance:

.. autoclass:: app.schemas.motivation.DopamineActivityCompletionResponse
   :members:
   :show-inheritance:

Analytics Schemas
~~~~~~~~~~~~~~~~

.. autoclass:: app.schemas.motivation.MotivationAnalyticsResponse
   :members:
   :show-inheritance:

.. autoclass:: app.schemas.motivation.MotivationDashboardResponse
   :members:
   :show-inheritance:

.. autoclass:: app.schemas.motivation.MotivationInsightResponse
   :members:
   :show-inheritance:

Tracking Schemas
~~~~~~~~~~~~~~~

.. autoclass:: app.schemas.motivation.EnergyMoodTrackingRequest
   :members:
   :show-inheritance:

.. autoclass:: app.schemas.motivation.EnergyMoodTrackingResponse
   :members:
   :show-inheritance:

Task Management Schemas
-----------------------

Task Schemas
~~~~~~~~~~~~

.. autoclass:: app.schemas.task.TaskBase
   :members:
   :show-inheritance:

.. autoclass:: app.schemas.task.TaskCreate
   :members:
   :show-inheritance:

.. autoclass:: app.schemas.task.TaskUpdate
   :members:
   :show-inheritance:

.. autoclass:: app.schemas.task.TaskResponse
   :members:
   :show-inheritance:

Specialized Task Schemas
~~~~~~~~~~~~~~~~~~~~~~~

.. autoclass:: app.schemas.task.TaskChunkRequest
   :members:
   :show-inheritance:

.. autoclass:: app.schemas.task.TaskFilterRequest
   :members:
   :show-inheritance:

.. autoclass:: app.schemas.task.TaskJarRequest
   :members:
   :show-inheritance:

.. autoclass:: app.schemas.task.TaskStatsResponse
   :members:
   :show-inheritance:

Schema Usage Examples
--------------------

Gamification Examples
~~~~~~~~~~~~~~~~~~~~

**Creating a Points Award**:

.. code-block:: python

   from app.schemas.gamification import PointsAwardCreate
   
   points_award = PointsAwardCreate(
       user_id="uuid",
       points=50,
       reason="Completed difficult task",
       multiplier=1.5,
       task_id="uuid"
   )

**Updating Gamification Profile**:

.. code-block:: python

   from app.schemas.gamification import GamificationProfileUpdate
   
   profile_update = GamificationProfileUpdate(
       celebration_style="enthusiastic",
       preferred_rewards={
           "types": ["badges", "themes", "points"],
           "frequency": "immediate"
       }
   )

**Calculating Points with Context**:

.. code-block:: python

   from app.schemas.gamification import PointsCalculationRequest
   
   calculation = PointsCalculationRequest(
       base_points=30,
       task_difficulty="hard",
       energy_level="low",
       time_of_day="morning",
       context={
           "first_task_of_day": True,
           "after_break": False
       }
   )

Motivation Examples
~~~~~~~~~~~~~~~~~~

**Requesting Dopamine Menu**:

.. code-block:: python

   from app.schemas.motivation import DopamineMenuRequest
   
   menu_request = DopamineMenuRequest(
       energy_level="low",
       available_time=5,
       context="pre_task",
       exclude_categories=["movement"],
       preferred_categories=["mental", "sensory"]
   )

**Recording Activity Completion**:

.. code-block:: python

   from app.schemas.motivation import DopamineActivityCompletionCreate
   
   completion = DopamineActivityCompletionCreate(
       user_id="uuid",
       activity_id="uuid",
       actual_duration=5,
       energy_before="low",
       energy_after="medium",
       mood_before=3,
       mood_after=6,
       satisfaction_rating=4,
       would_do_again=True,
       context="pre_task",
       notes="Felt refreshing and helped me focus"
   )

**Creating Custom Activity**:

.. code-block:: python

   from app.schemas.motivation import CustomActivityRequest
   
   custom_activity = CustomActivityRequest(
       name="Pet My Cat",
       description="Spend time with my cat for instant mood boost",
       category="social",
       duration=3,
       energy_requirement="low",
       energy_boost="high",
       tags=["pets", "comfort", "quick"]
   )

Task Management Examples
~~~~~~~~~~~~~~~~~~~~~~~

**Creating a Task**:

.. code-block:: python

   from app.schemas.task import TaskCreate
   
   task = TaskCreate(
       title="Complete project documentation",
       description="Write comprehensive docs for the new feature",
       priority="high",
       energy_level="medium",
       estimated_duration=120,
       due_date="2024-01-15T17:00:00Z",
       tags=["documentation", "project"]
   )

**Requesting AI Chunking**:

.. code-block:: python

   from app.schemas.task import TaskChunkRequest
   
   chunk_request = TaskChunkRequest(
       task_description="Write a comprehensive research paper on ADHD productivity tools",
       user_energy_level="medium",
       available_time=60,
       user_preferences={
           "chunk_size": "medium",
           "include_breaks": True
       }
   )

**Filtering Tasks**:

.. code-block:: python

   from app.schemas.task import TaskFilterRequest
   
   filter_request = TaskFilterRequest(
       energy_level="low",
       available_time=30,
       priority_levels=["low", "medium"],
       exclude_tags=["overwhelming"],
       context="tired_evening"
   )

Validation and Error Handling
-----------------------------

All schemas include comprehensive validation:

**Field Validation**:
- Type checking for all fields
- Range validation for numeric fields
- Format validation for dates and UUIDs
- Enum validation for choice fields

**Custom Validators**:
- Energy level consistency checks
- Duration range validation
- Context-appropriate field requirements
- ADHD-specific validation rules

**Error Response Format**:

.. code-block:: json

   {
     "detail": [
       {
         "loc": ["body", "energy_level"],
         "msg": "value is not a valid enumeration member; permitted: 'low', 'medium', 'high'",
         "type": "type_error.enum",
         "ctx": {"enum_values": ["low", "medium", "high"]}
       }
     ]
   }

Schema Evolution
---------------

**Versioning Strategy**:
- Backward compatibility maintained for at least 12 months
- New optional fields added without breaking changes
- Deprecated fields marked with warnings
- Major changes require new API version

**Migration Support**:
- Automatic schema migration for minor updates
- Migration guides for major changes
- Validation compatibility layers
- Development tools for schema testing

This comprehensive schema documentation ensures consistent API usage and helps developers understand the data structures used throughout Project Chronos.
