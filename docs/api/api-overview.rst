API Overview and Integration Guide
==================================

Project Chronos provides a comprehensive REST API designed with ADHD users and developers in mind. The API follows modern standards while incorporating ADHD-specific accommodations and features that make integration both powerful and accessible.

.. currentmodule:: app.api

API Design Philosophy
--------------------

ADHD-Centered API Design
~~~~~~~~~~~~~~~~~~~~~~~

Our API design reflects the same ADHD-first principles as our user interface:

.. mermaid::

   graph TB
       subgraph "Core Principles"
           Predictable[Predictable Patterns]
           Forgiving[Forgiving Errors]
           Clear[Clear Documentation]
           Consistent[Consistent Responses]
       end
       
       subgraph "Developer Experience"
           Simple[Simple Integration]
           Helpful[Helpful Errors]
           Examples[Rich Examples]
           Testing[Easy Testing]
       end
       
       subgraph "ADHD Features"
           Context[Context Preservation]
           Flexibility[Flexible Inputs]
           Validation[Gentle Validation]
           Recovery[Error Recovery]
       end
       
       Predictable --> Simple
       Forgiving --> Helpful
       Clear --> Examples
       Consistent --> Testing
       
       Simple --> Context
       Helpful --> Flexibility
       Examples --> Validation
       Testing --> Recovery

**Predictable and Consistent**
   API patterns that reduce cognitive load for developers:
   
   - **Consistent Naming**: Uniform resource naming across all endpoints
   - **Standard HTTP Methods**: Intuitive use of GET, POST, PUT, DELETE
   - **Predictable Responses**: Consistent response structure and error handling
   - **Clear Documentation**: Comprehensive examples and use cases

**Forgiving and Flexible**
   Error handling that supports rather than frustrates:
   
   - **Helpful Error Messages**: Clear explanations of what went wrong and how to fix it
   - **Partial Success**: Accept and process valid data even when some fields have issues
   - **Flexible Input Formats**: Accept various date/time formats and data structures
   - **Graceful Degradation**: Continue functioning when optional services are unavailable

API Architecture
---------------

Service-Oriented Design
~~~~~~~~~~~~~~~~~~~~~~

.. mermaid::

   graph LR
       subgraph "Client Applications"
           Web[Web App]
           Mobile[Mobile App]
           Third[Third Party]
       end
       
       subgraph "API Gateway"
           Gateway[FastAPI Gateway]
           Auth[Authentication]
           Rate[Rate Limiting]
           Docs[Auto Documentation]
       end
       
       subgraph "Core Services"
           Tasks[Task Management]
           Time[Time Blocking]
           Focus[Focus Sessions]
           Notifications[Notifications]
           Gamification[Gamification]
           Social[Body Doubling]
       end
       
       subgraph "Data & Background"
           Database[(PostgreSQL)]
           Queue[Redis Queue]
           Workers[Celery Workers]
       end
       
       Web --> Gateway
       Mobile --> Gateway
       Third --> Gateway
       
       Gateway --> Auth
       Gateway --> Rate
       Gateway --> Docs
       
       Auth --> Tasks
       Auth --> Time
       Auth --> Focus
       Auth --> Notifications
       Auth --> Gamification
       Auth --> Social
       
       Tasks --> Database
       Time --> Database
       Focus --> Database
       Notifications --> Queue
       Queue --> Workers

**RESTful Resource Design**
   Clean, intuitive resource organization:
   
   - **Resource-Based URLs**: Clear hierarchy and relationships
   - **HTTP Method Semantics**: Proper use of GET, POST, PUT, DELETE
   - **Stateless Operations**: Each request contains all necessary information
   - **Cacheable Responses**: Appropriate caching headers for performance

**ADHD-Specific Endpoints**
   Specialized endpoints for neurodivergent needs:
   
   - **Context Preservation**: Endpoints for saving and restoring user state
   - **Flexible Scheduling**: Time management with ADHD accommodations
   - **Attention Management**: Focus session control and monitoring
   - **Gentle Reminders**: Notification system with escalation control

Authentication and Security
--------------------------

JWT-Based Authentication
~~~~~~~~~~~~~~~~~~~~~~

.. mermaid::

   sequenceDiagram
       participant Client
       participant API
       participant Auth
       participant Database
       
       Client->>API: POST /auth/login
       API->>Auth: Validate credentials
       Auth->>Database: Check user
       Database-->>Auth: User data
       Auth->>Auth: Generate JWT tokens
       Auth-->>API: Access & refresh tokens
       API-->>Client: Authentication response
       
       Note over Client,Database: Subsequent API calls
       Client->>API: Request with Bearer token
       API->>Auth: Validate token
       Auth-->>API: User context
       API->>API: Process request
       API-->>Client: Response

**Token Management**
   Secure, user-friendly authentication:
   
   - **Short-Lived Access Tokens**: 15-minute expiration for security
   - **Long-Lived Refresh Tokens**: 30-day expiration for convenience
   - **Automatic Refresh**: Seamless token renewal for uninterrupted use
   - **Secure Storage**: Guidance for proper token storage in client applications

**ADHD-Friendly Security**
   Security that doesn't frustrate users:
   
   - **Remember Me Options**: Extended sessions for trusted devices
   - **Gentle Lockouts**: Progressive delays rather than hard blocks
   - **Recovery Assistance**: Clear guidance for locked accounts
   - **Privacy Controls**: Granular control over data sharing and visibility

Core API Endpoints
-----------------

Task Management API
~~~~~~~~~~~~~~~~~~

**Base URL**: ``/api/v1/tasks``

.. code-block:: http

   GET /api/v1/tasks
   POST /api/v1/tasks
   GET /api/v1/tasks/{task_id}
   PUT /api/v1/tasks/{task_id}
   DELETE /api/v1/tasks/{task_id}
   
   # ADHD-specific endpoints
   POST /api/v1/tasks/{task_id}/chunk
   POST /api/v1/tasks/{task_id}/start
   POST /api/v1/tasks/{task_id}/complete
   GET /api/v1/tasks/energy/{energy_level}

**Key Features**:
   - AI-powered task chunking and context addition
   - Energy-aware task filtering and suggestions
   - Flexible completion tracking with partial progress
   - ADHD-friendly task organization and categorization

Time Blocking API
~~~~~~~~~~~~~~~~

**Base URL**: ``/api/v1/time-blocks``

.. code-block:: http

   GET /api/v1/time-blocks/daily/{date}
   POST /api/v1/time-blocks
   PUT /api/v1/time-blocks/{block_id}/move
   POST /api/v1/time-blocks/validate/{date}
   
   # Visual interface endpoints
   GET /api/v1/time-blocks/views/circular/{date}
   GET /api/v1/time-blocks/views/timeline/{date}
   GET /api/v1/time-blocks/suggestions/{date}

**Key Features**:
   - Visual time management with circular and timeline views
   - Automatic buffer time insertion and conflict detection
   - Intelligent time slot suggestions based on energy patterns
   - Schedule validation with ADHD-specific warnings

Focus Sessions API
~~~~~~~~~~~~~~~~~

**Base URL**: ``/api/v1/focus``

.. code-block:: http

   POST /api/v1/focus/sessions
   POST /api/v1/focus/sessions/{session_id}/start
   POST /api/v1/focus/sessions/{session_id}/pause
   POST /api/v1/focus/sessions/{session_id}/resume
   POST /api/v1/focus/sessions/{session_id}/complete
   
   # ADHD-specific features
   GET /api/v1/focus/sessions/{session_id}/hyperfocus-check
   GET /api/v1/focus/sessions/{session_id}/break-suggestion
   POST /api/v1/focus/modes

**Key Features**:
   - Flexible focus session management with pause/resume
   - Hyperfocus detection and protection systems
   - Customizable focus modes for different work types
   - Break suggestions with ADHD-friendly activities

Notifications API
~~~~~~~~~~~~~~~~

**Base URL**: ``/api/v1/notifications``

.. code-block:: http

   GET /api/v1/notifications
   POST /api/v1/notifications
   POST /api/v1/notifications/bulk
   POST /api/v1/notifications/{notification_id}/acknowledge
   POST /api/v1/notifications/{notification_id}/snooze
   
   # Preference management
   GET /api/v1/notifications/preferences
   PUT /api/v1/notifications/preferences
   GET /api/v1/notifications/stats/summary

**Key Features**:
   - Persistent reminders with gentle escalation
   - Context-aware delivery respecting focus sessions
   - Flexible snoozing and acknowledgment options
   - Comprehensive preference management for ADHD needs

Request/Response Patterns
------------------------

Standard Response Format
~~~~~~~~~~~~~~~~~~~~~~

All API responses follow a consistent structure:

.. code-block:: json

   {
     "data": {
       // Main response data
     },
     "meta": {
       "timestamp": "2024-01-15T10:30:00Z",
       "request_id": "req_123456789",
       "api_version": "v1"
     },
     "pagination": {
       // Present for paginated responses
       "page": 1,
       "per_page": 20,
       "total": 150,
       "total_pages": 8
     }
   }

**ADHD-Friendly Response Features**:
   - **Consistent Structure**: Predictable response format reduces cognitive load
   - **Rich Metadata**: Context information for debugging and monitoring
   - **Calculated Fields**: Pre-computed values to reduce client-side processing
   - **Helpful Links**: Related resource URLs for easy navigation

Error Handling
~~~~~~~~~~~~~

.. mermaid::

   graph TB
       Error[API Error] --> Type{Error Type}
       
       Type -->|Validation| Validation[400 Bad Request]
       Type -->|Authentication| Auth[401 Unauthorized]
       Type -->|Authorization| Forbidden[403 Forbidden]
       Type -->|Not Found| NotFound[404 Not Found]
       Type -->|Server Error| Server[500 Internal Error]
       
       Validation --> Helpful[Helpful Error Messages]
       Auth --> Recovery[Recovery Guidance]
       Forbidden --> Explanation[Clear Explanation]
       NotFound --> Suggestions[Alternative Suggestions]
       Server --> Support[Support Information]

**Error Response Format**:

.. code-block:: json

   {
     "error": {
       "code": "VALIDATION_ERROR",
       "message": "The task title is required and cannot be empty.",
       "details": {
         "field": "title",
         "provided_value": "",
         "expected_format": "Non-empty string, 1-200 characters"
       },
       "suggestions": [
         "Provide a brief description of what you want to accomplish",
         "Try something like 'Review project proposal' or 'Call dentist'"
       ],
       "help_url": "https://docs.chronos.app/api/tasks#creating-tasks"
     },
     "meta": {
       "timestamp": "2024-01-15T10:30:00Z",
       "request_id": "req_123456789"
     }
   }

**ADHD-Friendly Error Features**:
   - **Clear Explanations**: Human-readable error messages
   - **Helpful Suggestions**: Specific guidance for fixing issues
   - **No Blame Language**: Supportive tone that doesn't shame users
   - **Recovery Assistance**: Clear next steps for resolution

Integration Examples
-------------------

Quick Start Integration
~~~~~~~~~~~~~~~~~~~~~

**1. Authentication**

.. code-block:: python

   import requests
   
   # Login to get tokens
   response = requests.post('https://api.chronos.app/v1/auth/login', json={
       'email': '<EMAIL>',
       'password': 'secure_password'
   })
   
   tokens = response.json()['data']
   access_token = tokens['access_token']
   
   # Use token for authenticated requests
   headers = {'Authorization': f'Bearer {access_token}'}

**2. Create a Task with AI Chunking**

.. code-block:: python

   # Create a complex task
   task_data = {
       'title': 'Prepare presentation for quarterly review',
       'description': 'Need to create slides and practice for Q4 review meeting',
       'due_date': '2024-01-20T14:00:00Z',
       'energy_level': 'high',
       'use_ai_chunking': True
   }
   
   response = requests.post(
       'https://api.chronos.app/v1/tasks',
       json=task_data,
       headers=headers
   )
   
   task = response.json()['data']
   print(f"Created task: {task['title']}")
   print(f"AI suggested {len(task['chunks'])} subtasks")

**3. Schedule Focus Session**

.. code-block:: python

   # Start a focus session for the task
   focus_data = {
       'session_type': 'deep_work',
       'title': 'Work on presentation',
       'planned_duration': 90,  # 90 minutes
       'task_id': task['id'],
       'enable_hyperfocus_protection': True
   }
   
   response = requests.post(
       'https://api.chronos.app/v1/focus/sessions',
       json=focus_data,
       headers=headers
   )
   
   session = response.json()['data']
   
   # Start the session
   requests.post(
       f'https://api.chronos.app/v1/focus/sessions/{session["id"]}/start',
       headers=headers
   )

**4. Set Up Gentle Reminders**

.. code-block:: python

   # Create a reminder sequence for the deadline
   reminder_data = {
       'type': 'deadline_warning',
       'title': 'Presentation Due Soon',
       'message': 'Your quarterly review presentation is due tomorrow. You\'ve got this!',
       'scheduled_for': '2024-01-19T09:00:00Z',
       'priority': 'high',
       'is_persistent': True,
       'task_id': task['id'],
       'reminder_stages': [1440, 720, 180, 60]  # 1 day, 12 hours, 3 hours, 1 hour
   }
   
   requests.post(
       'https://api.chronos.app/v1/notifications',
       json=reminder_data,
       headers=headers
   )

Advanced Integration Patterns
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Webhook Integration for Real-Time Updates**

.. code-block:: python

   # Set up webhook for task completion events
   webhook_config = {
       'url': 'https://your-app.com/webhooks/chronos',
       'events': ['task.completed', 'focus_session.ended'],
       'secret': 'your_webhook_secret'
   }
   
   requests.post(
       'https://api.chronos.app/v1/webhooks',
       json=webhook_config,
       headers=headers
   )

**Batch Operations for Efficiency**

.. code-block:: python

   # Create multiple related tasks at once
   batch_tasks = {
       'tasks': [
           {
               'title': 'Research presentation topics',
               'energy_level': 'medium',
               'estimated_duration': 30
           },
           {
               'title': 'Create slide outline',
               'energy_level': 'high',
               'estimated_duration': 45
           },
           {
               'title': 'Design slides',
               'energy_level': 'medium',
               'estimated_duration': 120
           }
       ],
       'batch_processing': True
   }
   
   response = requests.post(
       'https://api.chronos.app/v1/tasks/batch',
       json=batch_tasks,
       headers=headers
   )

Rate Limiting and Best Practices
-------------------------------

Rate Limiting
~~~~~~~~~~~~

.. mermaid::

   graph LR
       subgraph "Rate Limits"
           Auth[Authentication: 5/min]
           Read[Read Operations: 100/min]
           Write[Write Operations: 50/min]
           Bulk[Bulk Operations: 10/min]
       end
       
       subgraph "Headers"
           Limit[X-RateLimit-Limit]
           Remaining[X-RateLimit-Remaining]
           Reset[X-RateLimit-Reset]
       end
       
       subgraph "Best Practices"
           Cache[Cache Responses]
           Batch[Use Batch Endpoints]
           Retry[Exponential Backoff]
           Monitor[Monitor Usage]
       end

**Rate Limit Headers**:
   Every response includes rate limiting information:
   
   - ``X-RateLimit-Limit``: Maximum requests per window
   - ``X-RateLimit-Remaining``: Requests remaining in current window
   - ``X-RateLimit-Reset``: Unix timestamp when window resets

**ADHD-Friendly Rate Limiting**:
   - **Generous Limits**: Accommodate variable usage patterns
   - **Clear Communication**: Helpful error messages when limits are reached
   - **Gradual Enforcement**: Warnings before hard limits
   - **Recovery Assistance**: Clear guidance for resuming after limits

Best Practices for ADHD Users
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Client-Side Considerations**:
   - **Offline Support**: Cache data for when internet is unreliable
   - **Progress Preservation**: Save work frequently to prevent loss
   - **Error Recovery**: Graceful handling of network issues
   - **User Feedback**: Clear indication of API operation status

**Performance Optimization**:
   - **Efficient Queries**: Use filtering and pagination appropriately
   - **Caching Strategy**: Cache frequently accessed data
   - **Batch Operations**: Group related operations when possible
   - **Async Processing**: Use background tasks for heavy operations

This API is designed to be both powerful and accessible, supporting developers in creating ADHD-friendly applications that truly serve the neurodivergent community.

Complete API Reference
---------------------

Authentication Endpoints
~~~~~~~~~~~~~~~~~~~~~~~

**POST /api/v1/auth/login**
   Authenticate user and receive JWT tokens.

   **Request Body**:

   .. code-block:: json

      {
        "email": "<EMAIL>",
        "password": "secure_password"
      }

   **Response**:

   .. code-block:: json

      {
        "data": {
          "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
          "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
          "token_type": "bearer",
          "expires_in": 900,
          "user": {
            "id": "550e8400-e29b-41d4-a716-446655440000",
            "email": "<EMAIL>",
            "full_name": "John Doe",
            "is_active": true
          }
        },
        "meta": {
          "timestamp": "2024-01-15T10:30:00Z",
          "request_id": "req_123456789"
        }
      }

**POST /api/v1/auth/refresh**
   Refresh access token using refresh token.

**POST /api/v1/auth/logout**
   Logout and invalidate tokens.

Task Management Endpoints
~~~~~~~~~~~~~~~~~~~~~~~~

**GET /api/v1/tasks**
   List user's tasks with ADHD-friendly filtering.

   **Query Parameters**:
   - ``status``: Filter by task status (pending, in_progress, completed)
   - ``energy_level``: Filter by required energy (low, medium, high)
   - ``due_date_from``: Filter tasks due after this date
   - ``due_date_to``: Filter tasks due before this date
   - ``search``: Search in title and description
   - ``limit``: Number of tasks to return (default: 20, max: 100)
   - ``offset``: Number of tasks to skip for pagination

   **Response**:

   .. code-block:: json

      {
        "data": [
          {
            "id": "550e8400-e29b-41d4-a716-446655440000",
            "title": "Complete project proposal",
            "description": "Write and review the Q1 project proposal",
            "status": "pending",
            "priority": "high",
            "energy_level": "high",
            "complexity": "medium",
            "estimated_duration": 120,
            "due_date": "2024-01-20T17:00:00Z",
            "created_at": "2024-01-15T10:00:00Z",
            "updated_at": "2024-01-15T10:00:00Z",
            "chunks": [
              {
                "id": "chunk_001",
                "title": "Research similar proposals",
                "description": "Look at last quarter's successful proposals",
                "estimated_duration": 30,
                "order": 1
              }
            ],
            "ai_suggestions": {
              "next_steps": ["Start with research phase", "Set up document structure"],
              "energy_match": "This task requires high energy - schedule for your peak hours",
              "time_estimate": "Based on similar tasks, this might take 2-3 hours"
            }
          }
        ],
        "pagination": {
          "page": 1,
          "per_page": 20,
          "total": 45,
          "total_pages": 3
        }
      }

**POST /api/v1/tasks**
   Create a new task with AI assistance.

   **Request Body**:

   .. code-block:: json

      {
        "title": "Prepare presentation for quarterly review",
        "description": "Create slides and practice for Q4 review meeting",
        "due_date": "2024-01-25T14:00:00Z",
        "priority": "high",
        "energy_level": "high",
        "use_ai_chunking": true,
        "context": {
          "meeting_type": "quarterly_review",
          "audience": "executive_team",
          "duration": "30_minutes"
        }
      }

**POST /api/v1/tasks/{task_id}/chunk**
   Use AI to break down a complex task into manageable pieces.

**POST /api/v1/tasks/{task_id}/start**
   Mark task as started and begin tracking.

**POST /api/v1/tasks/{task_id}/complete**
   Mark task as completed with optional completion notes.

Time Blocking Endpoints
~~~~~~~~~~~~~~~~~~~~~~

**GET /api/v1/time-blocks/daily/{date}**
   Get complete daily schedule with visual positioning data.

   **Query Parameters**:
   - ``view_type``: View format (timeline, circular)
   - ``include_buffers``: Include buffer time blocks
   - ``include_completed``: Include completed blocks

   **Response**:

   .. code-block:: json

      {
        "data": {
          "date": "2024-01-15T00:00:00Z",
          "time_blocks": [
            {
              "id": "block_001",
              "title": "Morning Deep Work",
              "start_time": "2024-01-15T09:00:00Z",
              "end_time": "2024-01-15T11:00:00Z",
              "duration_minutes": 120,
              "block_type": "task",
              "buffer_before": 10,
              "buffer_after": 15,
              "color": "#3B82F6",
              "task_id": "550e8400-e29b-41d4-a716-446655440000",
              "visual_position": {
                "top": 180,
                "height": 120,
                "buffer_before_height": 10,
                "buffer_after_height": 15
              }
            }
          ],
          "total_scheduled_time": 480,
          "total_available_time": 960,
          "utilization_percentage": 50.0,
          "conflicts": [],
          "suggestions": [
            "Consider adding a break between long work sessions",
            "Schedule high-energy tasks during your peak hours (9-11 AM)"
          ],
          "view_data": {
            "hour_height": 60,
            "total_height": 960,
            "current_time_line": {
              "y": 240,
              "time": "10:00",
              "is_current": true
            }
          }
        }
      }

**POST /api/v1/time-blocks**
   Create a new time block with conflict checking.

**GET /api/v1/time-blocks/views/circular/{date}**
   Get circular clock view data for time blindness support.

**GET /api/v1/time-blocks/suggestions/{date}**
   Get AI-powered time slot suggestions for a task.

Focus Session Endpoints
~~~~~~~~~~~~~~~~~~~~~~

**POST /api/v1/focus/sessions**
   Create a new focus session.

   **Request Body**:

   .. code-block:: json

      {
        "session_type": "deep_work",
        "title": "Work on presentation slides",
        "planned_duration": 90,
        "break_duration": 15,
        "task_id": "550e8400-e29b-41d4-a716-446655440000",
        "focus_mode_id": "mode_deep_work",
        "enable_hyperfocus_protection": true,
        "hyperfocus_threshold": 120
      }

**POST /api/v1/focus/sessions/{session_id}/start**
   Start a focus session with environment setup.

**GET /api/v1/focus/sessions/{session_id}/hyperfocus-check**
   Check for hyperfocus state and get protection recommendations.

   **Response**:

   .. code-block:: json

      {
        "data": {
          "is_hyperfocus": true,
          "session_duration": 135,
          "threshold_exceeded": true,
          "alert": {
            "severity": "medium",
            "message": "You've been focused for over 2 hours. Consider taking a break soon.",
            "suggested_actions": [
              "Take a 10-15 minute break",
              "Hydrate and stretch",
              "Check in with your body",
              "Consider ending the session"
            ],
            "can_continue": true,
            "next_check_in": 15
          }
        }
      }

Notification Endpoints
~~~~~~~~~~~~~~~~~~~~~

**GET /api/v1/notifications**
   List user's notifications with ADHD-friendly filtering.

**POST /api/v1/notifications**
   Create a context-aware notification.

   **Request Body**:

   .. code-block:: json

      {
        "type": "task_reminder",
        "title": "Gentle Reminder: Project Deadline",
        "message": "Your project proposal is due in 2 hours. You've been working hard on this!",
        "scheduled_for": "2024-01-20T15:00:00Z",
        "priority": "high",
        "delivery_channels": ["push", "email"],
        "is_persistent": true,
        "respect_focus_mode": true,
        "max_snooze_count": 3,
        "task_id": "550e8400-e29b-41d4-a716-446655440000",
        "reminder_stages": [120, 60, 30, 15]
      }

**POST /api/v1/notifications/{notification_id}/acknowledge**
   Acknowledge a persistent reminder to stop escalation.

**POST /api/v1/notifications/{notification_id}/snooze**
   Snooze a notification with flexible timing.

   **Request Body**:

   .. code-block:: json

      {
        "snooze_duration": 15,
        "snooze_reason": "Need to finish current task first"
      }

Gamification Endpoints
~~~~~~~~~~~~~~~~~~~~~

**GET /api/v1/gamification/profile**
   Get user's gamification profile and current status.

**GET /api/v1/gamification/achievements**
   List available and earned achievements.

**POST /api/v1/gamification/dopamine-menu**
   Get personalized dopamine menu suggestions.

**GET /api/v1/gamification/leaderboard**
   Get community leaderboard (optional participation).

Body Doubling Endpoints
~~~~~~~~~~~~~~~~~~~~~~

**GET /api/v1/body-doubling/sessions**
   List available body doubling sessions.

**POST /api/v1/body-doubling/sessions**
   Create a new body doubling session.

**POST /api/v1/body-doubling/sessions/{session_id}/join**
   Join an existing body doubling session.

**WebSocket: /ws/body-doubling/{session_id}**
   Real-time communication for body doubling sessions.

Error Handling Examples
~~~~~~~~~~~~~~~~~~~~~

**Validation Error (400)**:

.. code-block:: json

   {
     "error": {
       "code": "VALIDATION_ERROR",
       "message": "The task title is required and cannot be empty.",
       "details": {
         "field": "title",
         "provided_value": "",
         "expected_format": "Non-empty string, 1-200 characters"
       },
       "suggestions": [
         "Provide a brief description of what you want to accomplish",
         "Try something like 'Review project proposal' or 'Call dentist'"
       ]
     }
   }

**Authentication Error (401)**:

.. code-block:: json

   {
     "error": {
       "code": "AUTHENTICATION_REQUIRED",
       "message": "Valid authentication token required.",
       "details": {
         "provided_token": "expired",
         "token_status": "expired"
       },
       "suggestions": [
         "Use the refresh token to get a new access token",
         "Log in again if refresh token is also expired"
       ],
       "help_url": "https://docs.chronos.app/api/authentication"
     }
   }

**Rate Limit Error (429)**:

.. code-block:: json

   {
     "error": {
       "code": "RATE_LIMIT_EXCEEDED",
       "message": "Too many requests. Please slow down and try again.",
       "details": {
         "limit": 100,
         "window": "60 seconds",
         "retry_after": 45
       },
       "suggestions": [
         "Wait 45 seconds before making another request",
         "Consider using batch endpoints for multiple operations",
         "Cache responses to reduce API calls"
       ]
     }
   }

SDK and Integration Examples
~~~~~~~~~~~~~~~~~~~~~~~~~~

**Python SDK Example**:

.. code-block:: python

   from chronos_sdk import ChronosClient

   # Initialize client
   client = ChronosClient(
       base_url="https://api.chronos.app",
       api_key="your_api_key"
   )

   # Create task with AI chunking
   task = client.tasks.create(
       title="Prepare quarterly presentation",
       use_ai_chunking=True,
       energy_level="high"
   )

   # Schedule focus session
   session = client.focus.create_session(
       task_id=task.id,
       session_type="deep_work",
       duration=90
   )

   # Start session
   client.focus.start_session(session.id)

**JavaScript/Node.js Example**:

.. code-block:: javascript

   const ChronosAPI = require('@chronos/api-client');

   const client = new ChronosAPI({
     baseURL: 'https://api.chronos.app',
     apiKey: 'your_api_key'
   });

   // Create and start focus session
   async function startFocusSession() {
     const session = await client.focus.createSession({
       title: 'Deep work on project',
       sessionType: 'deep_work',
       plannedDuration: 90,
       enableHyperfocusProtection: true
     });

     await client.focus.startSession(session.id);
     console.log('Focus session started!');
   }

This comprehensive API provides all the tools needed to build ADHD-friendly applications that truly serve the neurodivergent community with thoughtful, accommodating design.
