Integration Development Guide
=============================

This guide provides comprehensive instructions for developing new external service integrations for Project Chronos, with a focus on ADHD-optimized features and user experience.

.. currentmodule:: app.integrations

Development Philosophy
---------------------

ADHD-First Integration Design
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Every integration must prioritize ADHD user needs:

.. mermaid::

   graph TB
       subgraph "ADHD Considerations"
           Cognitive[Cognitive Load Reduction]
           Executive[Executive Function Support]
           Attention[Attention Pattern Respect]
           Emotional[Emotional Regulation]
       end
       
       subgraph "Technical Implementation"
           OAuth[OAuth 2.0 Security]
           Sync[Reliable Synchronization]
           Errors[Gentle Error Handling]
           Performance[Optimal Performance]
       end
       
       subgraph "User Experience"
           Setup[Easy Setup Process]
           Feedback[Clear Progress Feedback]
           Recovery[Error Recovery Assistance]
           Success[Success Celebration]
       end
       
       Cognitive --> OAuth
       Executive --> Sync
       Attention --> Errors
       Emotional --> Performance
       
       OAuth --> Setup
       Sync --> Feedback
       Errors --> Recovery
       Performance --> Success

**Core Principles:**
   - **Simplicity First**: Minimize cognitive load in setup and operation
   - **Gentle Guidance**: Supportive error messages and recovery assistance
   - **Intelligent Automation**: Reduce decision fatigue through smart defaults
   - **Emotional Safety**: Non-judgmental language and positive reinforcement

Creating a New Integration
-------------------------

Base Integration Class
~~~~~~~~~~~~~~~~~~~~~

All integrations must inherit from the BaseIntegration class:

.. code-block:: python

   from app.integrations.base import BaseIntegration, SyncResult
   from app.schemas.integration import OAuthTokens
   
   class MyServiceIntegration(BaseIntegration):
       """Integration for MyService with ADHD optimizations."""
       
       def __init__(self):
           super().__init__()
           self.base_url = "https://api.myservice.com/v1"
           self.oauth_url = "https://myservice.com/oauth/token"
           self.auth_url = "https://myservice.com/oauth/authorize"
           self.scopes = ["read", "write"]
           
       async def authenticate(self, credentials: Dict[str, Any]) -> OAuthTokens:
           """Implement OAuth 2.0 authentication."""
           # Implementation details...
           
       async def refresh_token(self, refresh_token: str) -> OAuthTokens:
           """Refresh OAuth access token."""
           # Implementation details...
           
       async def test_connection(self, access_token: str) -> bool:
           """Test API connectivity."""
           # Implementation details...

Required Methods
~~~~~~~~~~~~~~~

Every integration must implement these abstract methods:

**Authentication Methods:**

.. code-block:: python

   async def authenticate(self, credentials: Dict[str, Any]) -> OAuthTokens:
       """
       Exchange OAuth authorization code for access tokens.
       
       Args:
           credentials: Dict containing 'code', 'redirect_uri', and optionally 'state'
           
       Returns:
           OAuth tokens for API access
           
       Raises:
           IntegrationError: If authentication fails
       """
       
   async def refresh_token(self, refresh_token: str) -> OAuthTokens:
       """
       Refresh OAuth access token using refresh token.
       
       Args:
           refresh_token: OAuth refresh token
           
       Returns:
           New OAuth tokens
           
       Raises:
           IntegrationError: If token refresh fails
       """
       
   async def test_connection(self, access_token: str) -> bool:
       """
       Test if the connection to the external service is working.
       
       Args:
           access_token: OAuth access token
           
       Returns:
           True if connection is successful
       """

**Optional Sync Methods:**

Implement these based on your service's capabilities:

.. code-block:: python

   async def import_calendar_events(
       self,
       access_token: str,
       start_date: Optional[datetime] = None,
       end_date: Optional[datetime] = None,
       calendar_ids: Optional[List[str]] = None
   ) -> SyncResult:
       """Import calendar events with ADHD optimizations."""
       
   async def export_calendar_events(
       self,
       access_token: str,
       events: List[CalendarEventSync],
       calendar_id: Optional[str] = None
   ) -> SyncResult:
       """Export calendar events to external service."""
       
   async def import_tasks(
       self,
       access_token: str,
       project_ids: Optional[List[str]] = None,
       completed: Optional[bool] = None
   ) -> SyncResult:
       """Import tasks with ADHD categorization."""
       
   async def export_tasks(
       self,
       access_token: str,
       tasks: List[TaskSync],
       project_id: Optional[str] = None
   ) -> SyncResult:
       """Export tasks to external service."""

ADHD-Specific Features
---------------------

Energy Level Detection
~~~~~~~~~~~~~~~~~~~~~

Implement intelligent energy level detection from content:

.. code-block:: python

   def _detect_energy_level(self, title: str, description: str = "") -> str:
       """
       Detect required energy level from task/event content.
       
       Args:
           title: Task or event title
           description: Optional description
           
       Returns:
           Energy level: "low", "medium", or "high"
       """
       content = f"{title} {description}".lower()
       
       # High energy indicators
       high_energy_keywords = [
           "focus", "deep work", "complex", "difficult", "analyze",
           "create", "design", "write", "plan", "strategy"
       ]
       
       # Low energy indicators  
       low_energy_keywords = [
           "quick", "easy", "simple", "check", "review", "read",
           "5min", "routine", "admin", "organize"
       ]
       
       if any(keyword in content for keyword in high_energy_keywords):
           return "high"
       elif any(keyword in content for keyword in low_energy_keywords):
           return "low"
       else:
           return "medium"

Complexity Assessment
~~~~~~~~~~~~~~~~~~~~

Evaluate task complexity for better chunking:

.. code-block:: python

   def _assess_complexity(self, title: str, description: str = "", labels: List[str] = None) -> str:
       """
       Assess task complexity for ADHD users.
       
       Args:
           title: Task title
           description: Task description
           labels: Associated labels/tags
           
       Returns:
           Complexity level: "low", "medium", or "high"
       """
       content = f"{title} {description}".lower()
       labels = labels or []
       
       # Complexity indicators
       high_complexity = [
           "project", "research", "analysis", "multiple", "several",
           "complex", "difficult", "comprehensive", "detailed"
       ]
       
       low_complexity = [
           "quick", "simple", "easy", "one", "single", "basic",
           "straightforward", "routine", "standard"
       ]
       
       # Check content and labels
       complexity_score = 0
       
       if any(keyword in content for keyword in high_complexity):
           complexity_score += 2
       if any(keyword in content for keyword in low_complexity):
           complexity_score -= 1
       if any("complex" in label.lower() for label in labels):
           complexity_score += 1
       if any("simple" in label.lower() for label in labels):
           complexity_score -= 1
           
       if complexity_score >= 2:
           return "high"
       elif complexity_score <= -1:
           return "low"
       else:
           return "medium"

Duration Estimation
~~~~~~~~~~~~~~~~~~

Provide intelligent time estimates:

.. code-block:: python

   def _estimate_duration(self, title: str, labels: List[str] = None, complexity: str = "medium") -> Optional[int]:
       """
       Estimate task duration in minutes.
       
       Args:
           title: Task title
           labels: Associated labels/tags
           complexity: Task complexity level
           
       Returns:
           Estimated duration in minutes, or None if cannot estimate
       """
       labels = labels or []
       
       # Check for explicit time estimates in labels
       import re
       for label in labels:
           # Look for patterns like "30min", "2h", "1.5hr"
           time_match = re.search(r'(\d+(?:\.\d+)?)\s*(min|hour|hr|h)', label.lower())
           if time_match:
               duration = float(time_match.group(1))
               unit = time_match.group(2)
               if unit in ["hour", "hr", "h"]:
                   duration *= 60
               return int(duration)
       
       # Check title for time indicators
       title_lower = title.lower()
       if "quick" in title_lower or "5min" in title_lower:
           return 5
       elif "brief" in title_lower or "short" in title_lower:
           return 15
       
       # Base estimates on complexity
       complexity_estimates = {
           "low": 15,
           "medium": 30,
           "high": 60
       }
       
       return complexity_estimates.get(complexity, 30)

Data Conversion Helpers
----------------------

Calendar Event Conversion
~~~~~~~~~~~~~~~~~~~~~~~~

Convert external calendar events to ADHD-optimized format:

.. code-block:: python

   def _convert_external_event(self, external_event: Dict[str, Any]) -> Optional[CalendarEventSync]:
       """
       Convert external calendar event to CalendarEventSync with ADHD features.
       
       Args:
           external_event: Raw event data from external service
           
       Returns:
           CalendarEventSync object with ADHD optimizations
       """
       try:
           # Extract basic event information
           title = external_event.get("title", "")
           description = external_event.get("description", "")
           start_time = self._parse_datetime(external_event.get("start_time"))
           end_time = self._parse_datetime(external_event.get("end_time"))
           
           if not start_time or not end_time:
               return None
           
           # ADHD-specific processing
           energy_level = self._detect_energy_level(title, description)
           is_focus_time = self._detect_focus_time(title, description)
           
           # Calculate appropriate buffer times
           duration_minutes = (end_time - start_time).total_seconds() / 60
           buffer_before = 5 if duration_minutes > 30 else 2
           buffer_after = 5 if duration_minutes > 30 else 2
           
           return CalendarEventSync(
               external_id=external_event["id"],
               title=title,
               description=description,
               start_time=start_time,
               end_time=end_time,
               all_day=external_event.get("all_day", False),
               location=external_event.get("location"),
               attendees=external_event.get("attendees", []),
               energy_level=energy_level,
               buffer_before=buffer_before,
               buffer_after=buffer_after,
               is_focus_time=is_focus_time
           )
           
       except Exception as e:
           logger.error(f"Error converting external event: {e}")
           return None
   
   def _detect_focus_time(self, title: str, description: str = "") -> bool:
       """Detect if event is focus/deep work time."""
       content = f"{title} {description}".lower()
       focus_keywords = [
           "focus", "deep work", "concentrate", "coding", "writing",
           "analysis", "design", "planning", "strategy", "research"
       ]
       return any(keyword in content for keyword in focus_keywords)

Task Conversion
~~~~~~~~~~~~~~

Convert external tasks with ADHD categorization:

.. code-block:: python

   def _convert_external_task(self, external_task: Dict[str, Any]) -> Optional[TaskSync]:
       """
       Convert external task to TaskSync with ADHD optimizations.
       
       Args:
           external_task: Raw task data from external service
           
       Returns:
           TaskSync object with ADHD features
       """
       try:
           title = external_task.get("title", "")
           description = external_task.get("description", "")
           labels = external_task.get("labels", [])
           
           # ADHD-specific analysis
           energy_level = self._detect_energy_level(title, description)
           complexity = self._assess_complexity(title, description, labels)
           estimated_duration = self._estimate_duration(title, labels, complexity)
           
           # Determine chunk size for complex tasks
           chunk_size = None
           if complexity == "high":
               chunk_size = 25  # 25-minute chunks for complex tasks
           elif complexity == "medium" and estimated_duration and estimated_duration > 60:
               chunk_size = 30  # 30-minute chunks for longer medium tasks
           
           return TaskSync(
               external_id=external_task["id"],
               title=title,
               description=description,
               due_date=self._parse_datetime(external_task.get("due_date")),
               completed=external_task.get("completed", False),
               priority=external_task.get("priority", "medium"),
               labels=labels,
               project_id=external_task.get("project_id"),
               energy_level=energy_level,
               complexity=complexity,
               estimated_duration=estimated_duration,
               chunk_size=chunk_size
           )
           
       except Exception as e:
           logger.error(f"Error converting external task: {e}")
           return None

Error Handling
-------------

ADHD-Friendly Error Messages
~~~~~~~~~~~~~~~~~~~~~~~~~~~

Provide supportive, actionable error messages:

.. code-block:: python

   class ADHDFriendlyError(IntegrationError):
       """Error with ADHD-friendly messaging."""
       
       def __init__(self, message: str, suggestions: List[str] = None, recovery_steps: List[str] = None):
           super().__init__(message)
           self.suggestions = suggestions or []
           self.recovery_steps = recovery_steps or []
           self.tone = "supportive"
   
   def _handle_api_error(self, error_response: Dict[str, Any], operation: str) -> None:
       """Handle API errors with ADHD-friendly messaging."""
       
       error_code = error_response.get("error", "unknown_error")
       
       if error_code == "rate_limit_exceeded":
           raise ADHDFriendlyError(
               message="You're syncing a lot today! Let's take a quick break.",
               suggestions=[
                   "This helps protect both you and the external service",
                   "Your sync will automatically resume in a few minutes",
                   "Consider using scheduled syncs for large operations"
               ],
               recovery_steps=[
                   "Wait for the rate limit to reset",
                   "Try again in 5-10 minutes",
                   "Contact support if this happens frequently"
               ]
           )
       elif error_code == "invalid_token":
           raise ADHDFriendlyError(
               message="Your connection needs a quick refresh - this is totally normal!",
               suggestions=[
                   "This happens periodically for security reasons",
                   "Your data is completely safe",
                   "Reconnecting takes just a minute"
               ],
               recovery_steps=[
                   "Go to your integration settings",
                   "Click 'Reconnect Integration'",
                   "Complete the OAuth flow again"
               ]
           )
       else:
           raise ADHDFriendlyError(
               message=f"Something went wrong with {operation}, but we can fix this together!",
               suggestions=[
                   "These things happen sometimes with external services",
                   "Your data is safe and we'll help you get back on track",
                   "Try the operation again in a few minutes"
               ]
           )

Webhook Implementation
---------------------

Real-time Event Processing
~~~~~~~~~~~~~~~~~~~~~~~~~

Implement webhook handling for real-time updates:

.. code-block:: python

   async def process_webhook_event(
       self,
       event_data: Dict[str, Any],
       headers: Dict[str, str]
   ) -> SyncResult:
       """
       Process incoming webhook event with ADHD considerations.
       
       Args:
           event_data: Webhook event payload
           headers: HTTP headers from webhook request
           
       Returns:
           SyncResult from processing the event
       """
       try:
           # Verify webhook signature
           if not self._verify_webhook_signature(event_data, headers):
               raise IntegrationError("Invalid webhook signature")
           
           event_type = event_data.get("type", "unknown")
           
           # Process different event types
           if event_type == "task_updated":
               return await self._process_task_update(event_data)
           elif event_type == "calendar_event_changed":
               return await self._process_calendar_update(event_data)
           else:
               logger.warning(f"Unknown webhook event type: {event_type}")
               return SyncResult(success=True, items_processed=0)
               
       except Exception as e:
           logger.error(f"Webhook processing failed: {e}")
           return SyncResult(
               success=False,
               errors=[f"Webhook processing failed: {e}"]
           )
   
   def _verify_webhook_signature(self, event_data: Dict[str, Any], headers: Dict[str, str]) -> bool:
       """Verify webhook signature for security."""
       # Implementation depends on external service's signature method
       return True  # Placeholder

Testing Integration
------------------

Unit Testing
~~~~~~~~~~~

Create comprehensive tests for your integration:

.. code-block:: python

   import pytest
   from unittest.mock import AsyncMock, patch
   from app.integrations.my_service import MyServiceIntegration
   
   @pytest.fixture
   async def integration():
       """Create integration instance for testing."""
       integration = MyServiceIntegration()
       await integration._create_session()
       yield integration
       await integration._close_session()
   
   @pytest.mark.asyncio
   async def test_authentication_success(integration):
       """Test successful OAuth authentication."""
       mock_response = {
           "access_token": "test_access_token",
           "refresh_token": "test_refresh_token",
           "expires_in": 3600
       }
       
       with patch.object(integration, '_make_request', return_value=mock_response):
           tokens = await integration.authenticate({
               "code": "test_code",
               "redirect_uri": "https://test.com/callback"
           })
           
           assert tokens.access_token == "test_access_token"
           assert tokens.refresh_token == "test_refresh_token"
   
   @pytest.mark.asyncio
   async def test_adhd_energy_detection(integration):
       """Test ADHD energy level detection."""
       # Test high energy detection
       energy = integration._detect_energy_level("Deep focus coding session")
       assert energy == "high"
       
       # Test low energy detection
       energy = integration._detect_energy_level("Quick email check")
       assert energy == "low"
       
       # Test medium energy default
       energy = integration._detect_energy_level("Regular meeting")
       assert energy == "medium"

Integration Testing
~~~~~~~~~~~~~~~~~~

Test with actual external service (in development environment):

.. code-block:: python

   @pytest.mark.integration
   @pytest.mark.asyncio
   async def test_real_api_connection():
       """Test actual API connection (requires valid credentials)."""
       integration = MyServiceIntegration()
       
       # Use test credentials
       access_token = "test_access_token_from_oauth"
       
       # Test connection
       is_connected = await integration.test_connection(access_token)
       assert is_connected
       
       # Test data import
       result = await integration.import_tasks(access_token)
       assert result.success
       assert result.items_processed >= 0

Documentation
------------

Integration Documentation
~~~~~~~~~~~~~~~~~~~~~~~~~

Document your integration thoroughly:

.. code-block:: rst

   MyService Integration
   ====================
   
   .. autoclass:: app.integrations.my_service.MyServiceIntegration
      :members:
      :show-inheritance:
   
   **ADHD-Optimized Features:**
      - Energy-aware task categorization
      - Intelligent duration estimation
      - Complexity assessment for chunking
      - Gentle error handling and recovery
   
   **Setup Instructions:**
      1. Create MyService developer account
      2. Register OAuth application
      3. Configure credentials in Chronos
      4. Complete OAuth flow
   
   **Supported Operations:**
      - Bidirectional task synchronization
      - Real-time webhook updates
      - Bulk import/export operations
      - Health monitoring and diagnostics

Deployment Checklist
-------------------

Before deploying your integration:

**Security Review:**
   - [ ] OAuth implementation follows security best practices
   - [ ] Webhook signatures are properly verified
   - [ ] Sensitive data is encrypted at rest
   - [ ] Rate limiting is implemented
   - [ ] Error messages don't leak sensitive information

**ADHD Features:**
   - [ ] Energy level detection is accurate
   - [ ] Complexity assessment works correctly
   - [ ] Duration estimation is reasonable
   - [ ] Error messages are supportive and actionable
   - [ ] Progress feedback is clear and encouraging

**Testing:**
   - [ ] Unit tests cover all major functionality
   - [ ] Integration tests pass with real API
   - [ ] Error scenarios are properly handled
   - [ ] Performance is acceptable under load
   - [ ] ADHD features work as expected

**Documentation:**
   - [ ] API documentation is complete
   - [ ] Setup instructions are clear
   - [ ] ADHD features are well documented
   - [ ] Troubleshooting guide is available
   - [ ] Examples are provided for common use cases

This comprehensive guide ensures that new integrations maintain the high standards of ADHD accommodation and user experience that Project Chronos users depend on.
