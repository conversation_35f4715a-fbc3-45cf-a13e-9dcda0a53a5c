Development Environment Setup
==============================

This guide will help you set up a local development environment for Project Chronos. The setup process is designed to be straightforward and ADHD-friendly, with clear steps and helpful troubleshooting.

.. currentmodule:: app

Prerequisites
------------

System Requirements
~~~~~~~~~~~~~~~~~~

**Operating System**
   - Linux (Ubuntu 20.04+ recommended)
   - macOS (10.15+ recommended)
   - Windows 10/11 with WSL2

**Required Software**
   - Python 3.11 or higher
   - PostgreSQL 13 or higher
   - Redis 6 or higher
   - Git
   - Docker and Docker Compose (optional but recommended)

**Hardware Recommendations**
   - 8GB RAM minimum (16GB recommended for comfortable development)
   - 20GB free disk space
   - Multi-core processor for background task processing

Quick Setup with Docker
-----------------------

The fastest way to get started is using Docker Compose:

.. mermaid::

   graph LR
       Clone[Clone Repository] --> Docker[Start Docker Services]
       Docker --> Database[Initialize Database]
       Database --> Run[Run Application]
       Run --> Test[Run Tests]

**Step 1: Clone the Repository**

.. code-block:: bash

   git clone https://github.com/your-org/project-chronos.git
   cd project-chronos

**Step 2: Start Services with Docker Compose**

.. code-block:: bash

   # Start all services (PostgreSQL, Redis, API, Workers)
   docker-compose up -d
   
   # Check that all services are running
   docker-compose ps

**Step 3: Initialize the Database**

.. code-block:: bash

   # Run database migrations
   docker-compose exec api alembic upgrade head
   
   # Create a test user (optional)
   docker-compose exec api python scripts/create_test_user.py

**Step 4: Access the Application**

- **API Documentation**: http://localhost:8000/docs
- **API Base URL**: http://localhost:8000/api/v1
- **Redis Commander**: http://localhost:8081 (for Redis monitoring)

Manual Setup (Local Development)
--------------------------------

For developers who prefer local setup or need to modify the development environment:

Python Environment Setup
~~~~~~~~~~~~~~~~~~~~~~~~

**1. Install Python Dependencies**

.. code-block:: bash

   # Create virtual environment
   python -m venv venv
   
   # Activate virtual environment
   # On Linux/macOS:
   source venv/bin/activate
   # On Windows:
   venv\Scripts\activate
   
   # Install Poetry (dependency manager)
   pip install poetry
   
   # Install project dependencies
   poetry install

**2. Environment Configuration**

.. code-block:: bash

   # Copy environment template
   cp .env.example .env
   
   # Edit .env file with your local settings
   nano .env

**Example .env Configuration**:

.. code-block:: bash

   # Database
   DATABASE_URL=postgresql+asyncpg://chronos:chronos@localhost/chronos_dev
   
   # Redis
   REDIS_URL=redis://localhost:6379/0
   
   # Security
   SECRET_KEY=your-secret-key-here
   JWT_SECRET_KEY=your-jwt-secret-here
   
   # Development settings
   DEBUG=true
   ENVIRONMENT=development
   
   # External services (optional for development)
   OPENAI_API_KEY=your-openai-key-here
   EMAIL_SMTP_HOST=localhost
   EMAIL_SMTP_PORT=1025

Database Setup
~~~~~~~~~~~~~

**1. Install and Configure PostgreSQL**

.. code-block:: bash

   # Ubuntu/Debian
   sudo apt update
   sudo apt install postgresql postgresql-contrib
   
   # macOS with Homebrew
   brew install postgresql
   brew services start postgresql
   
   # Create database and user
   sudo -u postgres psql
   CREATE DATABASE chronos_dev;
   CREATE USER chronos WITH PASSWORD 'chronos';
   GRANT ALL PRIVILEGES ON DATABASE chronos_dev TO chronos;
   \q

**2. Run Database Migrations**

.. code-block:: bash

   # Initialize Alembic (if not already done)
   alembic init alembic
   
   # Run migrations
   alembic upgrade head
   
   # Verify database setup
   python scripts/verify_db.py

Redis Setup
~~~~~~~~~~

**1. Install and Start Redis**

.. code-block:: bash

   # Ubuntu/Debian
   sudo apt install redis-server
   sudo systemctl start redis-server
   
   # macOS with Homebrew
   brew install redis
   brew services start redis
   
   # Verify Redis is running
   redis-cli ping
   # Should return: PONG

Development Workflow
-------------------

Running the Application
~~~~~~~~~~~~~~~~~~~~~~

.. mermaid::

   graph TB
       Start[Start Development] --> API[Start API Server]
       API --> Workers[Start Celery Workers]
       Workers --> Beat[Start Celery Beat]
       Beat --> Test[Run Tests]
       Test --> Code[Write Code]
       Code --> API

**1. Start the API Server**

.. code-block:: bash

   # Development server with auto-reload
   uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
   
   # Or using the development script
   python scripts/run_dev.py

**2. Start Background Workers**

.. code-block:: bash

   # In a separate terminal, start Celery worker
   celery -A app.workers.celery_app worker --loglevel=info
   
   # In another terminal, start Celery beat scheduler
   celery -A app.workers.celery_app beat --loglevel=info

**3. Monitor Services**

.. code-block:: bash

   # Monitor Celery tasks
   celery -A app.workers.celery_app flower
   # Access at: http://localhost:5555
   
   # Monitor Redis
   redis-cli monitor

Code Quality and Testing
~~~~~~~~~~~~~~~~~~~~~~~

**Pre-commit Hooks**

.. code-block:: bash

   # Install pre-commit hooks
   pre-commit install
   
   # Run hooks manually
   pre-commit run --all-files

**Code Formatting and Linting**

.. code-block:: bash

   # Format code with Black
   black app/ tests/
   
   # Sort imports with isort
   isort app/ tests/
   
   # Type checking with mypy
   mypy app/
   
   # Linting with flake8
   flake8 app/ tests/

**Running Tests**

.. code-block:: bash

   # Run all tests
   pytest
   
   # Run with coverage
   pytest --cov=app --cov-report=html
   
   # Run specific test categories
   pytest tests/unit/
   pytest tests/integration/
   pytest tests/api/
   
   # Run tests for specific ADHD features
   pytest -k "adhd" -v

Database Management
------------------

Migration Workflow
~~~~~~~~~~~~~~~~~

.. mermaid::

   sequenceDiagram
       participant Dev as Developer
       participant Models as SQLAlchemy Models
       participant Alembic as Alembic
       participant DB as Database
       
       Dev->>Models: Modify models
       Dev->>Alembic: Generate migration
       Alembic->>Models: Detect changes
       Alembic->>Alembic: Create migration file
       Dev->>Alembic: Review migration
       Dev->>Alembic: Apply migration
       Alembic->>DB: Execute SQL changes

**Creating Migrations**

.. code-block:: bash

   # Generate migration for model changes
   alembic revision --autogenerate -m "Add notification preferences"
   
   # Review the generated migration file
   # Edit if necessary: alembic/versions/xxx_add_notification_preferences.py
   
   # Apply migration
   alembic upgrade head

**Database Utilities**

.. code-block:: bash

   # Reset database (development only)
   python scripts/reset_db.py
   
   # Seed database with test data
   python scripts/seed_db.py
   
   # Create test user with ADHD preferences
   python scripts/create_test_user.py --adhd-profile

Development Tools and IDE Setup
------------------------------

VS Code Configuration
~~~~~~~~~~~~~~~~~~~

**Recommended Extensions**:
   - Python
   - Pylance
   - Black Formatter
   - isort
   - GitLens
   - REST Client
   - Docker

**VS Code Settings** (`.vscode/settings.json`):

.. code-block:: json

   {
     "python.defaultInterpreterPath": "./venv/bin/python",
     "python.formatting.provider": "black",
     "python.linting.enabled": true,
     "python.linting.pylintEnabled": false,
     "python.linting.flake8Enabled": true,
     "python.sortImports.args": ["--profile", "black"],
     "editor.formatOnSave": true,
     "editor.codeActionsOnSave": {
       "source.organizeImports": true
     }
   }

**Launch Configuration** (`.vscode/launch.json`):

.. code-block:: json

   {
     "version": "0.2.0",
     "configurations": [
       {
         "name": "FastAPI",
         "type": "python",
         "request": "launch",
         "program": "${workspaceFolder}/venv/bin/uvicorn",
         "args": ["app.main:app", "--reload", "--host", "0.0.0.0", "--port", "8000"],
         "console": "integratedTerminal",
         "env": {
           "PYTHONPATH": "${workspaceFolder}"
         }
       }
     ]
   }

API Testing and Documentation
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Interactive API Documentation**
   - **Swagger UI**: http://localhost:8000/docs
   - **ReDoc**: http://localhost:8000/redoc

**API Testing with HTTPie**

.. code-block:: bash

   # Install HTTPie
   pip install httpie
   
   # Test authentication
   http POST localhost:8000/api/v1/auth/login email=<EMAIL> password=testpass
   
   # Test with authentication token
   http GET localhost:8000/api/v1/tasks Authorization:"Bearer YOUR_TOKEN"
   
   # Create a task with ADHD features
   http POST localhost:8000/api/v1/tasks \
     Authorization:"Bearer YOUR_TOKEN" \
     title="Test Task" \
     use_ai_chunking:=true \
     energy_level="medium"

Troubleshooting
--------------

Common Issues and Solutions
~~~~~~~~~~~~~~~~~~~~~~~~~

**Database Connection Issues**

.. code-block:: bash

   # Check PostgreSQL status
   sudo systemctl status postgresql
   
   # Check if database exists
   psql -U chronos -d chronos_dev -c "\l"
   
   # Reset database connection
   python scripts/test_db_connection.py

**Redis Connection Issues**

.. code-block:: bash

   # Check Redis status
   redis-cli ping
   
   # Check Redis configuration
   redis-cli config get "*"
   
   # Clear Redis cache
   redis-cli flushall

**Celery Worker Issues**

.. code-block:: bash

   # Check Celery worker status
   celery -A app.workers.celery_app inspect active
   
   # Purge all tasks
   celery -A app.workers.celery_app purge
   
   # Restart workers
   pkill -f "celery worker"
   celery -A app.workers.celery_app worker --loglevel=info

**Import and Path Issues**

.. code-block:: bash

   # Verify Python path
   python -c "import sys; print('\n'.join(sys.path))"
   
   # Install package in development mode
   pip install -e .

**Performance Issues**

.. code-block:: bash

   # Profile API endpoints
   python scripts/profile_api.py
   
   # Monitor database queries
   python scripts/monitor_db_queries.py
   
   # Check memory usage
   python scripts/memory_profiler.py

ADHD-Friendly Development Tips
-----------------------------

Productivity Strategies
~~~~~~~~~~~~~~~~~~~~~~

**Managing Development Focus**
   - Use the Pomodoro technique: 25-minute focused coding sessions
   - Take regular breaks to prevent hyperfocus burnout
   - Keep a development journal to track progress and ideas
   - Use TODO comments liberally to capture thoughts

**Handling Overwhelm**
   - Break large features into small, manageable tasks
   - Use feature branches for experimental work
   - Commit frequently with descriptive messages
   - Don't hesitate to ask for help in the community

**Maintaining Motivation**
   - Celebrate small wins and completed features
   - Share progress with the ADHD developer community
   - Use the gamification features you're building
   - Remember that you're building tools that help people like you

**Code Organization**
   - Use clear, descriptive variable and function names
   - Write docstrings for complex functions
   - Keep functions small and focused
   - Use type hints for better IDE support

This development setup is designed to be as smooth as possible while providing all the tools needed to contribute effectively to Project Chronos. Remember, the goal is to create technology that truly serves the ADHD community!
