# Agent 3: Task Management & AI Chunking - Implementation Guide

## Overview

Agent 3 provides the core task management functionality for Project Chronos, with ADHD-optimized features including AI-powered task chunking, adaptive filtering, and decision fatigue reduction through the "task jar" feature.

## Architecture

### Service Layer
- **TaskService**: Core CRUD operations with ADHD-specific features
- **AIChunkingService**: AI-powered task breakdown using OpenAI/Anthropic
- **AdaptiveFilterService**: Context-aware filtering and task jar functionality

### Key Features Implemented

#### 1. ADHD-Optimized Task Management
- Energy level tracking (low/medium/high)
- Context tags for situational filtering
- Hierarchical task relationships for chunking
- Completion momentum tracking

#### 2. AI-Powered Task Chunking
- Dual AI provider support (OpenAI GPT-4 + Anthropic Claude)
- ADHD-specific prompt engineering
- Three chunk sizes: small (5-15 min), medium (15-45 min), large (1-3 hours)
- Response validation and error handling

#### 3. Adaptive Task Filtering
- Energy level matching algorithms
- Context-aware task selection
- Time-based urgency scoring
- Quick wins identification

#### 4. Task Jar Feature
- Weighted random task selection
- Decision fatigue reduction
- Configurable jar size (1-20 tasks)
- Recent task exclusion

## API Endpoints

### Core Task Operations
```
POST   /api/v1/tasks/              # Create task
GET    /api/v1/tasks/              # List tasks with filtering
GET    /api/v1/tasks/{id}          # Get specific task
PUT    /api/v1/tasks/{id}          # Update task
DELETE /api/v1/tasks/{id}          # Delete task
```

### ADHD-Specific Features
```
POST   /api/v1/tasks/{id}/chunk    # AI task chunking
GET    /api/v1/tasks/jar/shake     # Task jar selection
GET    /api/v1/tasks/energy/{level} # Energy-matched tasks
GET    /api/v1/tasks/quick-wins    # Quick win tasks
GET    /api/v1/tasks/stats/completion # Completion statistics
```

## Usage Examples

### Creating an ADHD-Optimized Task
```python
task_data = TaskCreate(
    title="Prepare quarterly presentation",
    description="Create slides for Q4 business review",
    priority="high",
    energy_level="high",  # Requires high energy
    estimated_duration=120,  # 2 hours
    context_tags=["work", "computer", "presentation"],
    due_date="2024-01-15T10:00:00Z"
)
```

### AI Task Chunking
```python
chunk_request = TaskChunkRequest(
    chunk_size="small",  # 5-15 minute chunks
    max_subtasks=5,
    include_time_estimates=True,
    context="I have ADHD and get overwhelmed by big projects"
)

# Results in subtasks like:
# 1. "Open PowerPoint and create new presentation" (10 min)
# 2. "Draft title slide with company branding" (15 min)
# 3. "Create outline with 5 main sections" (10 min)
```

### Adaptive Task Filtering
```python
# Get tasks suitable for current low energy state
filter_request = TaskFilterRequest(
    energy_level="low",
    max_duration=30,  # 30 minutes max
    context_tags=["home", "phone"],
    limit=10
)
```

### Task Jar for Decision Fatigue
```python
jar_request = TaskJarRequest(
    jar_size=5,  # Get 5 random tasks
    energy_level="medium",
    exclude_recent=True  # Don't repeat recent tasks
)
```

## ADHD-Specific Optimizations

### 1. Energy Level Matching
Tasks are scored based on energy requirements vs. current energy:
- **Perfect match**: 5.0 points
- **Task requires less energy**: 4.0 points
- **Task requires more energy**: Penalty applied

### 2. Context Awareness
Tasks are filtered by situational context:
- Location tags: "home", "office", "anywhere"
- Tool requirements: "computer", "phone", "paper"
- Social context: "alone", "meeting", "team"

### 3. Decision Fatigue Reduction
- Task jar provides curated random selection
- Weighted randomization prioritizes urgent tasks
- Excludes recently completed tasks for variety

### 4. Momentum Building
- Quick wins feature for low-energy periods
- Completion statistics tracking
- Progress visualization support

## AI Prompt Engineering

### Small Chunks (5-15 minutes)
Optimized for ADHD users who need immediate action:
- Single, concrete actions
- No decision-making required
- Clear "done" state
- Removes all ambiguity

### Medium Chunks (15-45 minutes)
Focused work sessions:
- Completable in one sitting
- Clear boundaries and outcomes
- Builds momentum toward larger goal

### Large Chunks (1-3 hours)
Deep work phases:
- Major project milestones
- Suitable for hyperfocus sessions
- Logical project phases

## Testing

### Unit Tests
- TaskService CRUD operations
- AI response validation
- Energy level matching algorithms
- Context tag processing

### Coverage Areas
- ADHD-specific features
- Error handling and edge cases
- AI service fallbacks
- Database operations

## Configuration

### Environment Variables
```bash
CHRONOS_OPENAI_API_KEY=your_openai_key
CHRONOS_ANTHROPIC_API_KEY=your_anthropic_key
CHRONOS_AI_CHUNKING_ENABLED=true
CHRONOS_AI_CHUNKING_CACHE_TTL=3600
CHRONOS_DEFAULT_CHUNK_SIZE=small
CHRONOS_MAX_SUBTASKS_PER_CHUNK=7
```

### ADHD-Specific Settings
```python
DEFAULT_CHUNK_SIZE = "small"  # Conservative default
MAX_SUBTASKS_PER_CHUNK = 7    # Prevent overwhelm
DEFAULT_FOCUS_DURATION = 25   # Pomodoro-style
HYPERFOCUS_WARNING_THRESHOLD = 120  # 2 hours
```

## Integration Points

### Provides to Other Agents
- Task management APIs and services
- AI chunking capabilities
- Adaptive filtering algorithms
- Task context and metadata

### Requires from Other Agents
- User authentication (Agent 2) - Currently mocked
- Time blocking integration (Agent 4)
- Notification triggers (Agent 7)

## Next Steps

1. **Integration with Agent 2**: Replace mock authentication
2. **Agent 4 Integration**: Connect with time blocking
3. **Performance Optimization**: Add caching and indexing
4. **User Feedback Loop**: Implement AI learning from user interactions
5. **Advanced Analytics**: Task completion pattern analysis

## Troubleshooting

### Common Issues
1. **AI Service Unavailable**: Automatic fallback between providers
2. **Invalid Chunk Responses**: Validation and error handling
3. **Energy Level Mismatches**: Scoring algorithm adjustments
4. **Context Tag Conflicts**: Tag normalization and cleanup

### Monitoring
- AI service response times
- Chunking success rates
- User engagement with filtered tasks
- Task completion rates by energy level
