# Focus Sessions & Pomodoro System

## Overview

The Focus Sessions & Pomodoro system (Agent 5) provides ADHD-friendly focus management with gentle interventions, hyperfocus protection, and real-time timer functionality. This system is designed to work with neurodivergent attention patterns rather than against them.

## Key Features

### 🎯 Unobtrusive Focus Timers
- Customizable session durations (Pomodoro, Deep Work, Sprint, Custom)
- Gentle, non-jarring audio cues with volume control
- Visual progress indicators that don't demand attention
- Pause/resume functionality for hyperfocus protection

### 🛡️ Hyperfocus Protection
- Automatic detection of extended focus sessions
- Gentle reminders that don't force interruption
- "Continue working" vs "Take break" choice prompts
- Flexible break scheduling based on user patterns

### 🔕 Focus Mode Management
- Custom focus mode profiles (Writing, Studying, Creative, etc.)
- Notification shielding during focus sessions
- Automatic focus mode activation based on scheduled time blocks
- Emergency contact breakthrough options

### 📊 Focus Analytics
- Session completion rates and patterns
- Optimal focus time identification
- Hyperfocus incident tracking
- Productivity trend analysis

## API Endpoints

### Session Management

```http
POST /api/v1/focus/sessions
```
Create a new focus session with ADHD-optimized settings.

```http
POST /api/v1/focus/sessions/{session_id}/start
```
Start a planned focus session and activate timer.

```http
PUT /api/v1/focus/sessions/{session_id}/pause
```
Pause an active focus session with optional reason.

```http
PUT /api/v1/focus/sessions/{session_id}/resume
```
Resume a paused focus session.

```http
POST /api/v1/focus/sessions/{session_id}/complete
```
Complete a focus session with optional notes.

### Real-time Timer

```http
GET /api/v1/focus/sessions/{session_id}/timer
```
Get real-time timer state with elapsed and remaining time.

```http
POST /api/v1/focus/sessions/{session_id}/extend
```
Extend session duration (useful for hyperfocus protection).

### Analytics

```http
GET /api/v1/focus/analytics?days=30
```
Get focus session analytics and productivity insights.

## WebSocket Integration

### Real-time Updates

Connect to WebSocket endpoint for live timer updates:

```javascript
const ws = new WebSocket('ws://localhost:8000/ws/focus/{user_id}');

ws.onmessage = function(event) {
    const data = JSON.parse(event.data);
    
    switch(data.type) {
        case 'timer_update':
            updateTimerDisplay(data.data);
            break;
        case 'break_reminder':
            showGentleReminder(data.data);
            break;
        case 'hyperfocus_alert':
            showHyperfocusAlert(data.data);
            break;
    }
};
```

### Message Types

- `timer_update`: Real-time timer state updates
- `break_reminder`: Gentle break suggestions
- `hyperfocus_alert`: Hyperfocus detection notifications
- `connection_established`: WebSocket connection confirmation

## Default Focus Modes

### Pomodoro Classic
- **Duration**: 25 minutes work, 5 minute breaks
- **Long Break**: 15 minutes after 4 sessions
- **Best For**: Traditional productivity workflows

### Deep Work
- **Duration**: 90 minutes work, 20 minute breaks
- **Features**: Minimal notifications, longer focus periods
- **Best For**: Complex tasks requiring sustained attention

### Creative Flow
- **Duration**: Flexible (60 minutes default)
- **Features**: Gentle reminders, hyperfocus protection
- **Best For**: Creative work and artistic projects

### Study Session
- **Duration**: 45 minutes work, 15 minute breaks
- **Features**: Review reminders, structured breaks
- **Best For**: Learning and academic work

### Quick Sprint
- **Duration**: 15 minutes work, 5 minute breaks
- **Features**: Minimal reminders, rapid cycles
- **Best For**: Quick tasks and momentum building

## ADHD-Specific Features

### Gentle Reminders
- Non-jarring notification sounds
- Encouraging and supportive messaging
- User-customizable reminder tones (gentle, encouraging, minimal)
- Auto-dismissible reminders with configurable timeouts

### Hyperfocus Detection
- Automatic detection based on session duration and patterns
- Confidence scoring for intervention decisions
- Historical pattern analysis
- Gentle intervention options without forced breaks

### Break Suggestions
- **Hydration**: "Your brain has been working hard! How about some water?"
- **Movement**: "Consider taking a moment to move around and reset."
- **Eye Strain**: "Give your eyes a break with the 20-20-20 rule."

### Flexible Scheduling
- Respect for natural attention fluctuations
- Adaptive break duration calculations
- User preference integration
- Emergency pause functionality

## Usage Examples

### Starting a Focus Session

```python
# Create session
session_data = {
    "session_type": "pomodoro",
    "planned_duration": 25,
    "break_duration": 5,
    "focus_mode_settings": {
        "gentle_reminders": True,
        "reminder_threshold": 0.9,
        "hyperfocus_threshold": 120
    }
}

response = await client.post("/api/v1/focus/sessions", json=session_data)
session_id = response.json()["id"]

# Start the session
await client.post(f"/api/v1/focus/sessions/{session_id}/start")
```

### Handling Hyperfocus

```python
# Check for hyperfocus during long sessions
if session_duration > 120:  # 2 hours
    hyperfocus_data = detect_hyperfocus_pattern(
        session_duration=session_duration,
        planned_duration=planned_duration,
        user_history=user_session_history
    )
    
    if hyperfocus_data["is_hyperfocus"]:
        # Send gentle reminder via WebSocket
        await websocket_manager.send_hyperfocus_alert(
            user_id=user_id,
            session_id=session_id,
            session_duration=session_duration,
            confidence=hyperfocus_data["confidence"]
        )
```

### Custom Focus Mode

```python
custom_mode = {
    "name": "Writing Flow",
    "description": "Optimized for creative writing sessions",
    "settings": {
        "session_duration": 75,
        "break_duration": 15,
        "flexible_duration": True,
        "gentle_reminders": True,
        "reminder_threshold": 0.8,
        "hyperfocus_threshold": 180,
        "notification_sounds": "ambient",
        "break_suggestions": ["inspiration", "movement", "hydration"]
    }
}
```

## Testing

Run the focus session tests:

```bash
# Unit tests
pytest tests/unit/test_services/test_focus_service.py -v
pytest tests/unit/test_services/test_timer_service.py -v
pytest tests/unit/test_utils/test_focus_utils.py -v

# Integration tests
pytest tests/integration/test_focus_api.py -v

# All focus tests
pytest tests/ -k focus -v
```

## Configuration

### Environment Variables

```bash
# Redis for timer state persistence
CHRONOS_REDIS_HOST=localhost
CHRONOS_REDIS_PORT=6379
CHRONOS_REDIS_DB=0

# ADHD-specific defaults
CHRONOS_DEFAULT_FOCUS_DURATION=25
CHRONOS_DEFAULT_BREAK_DURATION=5
CHRONOS_HYPERFOCUS_WARNING_THRESHOLD=120
```

### Focus Mode Settings

```python
focus_settings = {
    "gentle_reminders": True,
    "reminder_threshold": 0.9,  # Show reminder at 90% completion
    "hyperfocus_threshold": 120,  # 2 hours
    "notification_sounds": "gentle",  # gentle, encouraging, minimal, ambient
    "break_suggestions": ["hydration", "movement", "eye_strain"],
    "auto_dismiss_seconds": 30,
    "dismissible_reminders": True
}
```

## Architecture

The focus session system consists of:

1. **FocusSessionService**: Business logic for session management
2. **TimerService**: Real-time timer functionality with Redis persistence
3. **FocusWebSocketManager**: WebSocket connections for live updates
4. **Focus Utilities**: ADHD-specific calculations and helpers
5. **Focus Models**: Database models with hyperfocus tracking
6. **Focus Schemas**: Pydantic models for API validation

This system provides a comprehensive, ADHD-friendly approach to focus management that respects neurodivergent attention patterns while providing gentle support for sustained productivity.
