/* Custom CSS for Project Chronos Documentation */

/* ADHD-friendly color scheme */
:root {
    --chronos-primary: #6366f1;
    --chronos-secondary: #8b5cf6;
    --chronos-accent: #06b6d4;
    --chronos-success: #10b981;
    --chronos-warning: #f59e0b;
    --chronos-error: #ef4444;
    --chronos-text: #1f2937;
    --chronos-text-light: #6b7280;
    --chronos-bg: #ffffff;
    --chronos-bg-alt: #f9fafb;
}

/* Improve readability for ADHD users */
body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--chronos-text);
}

/* Reduce cognitive load with better spacing */
.rst-content {
    max-width: 800px;
    margin: 0 auto;
}

/* Make code blocks more readable */
.highlight {
    background: var(--chronos-bg-alt);
    border-left: 4px solid var(--chronos-primary);
    padding: 1rem;
    margin: 1.5rem 0;
    border-radius: 0.5rem;
}

/* Improve table readability */
.rst-content table.docutils {
    border-collapse: collapse;
    margin: 1.5rem 0;
}

.rst-content table.docutils th {
    background: var(--chronos-primary);
    color: white;
    padding: 0.75rem;
    font-weight: 600;
}

.rst-content table.docutils td {
    padding: 0.75rem;
    border-bottom: 1px solid #e5e7eb;
}

.rst-content table.docutils tr:nth-child(even) {
    background: var(--chronos-bg-alt);
}

/* Status badges for better visual feedback */
.status-complete::before {
    content: "✅ ";
    color: var(--chronos-success);
}

.status-progress::before {
    content: "🔄 ";
    color: var(--chronos-warning);
}

.status-planned::before {
    content: "📋 ";
    color: var(--chronos-text-light);
}

/* Mermaid diagram styling */
.mermaid {
    text-align: center;
    margin: 2rem 0;
}

/* Admonition styling for ADHD-friendly alerts */
.admonition {
    border-left: 4px solid var(--chronos-accent);
    background: var(--chronos-bg-alt);
    padding: 1rem;
    margin: 1.5rem 0;
    border-radius: 0.5rem;
}

.admonition.note {
    border-left-color: var(--chronos-accent);
}

.admonition.warning {
    border-left-color: var(--chronos-warning);
}

.admonition.important {
    border-left-color: var(--chronos-error);
}

.admonition.tip {
    border-left-color: var(--chronos-success);
}

/* Navigation improvements */
.wy-nav-side {
    background: var(--chronos-bg);
    border-right: 1px solid #e5e7eb;
}

.wy-menu-vertical a {
    color: var(--chronos-text);
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    margin: 0.125rem 0;
}

.wy-menu-vertical a:hover {
    background: var(--chronos-bg-alt);
    color: var(--chronos-primary);
}

.wy-menu-vertical a.current {
    background: var(--chronos-primary);
    color: white;
}

/* Responsive improvements */
@media (max-width: 768px) {
    .rst-content {
        padding: 1rem;
    }
    
    .highlight {
        margin: 1rem -1rem;
        border-radius: 0;
        border-left: none;
        border-top: 4px solid var(--chronos-primary);
    }
}

/* Focus indicators for accessibility */
a:focus,
button:focus,
input:focus {
    outline: 2px solid var(--chronos-primary);
    outline-offset: 2px;
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
