# PRD: Phase 2 - Core User Flows
## Project Chronos Frontend Development - Weeks 5-8

### Document Information
- **Version**: 1.0
- **Date**: 2025-06-18
- **Author**: Project Chronos UX Team
- **Status**: Ready for Implementation
- **Priority**: Critical (P0)
- **Dependencies**: Phase 1 Foundation Infrastructure completion

---

## 1. Executive Summary

### Problem Statement
ADHD users need intuitive, cognitively-optimized user flows that reduce decision fatigue, support varying energy levels, and provide clear pathways to productivity. Traditional task management interfaces overwhelm neurodivergent users with too many choices and complex navigation patterns.

### Solution Overview
Implement four core user flows with ADHD-first design principles: Dashboard with energy-aware recommendations, Task Management with cognitive load optimization, Focus Sessions with distraction management, and User Onboarding with progressive disclosure. Each flow will adapt to user energy levels and cognitive capacity.

### Success Metrics
- **Task Initiation Rate**: 80% of users start tasks within 2 minutes of viewing
- **Flow Completion**: 90% completion rate for core user journeys
- **Cognitive Load**: User-reported overwhelm reduced by 60%
- **Energy Matching**: 85% accuracy in energy-appropriate task recommendations

---

## 2. Core User Flow Architecture

### 2.1 Dashboard Flow - Energy-Aware Task Recommendations

#### User Journey Map
```
Entry → Energy Check → Context Assessment → Task Recommendations → Quick Actions
  ↓         ↓              ↓                    ↓                   ↓
Landing   1-5 Scale    Time/Location/Mood   Filtered Tasks      One-Click Start
```

#### Component Specifications

##### Energy Check-In Component
```typescript
// components/dashboard/EnergyCheckIn.tsx
interface EnergyCheckInProps {
  currentEnergy: number;
  onEnergyChange: (energy: number) => void;
  showHistory?: boolean;
  quickMode?: boolean;
}

interface EnergyLevel {
  value: number;
  label: string;
  description: string;
  color: string;
  icon: React.ComponentType;
  taskTypes: string[];
  maxDuration: number; // minutes
}

const energyLevels: EnergyLevel[] = [
  {
    value: 1,
    label: "Very Low",
    description: "Minimal energy, need gentle tasks",
    color: "red",
    icon: BatteryLowIcon,
    taskTypes: ["routine", "maintenance", "quick"],
    maxDuration: 15
  },
  {
    value: 2,
    label: "Low",
    description: "Some energy, simple tasks work best",
    color: "orange",
    icon: BatteryQuarterIcon,
    taskTypes: ["routine", "administrative", "organizing"],
    maxDuration: 30
  },
  {
    value: 3,
    label: "Medium",
    description: "Balanced energy, regular tasks",
    color: "yellow",
    icon: BatteryHalfIcon,
    taskTypes: ["regular", "creative", "planning"],
    maxDuration: 60
  },
  {
    value: 4,
    label: "High",
    description: "Good energy, ready for challenges",
    color: "green",
    icon: BatteryThreeQuartersIcon,
    taskTypes: ["complex", "creative", "problem-solving"],
    maxDuration: 120
  },
  {
    value: 5,
    label: "Very High",
    description: "Peak energy, tackle anything",
    color: "blue",
    icon: BatteryFullIcon,
    taskTypes: ["complex", "challenging", "deep-work"],
    maxDuration: 240
  }
];

export const EnergyCheckIn: React.FC<EnergyCheckInProps> = ({
  currentEnergy,
  onEnergyChange,
  showHistory = false,
  quickMode = false
}) => {
  const [isExpanded, setIsExpanded] = useState(!quickMode);
  const { cognitiveLoad } = useCognitiveLoad();

  // Simplified view for high cognitive load
  if (cognitiveLoad === 'high' && !isExpanded) {
    return (
      <QuickEnergySelector
        currentEnergy={currentEnergy}
        onEnergyChange={onEnergyChange}
        onExpand={() => setIsExpanded(true)}
      />
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border p-6">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-semibold text-gray-900">
          How's your energy today?
        </h2>
        {cognitiveLoad === 'high' && (
          <button
            onClick={() => setIsExpanded(false)}
            className="text-sm text-blue-600 hover:underline"
          >
            Simplify
          </button>
        )}
      </div>

      <EnergyLevelGrid
        levels={energyLevels}
        currentEnergy={currentEnergy}
        onEnergyChange={onEnergyChange}
      />

      {showHistory && (
        <EnergyHistory className="mt-4" />
      )}

      <EnergyInsights
        currentEnergy={currentEnergy}
        className="mt-4"
      />
    </div>
  );
};
```

##### Task Recommendation Engine
```typescript
// components/dashboard/TaskRecommendations.tsx
interface TaskRecommendationProps {
  userEnergy: number;
  cognitiveLoad: 'low' | 'medium' | 'high';
  currentContext: UserContext;
  maxRecommendations?: number;
}

interface UserContext {
  timeOfDay: 'morning' | 'afternoon' | 'evening';
  location: 'home' | 'office' | 'other';
  availableTime: number; // minutes
  lastActivity: string;
  mood?: 'positive' | 'neutral' | 'negative';
}

interface TaskRecommendation {
  task: Task;
  matchScore: number;
  reasons: string[];
  estimatedSuccess: number;
  energyMatch: boolean;
  timeMatch: boolean;
  contextMatch: boolean;
}

export const TaskRecommendations: React.FC<TaskRecommendationProps> = ({
  userEnergy,
  cognitiveLoad,
  currentContext,
  maxRecommendations = 5
}) => {
  const { data: tasks } = useQuery(['tasks', 'pending']);
  const [recommendations, setRecommendations] = useState<TaskRecommendation[]>([]);

  const recommendationEngine = useMemo(() => {
    return new ADHDTaskRecommendationEngine({
      energyWeight: 0.4,
      timeWeight: 0.3,
      contextWeight: 0.2,
      moodWeight: 0.1
    });
  }, []);

  useEffect(() => {
    if (tasks) {
      const recs = recommendationEngine.generateRecommendations({
        tasks,
        userEnergy,
        cognitiveLoad,
        currentContext,
        maxResults: maxRecommendations
      });
      setRecommendations(recs);
    }
  }, [tasks, userEnergy, cognitiveLoad, currentContext, recommendationEngine, maxRecommendations]);

  if (cognitiveLoad === 'high') {
    return (
      <SimpleTaskRecommendations
        recommendations={recommendations.slice(0, 3)}
        onTaskSelect={handleTaskSelect}
      />
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-gray-900">
          Recommended for you
        </h3>
        <TaskJarButton
          availableTasks={recommendations}
          userEnergy={userEnergy}
        />
      </div>

      <div className="grid gap-3">
        {recommendations.map((rec, index) => (
          <TaskRecommendationCard
            key={rec.task.id}
            recommendation={rec}
            priority={index + 1}
            onSelect={() => handleTaskSelect(rec.task)}
            onQuickStart={() => handleQuickStart(rec.task)}
          />
        ))}
      </div>

      {recommendations.length === 0 && (
        <EmptyRecommendations
          userEnergy={userEnergy}
          onCreateTask={() => router.push('/tasks/new')}
        />
      )}
    </div>
  );
};

class ADHDTaskRecommendationEngine {
  constructor(private weights: RecommendationWeights) {}

  generateRecommendations(params: RecommendationParams): TaskRecommendation[] {
    const { tasks, userEnergy, cognitiveLoad, currentContext } = params;

    return tasks
      .map(task => this.scoreTask(task, userEnergy, cognitiveLoad, currentContext))
      .filter(rec => rec.matchScore > 0.3) // Minimum threshold
      .sort((a, b) => b.matchScore - a.matchScore)
      .slice(0, params.maxResults);
  }

  private scoreTask(
    task: Task,
    userEnergy: number,
    cognitiveLoad: string,
    context: UserContext
  ): TaskRecommendation {
    let score = 0;
    const reasons: string[] = [];

    // Energy matching
    const energyMatch = this.calculateEnergyMatch(task, userEnergy);
    score += energyMatch * this.weights.energyWeight;
    if (energyMatch > 0.7) reasons.push('Good energy match');

    // Time availability
    const timeMatch = this.calculateTimeMatch(task, context.availableTime);
    score += timeMatch * this.weights.timeWeight;
    if (timeMatch > 0.8) reasons.push('Fits available time');

    // Context appropriateness
    const contextMatch = this.calculateContextMatch(task, context);
    score += contextMatch * this.weights.contextWeight;
    if (contextMatch > 0.7) reasons.push('Good for current context');

    // Cognitive load consideration
    if (cognitiveLoad === 'high' && task.complexity > 5) {
      score *= 0.5; // Reduce score for complex tasks when cognitive load is high
      reasons.push('Simplified for current state');
    }

    // Success probability
    const estimatedSuccess = this.calculateSuccessProbability(task, userEnergy, context);

    return {
      task,
      matchScore: Math.min(score, 1),
      reasons,
      estimatedSuccess,
      energyMatch: energyMatch > 0.7,
      timeMatch: timeMatch > 0.8,
      contextMatch: contextMatch > 0.7
    };
  }

  private calculateEnergyMatch(task: Task, userEnergy: number): number {
    const taskEnergyRequirement = task.energyLevel || 3;
    const difference = Math.abs(userEnergy - taskEnergyRequirement);
    return Math.max(0, 1 - (difference / 4));
  }

  private calculateTimeMatch(task: Task, availableTime: number): number {
    const taskDuration = task.estimatedDuration || 30;
    if (taskDuration <= availableTime) {
      return 1;
    }
    return Math.max(0, availableTime / taskDuration);
  }

  private calculateContextMatch(task: Task, context: UserContext): number {
    let score = 0.5; // Base score

    // Location matching
    if (task.contextTags?.includes(context.location)) {
      score += 0.3;
    }

    // Time of day matching
    if (task.preferredTimeOfDay === context.timeOfDay) {
      score += 0.2;
    }

    return Math.min(score, 1);
  }

  private calculateSuccessProbability(task: Task, userEnergy: number, context: UserContext): number {
    // Complex algorithm considering user history, task complexity, energy, etc.
    let probability = 0.7; // Base probability

    // Energy factor
    const energyFactor = userEnergy / 5;
    probability *= (0.5 + energyFactor * 0.5);

    // Time pressure factor
    if (task.dueDate && isWithin24Hours(task.dueDate)) {
      probability *= 1.2; // Urgency can increase success for ADHD users
    }

    // Complexity factor
    const complexityPenalty = (task.complexity || 3) / 10;
    probability *= (1 - complexityPenalty);

    return Math.max(0.1, Math.min(0.95, probability));
  }
}
```

##### Quick Actions Component
```typescript
// components/dashboard/QuickActions.tsx
interface QuickActionsProps {
  userEnergy: number;
  cognitiveLoad: 'low' | 'medium' | 'high';
  recentTasks: Task[];
  onActionSelect: (action: QuickAction) => void;
}

interface QuickAction {
  id: string;
  label: string;
  description: string;
  icon: React.ComponentType;
  energyRequirement: number;
  estimatedTime: number;
  action: () => void;
  category: 'task' | 'focus' | 'break' | 'organize';
}

export const QuickActions: React.FC<QuickActionsProps> = ({
  userEnergy,
  cognitiveLoad,
  recentTasks,
  onActionSelect
}) => {
  const actions = useMemo(() => {
    const baseActions: QuickAction[] = [
      {
        id: 'task-jar',
        label: 'Task Jar',
        description: 'Pick a random task to avoid choice paralysis',
        icon: JarIcon,
        energyRequirement: 1,
        estimatedTime: 0,
        action: () => router.push('/tasks/jar'),
        category: 'task'
      },
      {
        id: 'quick-capture',
        label: 'Quick Capture',
        description: 'Quickly add a task or thought',
        icon: PlusIcon,
        energyRequirement: 2,
        estimatedTime: 2,
        action: () => openQuickCapture(),
        category: 'task'
      },
      {
        id: 'focus-session',
        label: 'Start Focus',
        description: 'Begin a focused work session',
        icon: FocusIcon,
        energyRequirement: 3,
        estimatedTime: 25,
        action: () => router.push('/focus/new'),
        category: 'focus'
      },
      {
        id: 'body-doubling',
        label: 'Find Body Double',
        description: 'Join or create a co-working session',
        icon: UsersIcon,
        energyRequirement: 3,
        estimatedTime: 0,
        action: () => router.push('/body-doubling'),
        category: 'focus'
      },
      {
        id: 'brain-dump',
        label: 'Brain Dump',
        description: 'Clear your mind of racing thoughts',
        icon: BrainIcon,
        energyRequirement: 2,
        estimatedTime: 10,
        action: () => openBrainDump(),
        category: 'organize'
      },
      {
        id: 'energy-break',
        label: 'Energy Break',
        description: 'Take a restorative break',
        icon: BatteryIcon,
        energyRequirement: 1,
        estimatedTime: 5,
        action: () => openEnergyBreak(),
        category: 'break'
      }
    ];

    // Filter actions based on energy and cognitive load
    return baseActions.filter(action => {
      if (cognitiveLoad === 'high' && action.energyRequirement > 2) {
        return false;
      }
      if (userEnergy < action.energyRequirement) {
        return false;
      }
      return true;
    });
  }, [userEnergy, cognitiveLoad]);

  // Add recent task actions
  const recentTaskActions = recentTasks.slice(0, 2).map(task => ({
    id: `recent-${task.id}`,
    label: `Continue: ${task.title}`,
    description: `Resume work on ${task.title}`,
    icon: PlayIcon,
    energyRequirement: task.energyLevel || 3,
    estimatedTime: task.estimatedDuration || 30,
    action: () => router.push(`/tasks/${task.id}`),
    category: 'task' as const
  }));

  const allActions = [...actions, ...recentTaskActions];

  if (cognitiveLoad === 'high') {
    return (
      <SimpleQuickActions
        actions={allActions.slice(0, 3)}
        onActionSelect={onActionSelect}
      />
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border p-6">
      <h3 className="text-lg font-medium text-gray-900 mb-4">
        Quick Actions
      </h3>

      <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
        {allActions.map(action => (
          <QuickActionCard
            key={action.id}
            action={action}
            onSelect={() => onActionSelect(action)}
            disabled={userEnergy < action.energyRequirement}
          />
        ))}
      </div>

      <div className="mt-4 pt-4 border-t border-gray-200">
        <CustomizeQuickActions
          currentActions={allActions}
          onCustomize={handleCustomizeActions}
        />
      </div>
    </div>
  );
};

const QuickActionCard: React.FC<{
  action: QuickAction;
  onSelect: () => void;
  disabled?: boolean;
}> = ({ action, onSelect, disabled = false }) => {
  const IconComponent = action.icon;

  return (
    <button
      onClick={onSelect}
      disabled={disabled}
      className={cn(
        'p-4 rounded-lg border text-left transition-all duration-200',
        'hover:shadow-md hover:border-blue-300',
        'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1',
        disabled
          ? 'opacity-50 cursor-not-allowed bg-gray-50'
          : 'bg-white hover:bg-blue-50'
      )}
    >
      <div className="flex items-start space-x-3">
        <div className={cn(
          'flex-shrink-0 w-8 h-8 rounded-lg flex items-center justify-center',
          getCategoryColor(action.category)
        )}>
          <IconComponent className="w-4 h-4" />
        </div>
        <div className="flex-1 min-w-0">
          <p className="text-sm font-medium text-gray-900 truncate">
            {action.label}
          </p>
          <p className="text-xs text-gray-500 mt-1">
            {action.description}
          </p>
          {action.estimatedTime > 0 && (
            <p className="text-xs text-blue-600 mt-1">
              ~{action.estimatedTime}min
            </p>
          )}
        </div>
      </div>
    </button>
  );
};

function getCategoryColor(category: QuickAction['category']): string {
  const colors = {
    task: 'bg-blue-100 text-blue-600',
    focus: 'bg-green-100 text-green-600',
    break: 'bg-yellow-100 text-yellow-600',
    organize: 'bg-purple-100 text-purple-600'
  };
  return colors[category];
}
```

### 2.2 Task Management Flow - Cognitive Load Optimization

#### Task List with ADHD Filtering
```typescript
// components/tasks/ADHDTaskList.tsx
interface ADHDTaskListProps {
  tasks: Task[];
  userEnergy: number;
  cognitiveLoad: 'low' | 'medium' | 'high';
  filters: TaskFilters;
  onFilterChange: (filters: TaskFilters) => void;
  onTaskSelect: (task: Task) => void;
}

interface TaskFilters {
  energyLevel: number[];
  estimatedTime: [number, number];
  complexity: [number, number];
  contextTags: string[];
  priority: Priority[];
  showOnlyMatching: boolean;
}

export const ADHDTaskList: React.FC<ADHDTaskListProps> = ({
  tasks,
  userEnergy,
  cognitiveLoad,
  filters,
  onFilterChange,
  onTaskSelect
}) => {
  const [viewMode, setViewMode] = useState<'list' | 'cards' | 'minimal'>('cards');
  const [sortBy, setSortBy] = useState<'energy' | 'time' | 'priority' | 'smart'>('smart');

  // Smart sorting based on ADHD principles
  const sortedTasks = useMemo(() => {
    return [...tasks].sort((a, b) => {
      switch (sortBy) {
        case 'energy':
          return Math.abs((a.energyLevel || 3) - userEnergy) -
                 Math.abs((b.energyLevel || 3) - userEnergy);
        case 'time':
          return (a.estimatedDuration || 30) - (b.estimatedDuration || 30);
        case 'priority':
          return getPriorityWeight(b.priority) - getPriorityWeight(a.priority);
        case 'smart':
        default:
          return calculateSmartScore(b, userEnergy, cognitiveLoad) -
                 calculateSmartScore(a, userEnergy, cognitiveLoad);
      }
    });
  }, [tasks, sortBy, userEnergy, cognitiveLoad]);

  // Filter tasks based on current state
  const filteredTasks = useMemo(() => {
    return sortedTasks.filter(task => {
      // Energy level filter
      if (filters.energyLevel.length > 0 &&
          !filters.energyLevel.includes(task.energyLevel || 3)) {
        return false;
      }

      // Time filter
      const duration = task.estimatedDuration || 30;
      if (duration < filters.estimatedTime[0] || duration > filters.estimatedTime[1]) {
        return false;
      }

      // Complexity filter
      const complexity = task.complexity || 3;
      if (complexity < filters.complexity[0] || complexity > filters.complexity[1]) {
        return false;
      }

      // Context tags filter
      if (filters.contextTags.length > 0 &&
          !filters.contextTags.some(tag => task.contextTags?.includes(tag))) {
        return false;
      }

      // Priority filter
      if (filters.priority.length > 0 && !filters.priority.includes(task.priority)) {
        return false;
      }

      // Show only matching filter (energy-appropriate tasks)
      if (filters.showOnlyMatching) {
        const energyDiff = Math.abs((task.energyLevel || 3) - userEnergy);
        if (energyDiff > 1) return false;
      }

      return true;
    });
  }, [sortedTasks, filters, userEnergy]);

  // Adaptive view based on cognitive load
  const adaptiveViewMode = useMemo(() => {
    if (cognitiveLoad === 'high') return 'minimal';
    if (cognitiveLoad === 'medium') return 'list';
    return viewMode;
  }, [cognitiveLoad, viewMode]);

  return (
    <div className="space-y-6">
      {/* Task List Header */}
      <TaskListHeader
        taskCount={filteredTasks.length}
        totalCount={tasks.length}
        userEnergy={userEnergy}
        cognitiveLoad={cognitiveLoad}
        viewMode={adaptiveViewMode}
        sortBy={sortBy}
        onViewModeChange={setViewMode}
        onSortChange={setSortBy}
      />

      {/* ADHD-Optimized Filters */}
      <ADHDTaskFilters
        filters={filters}
        onFilterChange={onFilterChange}
        userEnergy={userEnergy}
        cognitiveLoad={cognitiveLoad}
        availableTags={getAvailableContextTags(tasks)}
      />

      {/* Task List Content */}
      <div className="space-y-3">
        {filteredTasks.length === 0 ? (
          <EmptyTaskList
            hasFilters={hasActiveFilters(filters)}
            userEnergy={userEnergy}
            onClearFilters={() => onFilterChange(getDefaultFilters())}
            onCreateTask={() => router.push('/tasks/new')}
          />
        ) : (
          <>
            {/* Energy Match Indicator */}
            <EnergyMatchSummary
              tasks={filteredTasks}
              userEnergy={userEnergy}
            />

            {/* Task Items */}
            {adaptiveViewMode === 'minimal' && (
              <MinimalTaskList
                tasks={filteredTasks}
                onTaskSelect={onTaskSelect}
              />
            )}

            {adaptiveViewMode === 'list' && (
              <CompactTaskList
                tasks={filteredTasks}
                userEnergy={userEnergy}
                onTaskSelect={onTaskSelect}
              />
            )}

            {adaptiveViewMode === 'cards' && (
              <DetailedTaskCards
                tasks={filteredTasks}
                userEnergy={userEnergy}
                cognitiveLoad={cognitiveLoad}
                onTaskSelect={onTaskSelect}
              />
            )}
          </>
        )}
      </div>

      {/* Floating Action Button */}
      <FloatingTaskActions
        userEnergy={userEnergy}
        cognitiveLoad={cognitiveLoad}
        selectedTasks={[]} // For future multi-select
      />
    </div>
  );
};

function calculateSmartScore(task: Task, userEnergy: number, cognitiveLoad: string): number {
  let score = 0;

  // Energy matching (40% weight)
  const energyMatch = 1 - Math.abs((task.energyLevel || 3) - userEnergy) / 4;
  score += energyMatch * 0.4;

  // Urgency (30% weight)
  if (task.dueDate) {
    const daysUntilDue = getDaysUntilDue(task.dueDate);
    const urgencyScore = Math.max(0, 1 - daysUntilDue / 7);
    score += urgencyScore * 0.3;
  }

  // Complexity appropriateness (20% weight)
  const complexity = task.complexity || 3;
  let complexityScore = 1;
  if (cognitiveLoad === 'high' && complexity > 5) {
    complexityScore = 0.3;
  } else if (cognitiveLoad === 'medium' && complexity > 7) {
    complexityScore = 0.6;
  }
  score += complexityScore * 0.2;

  // Priority (10% weight)
  const priorityScore = getPriorityWeight(task.priority) / 4;
  score += priorityScore * 0.1;

  return score;
}
```

### 2.3 Focus Session Flow - Distraction Management

#### Focus Session Setup Component
```typescript
// components/focus/FocusSessionSetup.tsx
interface FocusSessionSetupProps {
  onSessionStart: (config: FocusSessionConfig) => void;
  userEnergy: number;
  cognitiveLoad: 'low' | 'medium' | 'high';
  selectedTask?: Task;
}

interface FocusSessionConfig {
  duration: number;
  breakDuration: number;
  sessionType: 'pomodoro' | 'flow' | 'micro' | 'custom';
  distractionLevel: 'minimal' | 'moderate' | 'full';
  backgroundSound: string | null;
  notifications: boolean;
  autoBreaks: boolean;
  hyperfocusProtection: boolean;
}

const sessionPresets = {
  micro: { duration: 15, break: 5, description: "Quick focused burst" },
  pomodoro: { duration: 25, break: 5, description: "Classic Pomodoro technique" },
  flow: { duration: 45, break: 10, description: "Extended focus session" },
  deep: { duration: 90, break: 15, description: "Deep work session" }
};

export const FocusSessionSetup: React.FC<FocusSessionSetupProps> = ({
  onSessionStart,
  userEnergy,
  cognitiveLoad,
  selectedTask
}) => {
  const [config, setConfig] = useState<FocusSessionConfig>({
    duration: 25,
    breakDuration: 5,
    sessionType: 'pomodoro',
    distractionLevel: 'moderate',
    backgroundSound: null,
    notifications: true,
    autoBreaks: true,
    hyperfocusProtection: true
  });

  // Auto-adjust based on energy and cognitive load
  useEffect(() => {
    let recommendedDuration = 25;
    let recommendedDistraction = 'moderate';

    if (cognitiveLoad === 'high') {
      recommendedDuration = 15;
      recommendedDistraction = 'minimal';
    } else if (userEnergy >= 4) {
      recommendedDuration = 45;
      recommendedDistraction = 'full';
    } else if (userEnergy <= 2) {
      recommendedDuration = 15;
      recommendedDistraction = 'minimal';
    }

    setConfig(prev => ({
      ...prev,
      duration: recommendedDuration,
      distractionLevel: recommendedDistraction as any
    }));
  }, [userEnergy, cognitiveLoad]);

  const handleQuickStart = () => {
    onSessionStart(config);
  };

  if (cognitiveLoad === 'high') {
    return (
      <QuickFocusSetup
        config={config}
        onStart={handleQuickStart}
        selectedTask={selectedTask}
      />
    );
  }

  return (
    <div className="max-w-2xl mx-auto space-y-6">
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">
          Set up your focus session
        </h2>

        {selectedTask && (
          <SelectedTaskCard task={selectedTask} className="mb-6" />
        )}

        {/* Session Type Selection */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-3">
            Session Type
          </label>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            {Object.entries(sessionPresets).map(([type, preset]) => (
              <SessionTypeCard
                key={type}
                type={type as any}
                preset={preset}
                isSelected={config.sessionType === type}
                onSelect={() => setConfig(prev => ({
                  ...prev,
                  sessionType: type as any,
                  duration: preset.duration,
                  breakDuration: preset.break
                }))}
                userEnergy={userEnergy}
              />
            ))}
          </div>
        </div>

        {/* Duration Customization */}
        <DurationSlider
          duration={config.duration}
          breakDuration={config.breakDuration}
          onDurationChange={(duration) => setConfig(prev => ({ ...prev, duration }))}
          onBreakChange={(breakDuration) => setConfig(prev => ({ ...prev, breakDuration }))}
          userEnergy={userEnergy}
        />

        {/* Distraction Management */}
        <DistractionLevelSelector
          level={config.distractionLevel}
          onLevelChange={(level) => setConfig(prev => ({ ...prev, distractionLevel: level }))}
          cognitiveLoad={cognitiveLoad}
        />

        {/* Advanced Options */}
        <AdvancedFocusOptions
          config={config}
          onConfigChange={setConfig}
          userEnergy={userEnergy}
        />

        {/* Start Button */}
        <div className="flex items-center justify-between pt-6 border-t">
          <FocusSessionPreview config={config} />
          <div className="flex space-x-3">
            <button
              onClick={() => router.back()}
              className="px-4 py-2 text-gray-600 hover:text-gray-800"
            >
              Cancel
            </button>
            <button
              onClick={handleQuickStart}
              className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              Start Focus Session
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
```

#### Active Focus Session Component
```typescript
// components/focus/ActiveFocusSession.tsx
interface ActiveFocusSessionProps {
  session: FocusSession;
  onPause: () => void;
  onStop: () => void;
  onBreak: () => void;
  onExtend: (minutes: number) => void;
}

export const ActiveFocusSession: React.FC<ActiveFocusSessionProps> = ({
  session,
  onPause,
  onStop,
  onBreak,
  onExtend
}) => {
  const [timeRemaining, setTimeRemaining] = useState(session.duration * 60);
  const [isRunning, setIsRunning] = useState(true);
  const [showHyperfocusWarning, setShowHyperfocusWarning] = useState(false);

  // Timer logic
  useEffect(() => {
    if (!isRunning) return;

    const interval = setInterval(() => {
      setTimeRemaining(prev => {
        if (prev <= 1) {
          handleSessionComplete();
          return 0;
        }

        // Hyperfocus protection
        if (session.config.hyperfocusProtection && prev === 30 * 60) { // 30 min warning
          setShowHyperfocusWarning(true);
        }

        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(interval);
  }, [isRunning, session]);

  const handleSessionComplete = () => {
    setIsRunning(false);
    // Trigger completion celebration
    showCompletionCelebration();

    if (session.config.autoBreaks) {
      onBreak();
    }
  };

  const progress = ((session.duration * 60 - timeRemaining) / (session.duration * 60)) * 100;
  const minutes = Math.floor(timeRemaining / 60);
  const seconds = timeRemaining % 60;

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="max-w-md w-full">
        {/* Minimal Focus Interface */}
        <div className="bg-white rounded-2xl shadow-lg p-8 text-center">
          {/* Progress Ring */}
          <div className="relative w-48 h-48 mx-auto mb-8">
            <CircularProgress
              progress={progress}
              size={192}
              strokeWidth={8}
              color="blue"
            />
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="text-center">
                <div className="text-4xl font-mono font-bold text-gray-900">
                  {String(minutes).padStart(2, '0')}:{String(seconds).padStart(2, '0')}
                </div>
                <div className="text-sm text-gray-500 mt-1">
                  {session.task?.title || 'Focus Session'}
                </div>
              </div>
            </div>
          </div>

          {/* Session Controls */}
          <div className="flex items-center justify-center space-x-4 mb-6">
            <button
              onClick={() => {
                setIsRunning(!isRunning);
                if (isRunning) onPause();
              }}
              className="w-12 h-12 rounded-full bg-blue-100 text-blue-600 hover:bg-blue-200 flex items-center justify-center"
            >
              {isRunning ? <PauseIcon className="w-6 h-6" /> : <PlayIcon className="w-6 h-6" />}
            </button>

            <button
              onClick={onStop}
              className="w-12 h-12 rounded-full bg-red-100 text-red-600 hover:bg-red-200 flex items-center justify-center"
            >
              <StopIcon className="w-6 h-6" />
            </button>
          </div>

          {/* Quick Actions */}
          <div className="flex items-center justify-center space-x-2 text-sm">
            <button
              onClick={() => onExtend(5)}
              className="px-3 py-1 text-blue-600 hover:bg-blue-50 rounded"
            >
              +5 min
            </button>
            <button
              onClick={() => onExtend(10)}
              className="px-3 py-1 text-blue-600 hover:bg-blue-50 rounded"
            >
              +10 min
            </button>
            <button
              onClick={onBreak}
              className="px-3 py-1 text-green-600 hover:bg-green-50 rounded"
            >
              Take Break
            </button>
          </div>
        </div>

        {/* Hyperfocus Warning */}
        {showHyperfocusWarning && (
          <HyperfocusWarning
            onDismiss={() => setShowHyperfocusWarning(false)}
            onTakeBreak={onBreak}
            onContinue={() => setShowHyperfocusWarning(false)}
          />
        )}

        {/* Ambient Controls */}
        {session.config.distractionLevel !== 'minimal' && (
          <AmbientControls
            backgroundSound={session.config.backgroundSound}
            onSoundChange={(sound) => updateSessionConfig({ backgroundSound: sound })}
          />
        )}
      </div>
    </div>
  );
};
```

### 2.4 User Onboarding Flow - Progressive Disclosure

#### ADHD-Optimized Onboarding
```typescript
// components/onboarding/ADHDOnboarding.tsx
interface OnboardingStep {
  id: string;
  title: string;
  description: string;
  component: React.ComponentType<OnboardingStepProps>;
  optional: boolean;
  estimatedTime: number;
  cognitiveLoad: number;
}

const onboardingSteps: OnboardingStep[] = [
  {
    id: 'welcome',
    title: 'Welcome to Chronos',
    description: 'Your ADHD-friendly productivity companion',
    component: WelcomeStep,
    optional: false,
    estimatedTime: 2,
    cognitiveLoad: 1
  },
  {
    id: 'adhd-assessment',
    title: 'Tell us about yourself',
    description: 'Help us personalize your experience',
    component: ADHDAssessmentStep,
    optional: true,
    estimatedTime: 5,
    cognitiveLoad: 3
  },
  {
    id: 'energy-patterns',
    title: 'Energy patterns',
    description: 'When do you feel most productive?',
    component: EnergyPatternsStep,
    optional: false,
    estimatedTime: 3,
    cognitiveLoad: 2
  },
  {
    id: 'first-task',
    title: 'Create your first task',
    description: 'Let\'s start with something simple',
    component: FirstTaskStep,
    optional: false,
    estimatedTime: 4,
    cognitiveLoad: 3
  },
  {
    id: 'ai-chunking-demo',
    title: 'AI task chunking',
    description: 'See how we break down overwhelming tasks',
    component: AIChunkingDemoStep,
    optional: true,
    estimatedTime: 3,
    cognitiveLoad: 2
  },
  {
    id: 'focus-session-intro',
    title: 'Focus sessions',
    description: 'Learn about distraction-free work time',
    component: FocusSessionIntroStep,
    optional: true,
    estimatedTime: 2,
    cognitiveLoad: 1
  },
  {
    id: 'completion',
    title: 'You\'re all set!',
    description: 'Welcome to your new productivity journey',
    component: CompletionStep,
    optional: false,
    estimatedTime: 1,
    cognitiveLoad: 1
  }
];

export const ADHDOnboarding: React.FC = () => {
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [completedSteps, setCompletedSteps] = useState<Set<string>>(new Set());
  const [userPreferences, setUserPreferences] = useState<Partial<UserPreferences>>({});
  const [cognitiveLoad, setCognitiveLoad] = useState<'low' | 'medium' | 'high'>('medium');

  const currentStep = onboardingSteps[currentStepIndex];
  const totalSteps = onboardingSteps.filter(step => !step.optional).length;
  const completedRequiredSteps = onboardingSteps
    .filter(step => !step.optional && completedSteps.has(step.id))
    .length;

  // Adaptive onboarding based on cognitive load
  const visibleSteps = useMemo(() => {
    if (cognitiveLoad === 'high') {
      return onboardingSteps.filter(step => !step.optional && step.cognitiveLoad <= 2);
    }
    return onboardingSteps;
  }, [cognitiveLoad]);

  const handleStepComplete = (stepId: string, data?: any) => {
    setCompletedSteps(prev => new Set([...prev, stepId]));

    if (data) {
      setUserPreferences(prev => ({ ...prev, ...data }));
    }

    // Auto-advance to next step
    const nextIndex = currentStepIndex + 1;
    if (nextIndex < visibleSteps.length) {
      setCurrentStepIndex(nextIndex);
    } else {
      handleOnboardingComplete();
    }
  };

  const handleSkipStep = () => {
    if (currentStep.optional) {
      const nextIndex = currentStepIndex + 1;
      if (nextIndex < visibleSteps.length) {
        setCurrentStepIndex(nextIndex);
      } else {
        handleOnboardingComplete();
      }
    }
  };

  const handleOnboardingComplete = async () => {
    // Save user preferences
    await saveUserPreferences(userPreferences);

    // Show completion celebration
    showOnboardingCelebration();

    // Navigate to dashboard
    router.push('/dashboard');
  };

  const StepComponent = currentStep.component;

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="max-w-2xl mx-auto px-4 py-8">
        {/* Progress Header */}
        <OnboardingProgress
          currentStep={completedRequiredSteps + 1}
          totalSteps={totalSteps}
          cognitiveLoad={cognitiveLoad}
          onCognitiveLoadChange={setCognitiveLoad}
        />

        {/* Step Content */}
        <div className="bg-white rounded-lg shadow-sm border p-8 mt-6">
          <StepComponent
            step={currentStep}
            onComplete={handleStepComplete}
            onSkip={handleSkipStep}
            userPreferences={userPreferences}
            cognitiveLoad={cognitiveLoad}
          />
        </div>

        {/* Navigation */}
        <OnboardingNavigation
          currentStepIndex={currentStepIndex}
          totalSteps={visibleSteps.length}
          canGoBack={currentStepIndex > 0}
          canSkip={currentStep.optional}
          onBack={() => setCurrentStepIndex(prev => Math.max(0, prev - 1))}
          onSkip={handleSkipStep}
          cognitiveLoad={cognitiveLoad}
        />

        {/* Overwhelm Escape Hatch */}
        <OverwhelmEscapeHatch
          onSimplify={() => setCognitiveLoad('high')}
          onExit={() => router.push('/dashboard')}
        />
      </div>
    </div>
  );
};
```

---

## 3. Implementation Timeline

### Week 5: Dashboard Foundation
- **Day 1-2**: Energy check-in component and state management
- **Day 3-4**: Task recommendation engine implementation
- **Day 5**: Quick actions component and integration

### Week 6: Task Management Core
- **Day 1-2**: ADHD task list with filtering and sorting
- **Day 3-4**: Task card components and interaction patterns
- **Day 5**: Task creation and editing flows

### Week 7: Focus Session Implementation
- **Day 1-2**: Focus session setup and configuration
- **Day 3-4**: Active session interface and timer logic
- **Day 5**: Break management and session completion

### Week 8: Onboarding & Integration
- **Day 1-2**: ADHD-optimized onboarding flow
- **Day 3-4**: User preference collection and personalization
- **Day 5**: Integration testing and flow optimization

---

## 4. Success Criteria

### User Experience Metrics
- ✅ 80% task initiation rate within 2 minutes
- ✅ 90% completion rate for core user journeys
- ✅ 60% reduction in user-reported overwhelm
- ✅ 85% accuracy in energy-appropriate recommendations

### Technical Performance
- ✅ Sub-2 second page load times
- ✅ Smooth animations at 60fps
- ✅ Responsive design across all devices
- ✅ Full keyboard accessibility

### ADHD-Specific Goals
- ✅ Cognitive load adaptation working correctly
- ✅ Energy level matching functional
- ✅ Progressive disclosure reducing overwhelm
- ✅ Focus session completion rates above 70%

This Phase 2 PRD establishes the core user experience foundation that makes Project Chronos truly ADHD-friendly and productive.
```

This is getting quite extensive. Let me continue with the remaining components and then create the other phase PRDs. Would you like me to continue with the complete Phase 2 PRD and then create Phase 3 and Phase 4 PRDs as well?