Agent 6: Real-time & WebSocket
===============================

.. currentmodule:: chronos.app

Agent 6 provides real-time communication infrastructure and virtual body doubling functionality for Project Chronos. This agent is specifically designed to address the social accountability needs of ADHD users through virtual co-working sessions.

Overview
--------

**Primary Responsibility**: WebSocket connections, real-time updates, body doubling sessions

**Dependencies**: 
- Agent 1 (Core Infrastructure)
- Agent 2 (Authentication) 
- Agent 5 (Focus Sessions)

**Key Features**:
- Virtual body doubling sessions
- Real-time progress sharing
- WebSocket infrastructure
- Synchronized focus sessions
- Live encouragement system

Mission Statement
-----------------

Enable virtual body doubling and real-time collaboration features that provide the social accountability and shared focus that many users with ADHD need to overcome task initiation barriers and maintain concentration.

Architecture Overview
--------------------

.. mermaid::

   graph TB
       subgraph "Client Layer"
           WC[Web Client]
           MC[Mobile Client]
       end
       
       subgraph "WebSocket Layer"
           WS[WebSocket Endpoints]
           WSM[WebSocket Manager]
           AUTH[WebSocket Auth]
       end
       
       subgraph "Service Layer"
           BDS[Body Doubling Service]
           RTS[Real-time Service]
           FS[Focus Service]
       end
       
       subgraph "Data Layer"
           REDIS[(Redis Cache)]
           PG[(PostgreSQL)]
       end
       
       subgraph "Models"
           BDM[Body Doubling Models]
           PM[Participant Models]
           MM[Message Models]
       end
       
       WC --> WS
       MC --> WS
       WS --> WSM
       WS --> AUTH
       WSM --> BDS
       WSM --> RTS
       BDS --> FS
       BDS --> BDM
       BDS --> PM
       RTS --> MM
       BDS --> PG
       RTS --> REDIS
       WSM --> REDIS

Technology Stack
----------------

- **WebSocket Framework**: FastAPI WebSocket support with connection management
- **Real-time State**: Redis for session state and message broadcasting  
- **Connection Pooling**: Custom WebSocket connection manager
- **Message Queue**: Redis Pub/Sub for real-time message distribution
- **Session Management**: Persistent session storage with automatic cleanup

Database Models
---------------

Agent 6 introduces three new database models:

BodyDoublingSession Model
~~~~~~~~~~~~~~~~~~~~~~~~~

.. autoclass:: models.body_doubling.BodyDoublingSession
   :members:
   :undoc-members:
   :show-inheritance:

**Key Fields**:
- ``host_user_id``: Session creator
- ``title``: Session description
- ``max_participants``: Capacity limit (2-10 users)
- ``session_type``: open, private, focus_group, study_group
- ``status``: waiting, active, paused, completed, cancelled
- ``session_settings``: JSON configuration

BodyDoublingParticipant Model
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. autoclass:: models.body_doubling.BodyDoublingParticipant
   :members:
   :undoc-members:
   :show-inheritance:

**Key Fields**:
- ``session_id``: Associated session
- ``user_id``: Participating user
- ``status``: active, away, focused, break, left
- ``share_progress``: Privacy setting for task sharing
- ``anonymous_mode``: Anonymous participation option

SessionMessage Model
~~~~~~~~~~~~~~~~~~~~

.. autoclass:: models.body_doubling.SessionMessage
   :members:
   :undoc-members:
   :show-inheritance:

**Key Fields**:
- ``message_type``: chat, encouragement, system, progress_update
- ``content``: Message text
- ``message_data``: Additional metadata (reactions, etc.)

Entity Relationship Diagram
---------------------------

.. mermaid::

   erDiagram
       User ||--o{ BodyDoublingSession : hosts
       User ||--o{ BodyDoublingParticipant : participates
       User ||--o{ SessionMessage : sends
       BodyDoublingSession ||--o{ BodyDoublingParticipant : contains
       BodyDoublingSession ||--o{ SessionMessage : has
       BodyDoublingSession ||--o| FocusSession : "current focus"
       BodyDoublingParticipant ||--o| Task : "current task"
       
       User {
           uuid id PK
           string email
           string full_name
           jsonb preferences
       }
       
       BodyDoublingSession {
           uuid id PK
           uuid host_user_id FK
           string title
           int max_participants
           string session_type
           string status
           datetime scheduled_start
           datetime actual_start
           jsonb session_settings
       }
       
       BodyDoublingParticipant {
           uuid id PK
           uuid session_id FK
           uuid user_id FK
           datetime joined_at
           string status
           boolean share_progress
           boolean anonymous_mode
           jsonb participant_settings
       }
       
       SessionMessage {
           uuid id PK
           uuid session_id FK
           uuid sender_id FK
           string message_type
           text content
           jsonb message_data
       }

WebSocket Infrastructure
-----------------------

Connection Management Flow
~~~~~~~~~~~~~~~~~~~~~~~~~

.. mermaid::

   sequenceDiagram
       participant Client
       participant WSEndpoint as WebSocket Endpoint
       participant WSManager as WebSocket Manager
       participant Auth as Authentication
       participant Redis
       participant Database
       
       Client->>WSEndpoint: Connect with token
       WSEndpoint->>Auth: Validate token
       Auth-->>WSEndpoint: User authenticated
       WSEndpoint->>WSManager: Register connection
       WSManager->>Redis: Store connection metadata
       WSManager->>Database: Update user status
       WSManager-->>Client: Connection established
       
       loop Message Handling
           Client->>WSEndpoint: Send message
           WSEndpoint->>WSManager: Process message
           WSManager->>Redis: Broadcast to session
           Redis->>WSManager: Deliver to participants
           WSManager-->>Client: Message delivered
       end
       
       Client->>WSEndpoint: Disconnect
       WSEndpoint->>WSManager: Cleanup connection
       WSManager->>Redis: Remove connection
       WSManager->>Database: Update user status

Body Doubling Session Flow
~~~~~~~~~~~~~~~~~~~~~~~~~

.. mermaid::

   sequenceDiagram
       participant Host as Session Host
       participant API as REST API
       participant Service as Body Doubling Service
       participant WS as WebSocket Manager
       participant Participant as Session Participant
       participant Redis

       Host->>API: Create session
       API->>Service: Create body doubling session
       Service->>Database: Store session
       Service-->>API: Session created
       API-->>Host: Session details

       Participant->>API: Join session
       API->>Service: Add participant
       Service->>Database: Store participant
       Service->>WS: Notify session
       WS->>Redis: Broadcast join event
       Redis->>WS: Deliver to all participants
       WS-->>Host: Participant joined
       WS-->>Participant: Welcome message

       Host->>WS: Start group focus
       WS->>Service: Create group focus session
       Service->>Database: Store focus session
       Service->>WS: Broadcast focus start
       WS->>Redis: Notify all participants
       Redis->>WS: Deliver focus start
       WS-->>Participant: Focus session started

       Participant->>WS: Share task progress
       WS->>Service: Process progress update
       Service->>WS: Broadcast progress
       WS->>Redis: Share with session
       Redis->>WS: Deliver to participants
       WS-->>Host: Progress update received

Real-time Message Types
~~~~~~~~~~~~~~~~~~~~~~

The WebSocket system supports various message types for different interactions:

**Connection Messages**:
- ``participant_joined``: New user joins session
- ``participant_left``: User leaves session
- ``participant_status_changed``: Status update (active, away, focused, break)

**Focus Session Messages**:
- ``focus_session_started``: Group focus session begins
- ``focus_session_ended``: Group focus session ends
- ``break_started``: Break period begins
- ``break_ended``: Break period ends

**Progress Sharing Messages**:
- ``task_progress``: Task completion updates
- ``milestone_reached``: Important progress milestones
- ``breakthrough``: Overcoming obstacles
- ``stuck``: Requesting help or encouragement

**Social Interaction Messages**:
- ``chat_message``: Text communication
- ``encouragement``: Reactions and support (👍, 🔥, ❤️, ⭐)
- ``celebration``: Achievement celebrations

**System Messages**:
- ``heartbeat``: Connection health check
- ``session_status_changed``: Session state updates
- ``error``: Error notifications

Pydantic Schemas
---------------

Agent 6 includes comprehensive Pydantic schemas for request/response validation:

Session Management Schemas
~~~~~~~~~~~~~~~~~~~~~~~~~

.. autoclass:: schemas.body_doubling.BodyDoublingSessionCreate
   :members:
   :undoc-members:

.. autoclass:: schemas.body_doubling.BodyDoublingSessionResponse
   :members:
   :undoc-members:

Participant Schemas
~~~~~~~~~~~~~~~~~~

.. autoclass:: schemas.body_doubling.BodyDoublingParticipantCreate
   :members:
   :undoc-members:

.. autoclass:: schemas.body_doubling.BodyDoublingParticipantResponse
   :members:
   :undoc-members:

Real-time Communication Schemas
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. autoclass:: schemas.body_doubling.WebSocketMessage
   :members:
   :undoc-members:

.. autoclass:: schemas.body_doubling.TaskProgressUpdate
   :members:
   :undoc-members:

.. autoclass:: schemas.body_doubling.EncouragementMessage
   :members:
   :undoc-members:

Implementation Status
--------------------

Phase 1: Foundation (✅ Complete)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Completed Components**:
- ✅ Database models for body doubling sessions
- ✅ Participant and message models
- ✅ Comprehensive Pydantic schemas
- ✅ Model relationships and constraints
- ✅ Schema validation and error handling

**Files Implemented**:
- ``chronos/app/models/body_doubling.py`` - Core database models
- ``chronos/app/schemas/body_doubling.py`` - Request/response schemas
- Updated ``chronos/app/models/__init__.py`` - Model exports

Phase 2: WebSocket Infrastructure (🔄 Next)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Planned Components**:
- WebSocket connection manager
- Redis integration for real-time state
- WebSocket authentication middleware
- Connection pooling and cleanup
- Message broadcasting utilities

**Files to Implement**:
- ``chronos/app/api/websockets/manager.py``
- ``chronos/app/middleware/websocket_auth.py``
- ``chronos/app/core/redis.py``
- ``chronos/app/utils/websocket_utils.py``

Phase 3: Business Logic (📋 Planned)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Planned Components**:
- Body doubling service implementation
- Real-time coordination service
- Session management logic
- Participant management
- Message handling and broadcasting

**Files to Implement**:
- ``chronos/app/services/body_doubling_service.py``
- ``chronos/app/services/realtime_service.py``
- ``chronos/app/utils/broadcast_utils.py``

Phase 4: API Endpoints (📋 Planned)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Planned Components**:
- REST API endpoints for session management
- WebSocket endpoints for real-time communication
- Integration with existing authentication
- API documentation and testing

**Files to Implement**:
- ``chronos/app/api/v1/body_doubling.py``
- ``chronos/app/api/websockets/body_doubling.py``
- ``chronos/app/api/websockets/__init__.py``

ADHD-Specific Features
---------------------

Virtual Body Doubling Benefits
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Task Initiation Support**:
- Social accountability reduces executive dysfunction
- Shared presence helps overcome starting barriers
- Gentle peer pressure for task engagement

**Focus Maintenance**:
- Synchronized work sessions maintain attention
- Break coordination prevents hyperfocus
- Progress sharing provides dopamine rewards

**Executive Function Support**:
- Structured session format reduces decision fatigue
- Clear start/stop signals help with transitions
- Visual progress indicators support working memory

Design Considerations
~~~~~~~~~~~~~~~~~~~~

**Cognitive Load Reduction**:
- Simple, intuitive interface design
- Minimal required interactions
- Clear visual feedback for all actions

**Sensory Considerations**:
- Optional audio/visual notifications
- Customizable notification preferences
- Distraction-free focus modes

**Emotional Support**:
- Encouragement system for motivation
- Anonymous participation options
- Positive reinforcement mechanisms

Quality Standards
----------------

Performance Requirements
~~~~~~~~~~~~~~~~~~~~~~~~

- WebSocket message delivery < 100ms
- Support for 1000+ concurrent connections
- Session state synchronization < 200ms
- Connection recovery within 5 seconds

Reliability Requirements
~~~~~~~~~~~~~~~~~~~~~~~

- 99.9% WebSocket uptime
- Automatic connection recovery
- Graceful handling of network interruptions
- Data consistency across all participants

Security Requirements
~~~~~~~~~~~~~~~~~~~~

- JWT-based WebSocket authentication
- Session-based access control
- Message content validation
- Rate limiting for abuse prevention

Testing Strategy
---------------

Unit Testing
~~~~~~~~~~~

- Model validation and relationships
- Schema validation and serialization
- Service logic and error handling
- WebSocket connection management

Integration Testing
~~~~~~~~~~~~~~~~~~

- End-to-end session workflows
- Real-time message delivery
- Multi-user session scenarios
- Connection failure recovery

Performance Testing
~~~~~~~~~~~~~~~~~~

- Concurrent connection limits
- Message throughput testing
- Memory usage under load
- Connection cleanup verification

Success Metrics
--------------

Feature Adoption
~~~~~~~~~~~~~~~

- 40% of users try body doubling within first month
- 25% of users regularly participate in sessions
- 60% of sessions result in task completion
- 30% of users create their own sessions

User Impact
~~~~~~~~~~

- 50% increase in task initiation rates during body doubling
- 40% improvement in focus session completion
- 70% user satisfaction with body doubling experience
- 35% reduction in procrastination incidents

Technical Metrics
~~~~~~~~~~~~~~~~

- < 100ms average message latency
- > 99.9% WebSocket connection uptime
- < 5 second connection recovery time
- Support for 1000+ concurrent users

Next Steps
----------

1. **Implement WebSocket Infrastructure**
   - Create connection manager with Redis backend
   - Add WebSocket authentication middleware
   - Implement message broadcasting system

2. **Develop Business Logic Services**
   - Body doubling session management
   - Real-time coordination and state management
   - Participant lifecycle management

3. **Create API Endpoints**
   - REST endpoints for session CRUD operations
   - WebSocket endpoints for real-time communication
   - Integration with existing authentication system

4. **Add Comprehensive Testing**
   - Unit tests for all components
   - Integration tests for real-time workflows
   - Performance and load testing

The foundation models and schemas are now complete and ready for the next implementation phase.
