# Project Chronos Architecture Diagrams

This document contains comprehensive Mermaid diagrams visualizing the ADHD-focused architecture of Project Chronos from multiple perspectives.

## System Overview

### 10-Agent Architecture Overview
```mermaid
graph TB
    subgraph "Foundation Layer"
        A1[Agent 1: Core Infrastructure & Database]
        A2[Agent 2: Authentication & Security]
    end
    
    subgraph "Core Features Layer"
        A3[Agent 3: Task Management & AI Chunking]
        A4[Agent 4: Time Blocking & Scheduling]
        A5[Agent 5: Focus Sessions & Pomodoro]
    end
    
    subgraph "Advanced Features Layer"
        A6[Agent 6: Real-time & WebSocket]
        A7[Agent 7: Notifications & Background Tasks]
        A8[Agent 8: Gamification & Motivation]
    end
    
    subgraph "Integration & Quality Layer"
        A9[Agent 9: API & Integration]
        A10[Agent 10: Testing & Quality Assurance]
    end
    
    A1 --> A2
    A1 --> A3
    A1 --> A4
    A1 --> A5
    A2 --> A3
    A2 --> A4
    A2 --> A5
    A3 --> A6
    A4 --> A6
    A5 --> A6
    A3 --> A7
    A4 --> A7
    A5 --> A7
    A3 --> A8
    A5 --> A8
    A6 --> A8
    A1 --> A9
    A2 --> A9
    A3 --> A9
    A4 --> A9
    A5 --> A9
    A6 --> A9
    A7 --> A9
    A8 --> A9
    A1 --> A10
    A2 --> A10
    A3 --> A10
    A4 --> A10
    A5 --> A10
    A6 --> A10
    A7 --> A10
    A8 --> A10
    A9 --> A10
    
    classDef foundation fill:#e1f5fe
    classDef core fill:#f3e5f5
    classDef advanced fill:#e8f5e8
    classDef integration fill:#fff3e0
    
    class A1,A2 foundation
    class A3,A4,A5 core
    class A6,A7,A8 advanced
    class A9,A10 integration
```

## Database Schema (Agent 1)

### Entity Relationship Diagram
```mermaid
erDiagram
    User {
        uuid id PK
        string email UK
        string hashed_password
        string full_name
        boolean is_active
        boolean is_verified
        boolean adhd_diagnosed
        string timezone
        jsonb preferences
        text bio
        timestamp created_at
        timestamp updated_at
    }
    
    Task {
        uuid id PK
        uuid user_id FK
        string title
        text description
        string status
        string priority
        string energy_level
        integer estimated_duration
        integer actual_duration
        string[] context_tags
        boolean is_chunked
        uuid parent_task_id FK
        timestamp due_date
        timestamp completed_at
        timestamp created_at
        timestamp updated_at
    }
    
    FocusSession {
        uuid id PK
        uuid user_id FK
        uuid task_id FK
        string session_type
        integer planned_duration
        integer actual_duration
        integer break_duration
        string status
        jsonb focus_mode_settings
        timestamp started_at
        timestamp completed_at
        timestamp paused_at
        boolean hyperfocus_detected
        integer hyperfocus_duration
        timestamp created_at
        timestamp updated_at
    }
    
    TimeBlock {
        uuid id PK
        uuid user_id FK
        uuid task_id FK
        string title
        string description
        timestamp start_time
        integer duration
        string block_type
        boolean is_flexible
        integer buffer_before
        integer buffer_after
        string color
        string status
        timestamp created_at
        timestamp updated_at
    }
    
    Notification {
        uuid id PK
        uuid user_id FK
        uuid task_id FK
        string type
        string title
        text message
        timestamp scheduled_for
        timestamp sent_at
        timestamp acknowledged_at
        boolean is_persistent
        string priority
        string[] delivery_channels
        integer retry_count
        integer max_retries
        boolean escalated
        timestamp created_at
        timestamp updated_at
    }
    
    UserGamification {
        uuid id PK
        uuid user_id FK
        integer total_points
        integer level
        integer points_to_next_level
        integer dopamine_menu_uses
        integer task_jar_uses
        integer hyperfocus_sessions
        timestamp created_at
        timestamp updated_at
    }
    
    Achievement {
        uuid id PK
        string name UK
        text description
        string category
        integer points_reward
        string badge_icon
        jsonb criteria
        boolean is_hidden
        timestamp created_at
        timestamp updated_at
    }
    
    UserAchievement {
        uuid id PK
        uuid user_id FK
        uuid achievement_id FK
        timestamp unlocked_at
        integer points_earned
        timestamp created_at
        timestamp updated_at
    }
    
    Streak {
        uuid id PK
        uuid user_id FK
        string streak_type
        integer current_streak
        integer longest_streak
        date last_activity_date
        integer freeze_count
        integer max_freezes_per_month
        timestamp created_at
        timestamp updated_at
    }
    
    PointsAward {
        uuid id PK
        uuid user_id FK
        uuid task_id FK
        integer points_awarded
        string reason
        integer multiplier
        integer total_points_after
        timestamp created_at
        timestamp updated_at
    }
    
    User ||--o{ Task : "owns"
    User ||--o{ FocusSession : "has"
    User ||--o{ TimeBlock : "schedules"
    User ||--o{ Notification : "receives"
    User ||--|| UserGamification : "has"
    User ||--o{ UserAchievement : "unlocks"
    User ||--o{ Streak : "maintains"
    User ||--o{ PointsAward : "earns"
    
    Task ||--o{ Task : "parent/child"
    Task ||--o{ FocusSession : "worked_on"
    Task ||--o{ TimeBlock : "scheduled_in"
    Task ||--o{ Notification : "triggers"
    Task ||--o{ PointsAward : "rewards"
    
    Achievement ||--o{ UserAchievement : "unlocked_as"
```

## ADHD User Journey Flow

### Task Management Flow with ADHD Features
```mermaid
flowchart TD
    Start([User wants to add task]) --> Overwhelmed{Feeling overwhelmed by task?}
    
    Overwhelmed -->|Yes| AIChunk[AI Task Chunking]
    Overwhelmed -->|No| CreateTask[Create Simple Task]
    
    AIChunk --> ChunkSize{Choose chunk size}
    ChunkSize --> Small[Small chunks<br/>5-15 min each]
    ChunkSize --> Medium[Medium chunks<br/>15-45 min each]
    ChunkSize --> Large[Large chunks<br/>45-90 min each]
    
    Small --> GenerateSubtasks[Generate 3-7 subtasks]
    Medium --> GenerateSubtasks
    Large --> GenerateSubtasks
    
    GenerateSubtasks --> ReviewChunks[Review AI suggestions]
    ReviewChunks --> AcceptChunks{Accept chunks?}
    AcceptChunks -->|Yes| SaveSubtasks[Save subtasks]
    AcceptChunks -->|No| ModifyChunks[Modify suggestions]
    ModifyChunks --> SaveSubtasks
    
    CreateTask --> SetEnergy[Set energy level]
    SaveSubtasks --> SetEnergy
    
    SetEnergy --> AddContext[Add context tags]
    AddContext --> ScheduleTask{Want to schedule?}
    
    ScheduleTask -->|Yes| TimeBlock[Create time block]
    ScheduleTask -->|No| TaskJar[Add to task jar]
    
    TimeBlock --> BufferTime[Add buffer time]
    BufferTime --> ConflictCheck{Schedule conflicts?}
    ConflictCheck -->|Yes| ResolveConflict[Suggest alternatives]
    ConflictCheck -->|No| Scheduled[Task scheduled]
    
    ResolveConflict --> TimeBlock
    TaskJar --> Ready[Task ready]
    Scheduled --> Ready
    
    Ready --> StartWork{Ready to work?}
    StartWork -->|Yes| FocusSession[Start focus session]
    StartWork -->|No| DopamineMenu[Use dopamine menu]
    
    DopamineMenu --> QuickActivity[Do 5-min activity]
    QuickActivity --> StartWork
    
    FocusSession --> WorkTimer[Work with timer]
    WorkTimer --> HyperfocusCheck{Working > 2 hours?}
    HyperfocusCheck -->|Yes| GentleReminder[Gentle break reminder]
    HyperfocusCheck -->|No| TaskComplete{Task done?}
    
    GentleReminder --> BreakChoice{Take break?}
    BreakChoice -->|Yes| Break[Take break]
    BreakChoice -->|No| Continue[Continue working]
    Break --> TaskComplete
    Continue --> TaskComplete
    
    TaskComplete -->|Yes| Celebrate[Celebrate completion]
    TaskComplete -->|No| SaveProgress[Save progress]
    
    Celebrate --> AwardPoints[Award points]
    AwardPoints --> CheckAchievements[Check achievements]
    CheckAchievements --> UpdateStreak[Update streaks]
    UpdateStreak --> Done([Complete])
    
    SaveProgress --> Done
    
    classDef adhd fill:#e8f5e8
    classDef ai fill:#e1f5fe
    classDef gamification fill:#fff3e0
    classDef decision fill:#f3e5f5
    
    class AIChunk,GenerateSubtasks,ReviewChunks ai
    class DopamineMenu,GentleReminder,BreakChoice,HyperfocusCheck adhd
    class AwardPoints,CheckAchievements,UpdateStreak,Celebrate gamification
    class Overwhelmed,AcceptChunks,ScheduleTask,ConflictCheck,StartWork,TaskComplete,BreakChoice decision
```

## Focus Session State Machine

### Focus Session Lifecycle
```mermaid
stateDiagram-v2
    [*] --> Planned : Create session
    
    Planned --> Active : Start session
    Planned --> Cancelled : Cancel
    
    Active --> Paused : Pause
    Active --> Break : Timer complete
    Active --> Hyperfocus : 2+ hours detected
    Active --> Completed : Manual complete
    
    Paused --> Active : Resume
    Paused --> Cancelled : Cancel
    
    Break --> Active : Resume work
    Break --> Completed : End session
    
    Hyperfocus --> GentleReminder : Show reminder
    GentleReminder --> Active : Continue working
    GentleReminder --> Break : Take break
    GentleReminder --> Completed : End session
    
    Completed --> [*]
    Cancelled --> [*]
    
    note right of Hyperfocus
        ADHD-specific state
        Gentle intervention
        User choice preserved
    end note
    
    note right of GentleReminder
        Non-disruptive
        Preserves flow state
        Offers options
    end note
```

## Notification System Flow

### Persistent Notification Lifecycle
```mermaid
sequenceDiagram
    participant U as User
    participant NS as Notification Service
    participant FS as Focus Service
    participant DB as Database
    participant BG as Background Worker
    
    U->>NS: Create reminder
    NS->>DB: Store notification
    NS->>BG: Schedule delivery
    
    Note over BG: Wait for scheduled time
    
    BG->>FS: Check focus mode
    alt User in focus mode
        FS-->>BG: Defer notification
        BG->>BG: Reschedule for later
    else User available
        BG->>U: Send notification
        BG->>DB: Mark as sent
        
        alt Persistent notification
            Note over BG: Wait 5 minutes
            BG->>DB: Check acknowledgment
            alt Not acknowledged
                BG->>U: Send gentle reminder
                Note over BG: Wait 15 minutes
                BG->>DB: Check acknowledgment again
                alt Still not acknowledged
                    BG->>U: Escalate notification
                    BG->>DB: Mark as escalated
                end
            end
        end
    end
    
    U->>NS: Acknowledge notification
    NS->>DB: Mark acknowledged
    NS->>BG: Cancel pending reminders
```

## Technology Stack Architecture

### System Components
```mermaid
graph TB
    subgraph "Frontend (Future)"
        Web[Web App]
        Mobile[Mobile App]
    end
    
    subgraph "API Layer"
        FastAPI[FastAPI Application]
        Auth[Authentication Middleware]
        CORS[CORS Middleware]
        RateLimit[Rate Limiting]
    end
    
    subgraph "Business Logic"
        UserService[User Service]
        TaskService[Task Service]
        FocusService[Focus Service]
        NotificationService[Notification Service]
        GamificationService[Gamification Service]
        AIService[AI Chunking Service]
    end
    
    subgraph "Data Layer"
        PostgreSQL[(PostgreSQL Database)]
        Redis[(Redis Cache)]
        Alembic[Alembic Migrations]
    end
    
    subgraph "External Services"
        OpenAI[OpenAI API]
        Anthropic[Anthropic Claude]
        GoogleCal[Google Calendar]
        Outlook[Outlook Calendar]
        SMTP[Email Service]
    end
    
    subgraph "Background Processing"
        Celery[Celery Workers]
        CeleryBeat[Celery Beat Scheduler]
        NotificationWorker[Notification Worker]
        AIWorker[AI Processing Worker]
    end
    
    subgraph "Real-time"
        WebSocket[WebSocket Manager]
        BodyDoubling[Body Doubling Sessions]
        LiveUpdates[Live Updates]
    end
    
    Web --> FastAPI
    Mobile --> FastAPI
    
    FastAPI --> Auth
    FastAPI --> CORS
    FastAPI --> RateLimit
    
    FastAPI --> UserService
    FastAPI --> TaskService
    FastAPI --> FocusService
    FastAPI --> NotificationService
    FastAPI --> GamificationService
    FastAPI --> WebSocket
    
    TaskService --> AIService
    AIService --> OpenAI
    AIService --> Anthropic
    
    UserService --> PostgreSQL
    TaskService --> PostgreSQL
    FocusService --> PostgreSQL
    NotificationService --> PostgreSQL
    GamificationService --> PostgreSQL
    
    UserService --> Redis
    TaskService --> Redis
    AIService --> Redis
    
    NotificationService --> Celery
    AIService --> Celery
    
    Celery --> NotificationWorker
    Celery --> AIWorker
    CeleryBeat --> Celery
    
    NotificationWorker --> SMTP
    
    FastAPI --> GoogleCal
    FastAPI --> Outlook
    
    Alembic --> PostgreSQL
    
    classDef frontend fill:#e1f5fe
    classDef api fill:#f3e5f5
    classDef business fill:#e8f5e8
    classDef data fill:#fff3e0
    classDef external fill:#fce4ec
    classDef background fill:#f1f8e9
    classDef realtime fill:#e0f2f1
    
    class Web,Mobile frontend
    class FastAPI,Auth,CORS,RateLimit api
    class UserService,TaskService,FocusService,NotificationService,GamificationService,AIService business
    class PostgreSQL,Redis,Alembic data
    class OpenAI,Anthropic,GoogleCal,Outlook,SMTP external
    class Celery,CeleryBeat,NotificationWorker,AIWorker background
    class WebSocket,BodyDoubling,LiveUpdates realtime
```

## Development Workflow

### Agent Development Process
```mermaid
gitgraph
    commit id: "Initial PRDs"
    branch agent1
    checkout agent1
    commit id: "Core Infrastructure"
    commit id: "Database Models"
    commit id: "Advanced Models"
    commit id: "Testing & Docs"
    checkout main
    merge agent1
    
    branch agent2
    checkout agent2
    commit id: "Auth System"
    commit id: "Security Middleware"
    commit id: "User Management"
    commit id: "Testing & Integration"
    checkout main
    merge agent2
    
    branch agent3
    checkout agent3
    commit id: "Task CRUD"
    commit id: "AI Chunking"
    commit id: "Adaptive Filtering"
    commit id: "Task Jar Feature"
    checkout main
    merge agent3
    
    branch integration
    checkout integration
    commit id: "Multi-agent Integration"
    commit id: "End-to-end Testing"
    commit id: "Performance Optimization"
    checkout main
    merge integration
    
    commit id: "Production Release"
```

## ADHD-Specific Feature Flows

### Time Blindness Solutions
```mermaid
graph LR
    subgraph "Time Perception Issues"
        TB1[Can't estimate duration]
        TB2[Loses track of time]
        TB3[Poor time awareness]
    end

    subgraph "Visual Time Solutions"
        VT1[Circular Clock View]
        VT2[Timeline Interface]
        VT3[Progress Indicators]
        VT4[Buffer Time Visualization]
    end

    subgraph "Scheduling Support"
        SS1[Automatic Buffer Time]
        SS2[Conflict Detection]
        SS3[Gentle Time Reminders]
        SS4[Flexible Scheduling]
    end

    TB1 --> VT1
    TB1 --> VT2
    TB2 --> VT3
    TB2 --> SS3
    TB3 --> VT4
    TB3 --> SS1

    VT1 --> SS2
    VT2 --> SS4

    classDef problem fill:#ffebee
    classDef solution fill:#e8f5e8
    classDef support fill:#e1f5fe

    class TB1,TB2,TB3 problem
    class VT1,VT2,VT3,VT4 solution
    class SS1,SS2,SS3,SS4 support
```

### Executive Dysfunction Support
```mermaid
graph LR
    subgraph "ADHD Challenges"
        TP[Task Paralysis]
        DF[Decision Fatigue]
        WM[Working Memory Issues]
        HF[Hyperfocus Management]
    end

    subgraph "Technical Solutions"
        AI[AI Task Chunking]
        TJ[Task Jar Selection]
        DM[Dopamine Menu]
        AF[Adaptive Filtering]
        SD[Smart Defaults]
        PN[Persistent Notifications]
        CP[Context Preservation]
        GI[Gentle Interruptions]
        BR[Break Reminders]
    end

    TP --> AI
    TP --> TJ
    TP --> DM
    DF --> AF
    DF --> SD
    WM --> PN
    WM --> CP
    HF --> GI
    HF --> BR

    classDef challenge fill:#ffebee
    classDef solution fill:#e8f5e8

    class TP,DF,WM,HF challenge
    class AI,TJ,DM,AF,SD,PN,CP,GI,BR solution
```

### Gamification Psychology for ADHD
```mermaid
graph TD
    subgraph "ADHD Challenges"
        DC[Dopamine Deficit]
        LM[Low Motivation]
        IR[Inconsistent Rewards]
        SP[Struggles with Persistence]
    end

    subgraph "Gamification Solutions"
        IM[Immediate Rewards]
        VP[Visual Progress]
        FS[Flexible Streaks]
        SM[Social Motivation]
    end

    subgraph "Implementation"
        Points[Points System]
        Badges[Achievement Badges]
        Levels[Level Progression]
        Streaks[Streak Tracking]
        BodyDouble[Body Doubling]
        Celebrations[Completion Celebrations]
    end

    DC --> IM
    LM --> VP
    IR --> FS
    SP --> SM

    IM --> Points
    IM --> Celebrations
    VP --> Badges
    VP --> Levels
    FS --> Streaks
    SM --> BodyDouble

    Points --> Dopamine[Dopamine Release]
    Badges --> Achievement[Sense of Achievement]
    Levels --> Progress[Progress Satisfaction]
    Streaks --> Consistency[Consistency Building]
    BodyDouble --> Accountability[Social Accountability]
    Celebrations --> Motivation[Increased Motivation]

    classDef challenge fill:#ffebee
    classDef solution fill:#e1f5fe
    classDef implementation fill:#e8f5e8
    classDef outcome fill:#fff3e0

    class DC,LM,IR,SP challenge
    class IM,VP,FS,SM solution
    class Points,Badges,Levels,Streaks,BodyDouble,Celebrations implementation
    class Dopamine,Achievement,Progress,Consistency,Accountability,Motivation outcome
```

## Data Flow Diagrams

### AI Task Chunking Process
```mermaid
sequenceDiagram
    participant U as User
    participant UI as Frontend
    participant API as Task API
    participant AI as AI Service
    participant Cache as Redis Cache
    participant DB as Database

    U->>UI: "This project is overwhelming"
    UI->>API: POST /tasks/chunk
    API->>Cache: Check cached chunks

    alt Cache miss
        API->>AI: Analyze task complexity
        AI->>AI: Generate subtasks
        AI->>API: Return chunked tasks
        API->>Cache: Store chunks (1hr TTL)
    else Cache hit
        Cache->>API: Return cached chunks
    end

    API->>UI: Chunked task suggestions
    UI->>U: Show AI suggestions
    U->>UI: Accept/modify chunks
    UI->>API: Save final chunks
    API->>DB: Store task hierarchy
    DB->>API: Confirm saved
    API->>UI: Success response
    UI->>U: "Tasks broken down! 🎉"
```

### Real-time Body Doubling Session
```mermaid
sequenceDiagram
    participant U1 as User 1
    participant U2 as User 2
    participant WS as WebSocket Manager
    participant Session as Session Service
    participant DB as Database

    U1->>WS: Create body doubling session
    WS->>Session: Initialize session
    Session->>DB: Store session
    WS->>U1: Session created

    U2->>WS: Join session
    WS->>Session: Add participant
    Session->>DB: Update participants
    WS->>U1: User 2 joined
    WS->>U2: Joined successfully

    U1->>WS: Start focus timer
    WS->>U2: Sync timer start

    loop Every minute
        WS->>U1: Timer update
        WS->>U2: Timer update
    end

    U1->>WS: Complete task
    WS->>U2: User 1 completed task
    WS->>U1: Celebration animation
    WS->>U2: Encouragement prompt

    U2->>WS: Send encouragement
    WS->>U1: Receive encouragement
```

These comprehensive diagrams provide visual documentation of the Project Chronos architecture from multiple perspectives:

1. **System Architecture**: Overall agent relationships and dependencies
2. **Database Schema**: Complete ERD with ADHD-specific fields
3. **User Journeys**: ADHD-focused task management flows
4. **State Machines**: Focus session lifecycle with hyperfocus handling
5. **Notification Flows**: Persistent reminder system
6. **Technology Stack**: Complete system components
7. **ADHD Features**: Specific solutions for neurodivergent challenges
8. **Data Flows**: Real-time processes and AI integration

These diagrams make the ADHD-focused design decisions clear and help developers understand how each component supports neurodivergent users. They also serve as excellent documentation for stakeholders and future development teams.
