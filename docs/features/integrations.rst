External Service Integrations
=============================

.. currentmodule:: app.integrations

Project Chronos provides comprehensive integration with external productivity services, designed with ADHD users at the center. Every integration includes specialized accommodations for neurodivergent patterns while maintaining seamless data synchronization.

.. note::
   All integrations are built with ADHD-first design principles, including energy-aware categorization, intelligent conflict resolution, and gentle error handling that supports rather than frustrates users.

Integration Architecture
-----------------------

ADHD-Centered Integration Design
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Our integration system is designed specifically for neurodivergent users:

.. mermaid::

   graph TB
       subgraph "ADHD Accommodations"
           Energy[Energy Level Detection]
           Complexity[Complexity Assessment]
           Duration[Duration Estimation]
           Buffer[Buffer Time Management]
       end
       
       subgraph "External Services"
           Google[Google Calendar]
           Todoist[Todoist Tasks]
           Slack[Slack Notifications]
           Notion[Notion Databases]
       end
       
       subgraph "Integration Framework"
           OAuth[OAuth 2.0 Management]
           Sync[Bidirectional Sync]
           Conflicts[Conflict Resolution]
           Webhooks[Real-time Updates]
       end
       
       subgraph "User Experience"
           Gentle[Gentle Error Handling]
           Progress[Progress Tracking]
           Recovery[Recovery Assistance]
           Feedback[Positive Feedback]
       end
       
       Energy --> Google
       Complexity --> Todoist
       Duration --> Notion
       Buffer --> Google
       
       Google --> OAuth
       Todoist --> Sync
       Slack --> Webhooks
       Notion --> Conflicts
       
       OAuth --> Gentle
       Sync --> Progress
       Conflicts --> Recovery
       Webhooks --> Feedback

**Core Design Principles**:
   - **Cognitive Load Reduction**: Smart defaults and automated categorization
   - **Executive Function Support**: Guided workflows and conflict resolution
   - **Attention Pattern Respect**: Batch processing and focus mode awareness
   - **Emotional Safety**: Non-judgmental error handling and positive reinforcement

Base Integration Framework
~~~~~~~~~~~~~~~~~~~~~~~~~

All integrations inherit from a common base class ensuring consistent behavior:

.. autoclass:: app.integrations.base.BaseIntegration
   :members:
   :show-inheritance:

**Key Features**:
   - **OAuth 2.0 Management**: Secure authentication with automatic token refresh
   - **Error Handling**: ADHD-friendly error messages with recovery guidance
   - **Rate Limiting**: Intelligent request throttling to prevent API abuse
   - **Conflict Detection**: Automatic identification and resolution of data conflicts
   - **Webhook Support**: Real-time synchronization for immediate updates

Supported Integrations
---------------------

Google Calendar Integration
~~~~~~~~~~~~~~~~~~~~~~~~~~

.. autoclass:: app.integrations.google_calendar.GoogleCalendarIntegration
   :members:
   :show-inheritance:

**ADHD-Optimized Features**:
   - **Energy-Aware Categorization**: Automatic energy level detection from event content
   - **Focus Time Recognition**: Identification of deep work and concentration periods
   - **Buffer Time Management**: Automatic transition time for ADHD time blindness
   - **Conflict-Free Scheduling**: Intelligent conflict detection and resolution

.. code-block:: python

   # Example: Import calendar events with ADHD accommodations
   async with GoogleCalendarIntegration() as integration:
       # Authenticate with OAuth 2.0
       tokens = await integration.authenticate({
           "code": "oauth_authorization_code",
           "redirect_uri": "https://app.chronos.com/oauth/callback"
       })
       
       # Import events with ADHD-specific processing
       result = await integration.import_calendar_events(
           access_token=tokens.access_token,
           start_date=datetime.now(),
           end_date=datetime.now() + timedelta(days=30)
       )
       
       # Events automatically include:
       # - Energy level categorization
       # - Focus time identification
       # - Buffer time recommendations
       # - ADHD-friendly descriptions

**Calendar Event Mapping**:
   - **Energy Levels**: Automatic detection based on event titles and descriptions
   - **Focus Sessions**: Recognition of deep work periods for hyperfocus protection
   - **Buffer Times**: 5-minute buffers added automatically for transitions
   - **All-Day Events**: Special handling for ADHD time perception challenges

Todoist Integration
~~~~~~~~~~~~~~~~~~

.. autoclass:: app.integrations.todoist.TodoistIntegration
   :members:
   :show-inheritance:

**ADHD Task Management Features**:
   - **Intelligent Categorization**: Energy and complexity assessment from task content
   - **Duration Estimation**: Smart time prediction from labels and historical data
   - **Project Organization**: Seamless project mapping with ADHD-friendly structure
   - **Label-Based Metadata**: Extraction of ADHD-relevant information from labels

.. code-block:: python

   # Example: Sync tasks with ADHD optimizations
   async with TodoistIntegration() as integration:
       # Import tasks with automatic ADHD categorization
       result = await integration.import_tasks(
           access_token=access_token,
           project_ids=["work_project", "personal_project"]
       )
       
       # Tasks automatically include:
       # - Energy level assessment
       # - Complexity categorization
       # - Duration estimation
       # - Chunk size recommendations

**Task Analysis Features**:
   - **Content Analysis**: Keyword detection for energy and complexity assessment
   - **Label Processing**: Extraction of time estimates and metadata from labels
   - **Priority Mapping**: Intelligent priority conversion between systems
   - **Completion Tracking**: Bidirectional completion status synchronization

Slack Integration
~~~~~~~~~~~~~~~~

.. autoclass:: app.integrations.slack.SlackIntegration
   :members:
   :show-inheritance:

**ADHD-Friendly Communication Features**:
   - **Rich Message Formatting**: Visual blocks with clear structure and emoji
   - **Body Doubling Support**: Specialized invitations for co-working sessions
   - **Interactive Notifications**: One-click actions for task completion and snoozing
   - **Context-Aware Messaging**: Notifications adapted to ADHD attention patterns

.. code-block:: python

   # Example: Send ADHD-optimized notification
   async with SlackIntegration() as integration:
       # Send body doubling invitation
       result = await integration.send_body_doubling_invitation(
           access_token=access_token,
           channel="#focus-sessions",
           session_title="Morning Deep Work Session",
           start_time=datetime.now() + timedelta(hours=1),
           duration_minutes=90,
           join_url="https://app.chronos.com/body-doubling/session-123"
       )
       
       # Notification includes:
       # - Visual appeal with emojis and formatting
       # - Clear session details and timing
       # - One-click join button
       # - ADHD-friendly encouragement

**Notification Types**:
   - **Task Reminders**: Gentle prompts with positive messaging
   - **Achievement Celebrations**: Dopamine-boosting success recognition
   - **Body Doubling Invitations**: Social accountability session invites
   - **Focus Break Suggestions**: Attention restoration reminders
   - **Energy Check-ins**: Self-awareness prompts for energy management

Notion Integration
~~~~~~~~~~~~~~~~~

.. autoclass:: app.integrations.notion.NotionIntegration
   :members:
   :show-inheritance:

**ADHD Database Management Features**:
   - **Flexible Property Mapping**: Accommodation of different database structures
   - **Rich Content Support**: Full rich text and multi-select property handling
   - **ADHD Property Recognition**: Automatic detection of energy and complexity fields
   - **Template Support**: Pre-configured ADHD-optimized database templates

.. code-block:: python

   # Example: Sync with Notion database
   async with NotionIntegration() as integration:
       # Export tasks to Notion with ADHD properties
       tasks = [
           TaskSync(
               title="Complete project proposal",
               description="Write and review Q1 proposal",
               energy_level="high",
               complexity="medium",
               estimated_duration=120,
               labels=["work", "deadline", "important"]
           )
       ]
       
       result = await integration.export_tasks(
           access_token=access_token,
           tasks=tasks,
           database_id="notion_database_id"
       )
       
       # Creates Notion pages with:
       # - ADHD-specific properties
       # - Rich formatting and structure
       # - Metadata preservation
       # - Bidirectional sync capability

**Database Features**:
   - **Property Detection**: Automatic recognition of ADHD-relevant properties
   - **Rich Text Processing**: Full support for formatted content and links
   - **Multi-Select Handling**: Tag and label synchronization
   - **Date Management**: Intelligent date parsing and formatting

Integration Management
---------------------

OAuth Authentication Flow
~~~~~~~~~~~~~~~~~~~~~~~~~

All integrations use OAuth 2.0 for secure authentication:

.. mermaid::

   sequenceDiagram
       participant User
       participant Chronos
       participant External
       participant Integration
       
       User->>Chronos: Request Integration
       Chronos->>External: Initiate OAuth Flow
       External-->>User: Authorization Page
       User->>External: Grant Permissions
       External->>Chronos: Authorization Code
       Chronos->>Integration: Exchange for Tokens
       Integration-->>Chronos: Access & Refresh Tokens
       Chronos->>Chronos: Store Encrypted Tokens
       Chronos-->>User: Integration Complete

**Security Features**:
   - **Encrypted Storage**: All tokens encrypted at rest
   - **Automatic Refresh**: Seamless token renewal before expiration
   - **Scope Management**: Minimal required permissions for each integration
   - **Revocation Support**: Easy disconnection and token revocation

Synchronization Operations
~~~~~~~~~~~~~~~~~~~~~~~~~

**Sync Types**:
   - **Import**: Bring data from external service to Chronos
   - **Export**: Send Chronos data to external service
   - **Bidirectional**: Two-way synchronization with conflict resolution
   - **Real-time**: Webhook-based immediate updates

**ADHD-Friendly Sync Features**:
   - **Progress Tracking**: Clear indication of sync status and completion
   - **Batch Processing**: Grouped operations to reduce cognitive load
   - **Error Recovery**: Gentle guidance for resolving sync issues
   - **Conflict Resolution**: User-friendly options for handling data conflicts

.. code-block:: python

   # Example: Start sync operation with progress tracking
   sync_request = SyncRequest(
       operation_type="import_calendar_events",
       force=False,
       dry_run=False,
       filters={
           "start_date": "2024-01-15",
           "end_date": "2024-02-15",
           "include_focus_time": True,
           "add_buffer_time": True
       }
   )
   
   # Start background sync with status tracking
   sync_response = await integration_service.start_sync(
       user_id=user.id,
       integration_id=integration.id,
       sync_request=sync_request
   )
   
   # Monitor progress with ADHD-friendly updates
   while sync_response.status != SyncStatus.COMPLETED:
       await asyncio.sleep(5)
       status = await integration_service.get_sync_status(sync_response.sync_id)
       # Provide encouraging progress updates

Health Monitoring
~~~~~~~~~~~~~~~~

**Integration Health Checks**:
   - **Token Status**: Automatic monitoring of OAuth token validity
   - **API Connectivity**: Regular connection testing to external services
   - **Sync Success Rate**: Tracking of successful vs failed operations
   - **Error Pattern Detection**: Identification of recurring issues

**ADHD-Friendly Monitoring**:
   - **Proactive Notifications**: Gentle alerts before issues become problems
   - **Clear Recommendations**: Specific steps for resolving health issues
   - **Success Celebration**: Recognition of successful sync operations
   - **Recovery Assistance**: Guided troubleshooting for common problems

.. code-block:: python

   # Example: Check integration health
   health = await integration_service.check_integration_health(
       user_id=user.id,
       integration_id=integration.id
   )
   
   if not health.is_healthy:
       # Provide ADHD-friendly recommendations
       for recommendation in health.recommendations:
           await send_gentle_notification(
               user_id=user.id,
               message=f"💡 Suggestion: {recommendation}",
               type="integration_health"
           )

API Integration Examples
-----------------------

Creating an Integration
~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   # Create Google Calendar integration with ADHD settings
   integration_data = IntegrationCreate(
       integration_type=IntegrationType.GOOGLE_CALENDAR,
       name="My Work Calendar",
       description="Primary calendar for work events and meetings",
       config={
           "calendar_ids": ["primary", "<EMAIL>"],
           "sync_frequency": "real_time",
           "buffer_time": 5,
           "energy_detection": True
       },
       sync_settings={
           "import_events": True,
           "export_events": True,
           "respect_focus_mode": True,
           "quiet_hours": {
               "start": "22:00",
               "end": "07:00"
           },
           "adhd_optimizations": {
               "add_buffer_time": True,
               "detect_focus_sessions": True,
               "energy_categorization": True
           }
       }
   )
   
   integration = await integration_service.create_integration(
       user_id=user.id,
       integration_data=integration_data
   )

Managing Sync Operations
~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   # Start comprehensive sync with ADHD accommodations
   sync_request = SyncRequest(
       operation_type="bidirectional_sync",
       force=False,
       dry_run=False,
       filters={
           "date_range": {
               "start": "2024-01-15",
               "end": "2024-03-15"
           },
           "adhd_features": {
               "energy_mapping": True,
               "buffer_time_addition": True,
               "focus_session_detection": True,
               "complexity_assessment": True
           }
       }
   )
   
   # Execute sync with progress tracking
   sync_response = await integration_service.start_sync(
       user_id=user.id,
       integration_id=integration.id,
       sync_request=sync_request
   )
   
   # Monitor with ADHD-friendly feedback
   print(f"✨ Sync started! Estimated completion: {sync_response.estimated_duration} seconds")
   print(f"🔄 Status: {sync_response.status}")
   print(f"📊 Sync ID: {sync_response.sync_id}")

Webhook Processing
~~~~~~~~~~~~~~~~~

.. code-block:: python

   # Handle real-time webhook events
   @app.post("/webhooks/google-calendar")
   async def handle_google_calendar_webhook(
       request: Request,
       background_tasks: BackgroundTasks
   ):
       # Process webhook with ADHD considerations
       webhook_data = await request.json()
       
       # Queue background processing
       background_tasks.add_task(
           process_calendar_webhook,
           webhook_data,
           adhd_optimizations=True
       )
       
       return {"status": "received", "message": "Processing calendar update..."}
   
   async def process_calendar_webhook(webhook_data: dict, adhd_optimizations: bool = True):
       # Process with ADHD-friendly features
       if adhd_optimizations:
           # Add buffer times
           # Detect energy levels
           # Check for focus session conflicts
           # Apply gentle notifications
           pass

This comprehensive integration system ensures that ADHD users can seamlessly connect their existing productivity tools while benefiting from specialized neurodivergent accommodations and support.
