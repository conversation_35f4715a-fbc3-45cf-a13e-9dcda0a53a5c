Notifications & Background Tasks Features
==========================================

.. currentmodule:: app.services.notification_service

Project Chronos provides ADHD-optimized notification and reminder systems designed to support memory challenges while respecting attention patterns and preventing overwhelm through intelligent, context-aware delivery.

Core Functionality
------------------

ADHD-Optimized Notification System
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Persistent Reminders with Gentle Escalation**
   Notification system designed to work with ADHD memory challenges:

   - **Acknowledgment-Required Reminders**: Important notifications persist until acknowledged
   - **Gentle Escalation**: Gradual increase in reminder frequency without overwhelming
   - **Smart Snoozing**: Flexible snooze options that accommodate attention fluctuations
   - **Context-Aware Timing**: Notifications respect focus sessions and quiet hours
   - **Multi-Channel Delivery**: Push, email, and SMS options based on urgency and preference

   .. code-block:: python

      # Create persistent reminder with ADHD accommodations
      notification_data = NotificationCreate(
          type=NotificationType.TASK_REMINDER,
          title="Important Project Deadline",
          message="Your project proposal is due in 2 hours. You've got this!",
          scheduled_for=deadline_time - timedelta(hours=2),
          priority=NotificationPriority.HIGH,
          delivery_channels=["push", "email"],
          is_persistent=True,  # Requires acknowledgment
          respect_focus_mode=True,  # Defer during focus sessions
          max_snooze_count=3,  # Allow flexible snoozing
          reminder_stages=[120, 60, 30, 15]  # Staggered reminders
      )

**Context-Aware Delivery**
   Intelligent notification timing that works with ADHD patterns:

   - **Focus Mode Respect**: Non-urgent notifications deferred during focus sessions
   - **Quiet Hours Support**: Automatic deferral during sleep and rest periods
   - **Energy-Aware Timing**: Notifications scheduled for optimal energy periods
   - **Batch Processing**: Non-urgent notifications grouped for designated times
   - **Attention-Friendly Spacing**: Prevents notification overwhelm through intelligent spacing

**Staggered Reminder Sequences**
   Multi-stage reminders that accommodate ADHD time blindness:

   - **Deadline Awareness**: Multiple reminders leading up to important deadlines
   - **Flexible Timing**: Customizable reminder intervals based on task importance
   - **Progressive Urgency**: Gentle increase in reminder intensity as deadlines approach
   - **Completion Tracking**: Automatic cancellation when tasks are completed
   - **Pattern Learning**: System learns optimal reminder timing for each user

Background Task Processing
~~~~~~~~~~~~~~~~~~~~~~~~~

**Celery-Powered Reliability**
   Robust background processing for consistent notification delivery:

   - **Queue-Based Processing**: Reliable delivery through Redis-backed queues
   - **Automatic Retry Logic**: Exponential backoff for failed deliveries
   - **Health Monitoring**: System monitoring and error recovery
   - **Scalable Architecture**: Handles high notification volumes efficiently
   - **ADHD-Friendly Error Handling**: Gentle error management without user overwhelm

**Periodic Task Management**
   Automated background processes for ADHD support:

   - **Daily Reminder Processing**: Hourly checks for due notifications
   - **Staggered Reminder Delivery**: 5-minute intervals for reminder sequences
   - **Batch Processing**: Designated times for non-urgent notification batches
   - **Cleanup Operations**: Automatic cleanup of expired and old notifications
   - **Health Checks**: Regular system health monitoring and maintenance

Notification Types & Personalization
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**ADHD-Specific Notification Types**
   Specialized notifications designed for ADHD needs:

   - **Task Reminders**: Deadline-aware reminders with positive messaging
   - **Focus Break Suggestions**: Gentle prompts for attention restoration
   - **Body Doubling Invitations**: Social accountability notifications
   - **Achievement Celebrations**: Dopamine-boosting success recognition
   - **Energy Check-ins**: Self-awareness prompts for energy management
   - **Medication Reminders**: Health-focused reminders with privacy protection
   - **Daily Review Prompts**: Reflection and planning support

**Personalized Delivery Preferences**
   Customizable notification behavior for individual ADHD patterns:

   - **Channel Preferences**: User-defined delivery method preferences
   - **Timing Customization**: Personal quiet hours and optimal notification times
   - **Escalation Settings**: Customizable escalation behavior and limits
   - **Batch Preferences**: Control over notification batching and timing
   - **Type-Specific Settings**: Different preferences for different notification types

User Experience Optimizations
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**ADHD-Friendly Messaging**
   Notification content designed to support rather than stress:

   - **Positive Tone**: Encouraging and supportive language throughout
   - **Clear Actions**: Specific, actionable next steps in every notification
   - **Non-Judgmental Language**: No blame or criticism for missed deadlines
   - **Dopamine-Aware Timing**: Achievement notifications delivered immediately
   - **Context Preservation**: Notifications include relevant context and links

**Flexible Interaction Options**
   User controls that accommodate ADHD attention patterns:

   - **Smart Snoozing**: Multiple snooze duration options (5min, 15min, 1hr, 4hr)
   - **Quick Actions**: One-tap acknowledgment and task completion
   - **Batch Management**: Ability to acknowledge multiple notifications at once
   - **Preference Shortcuts**: Quick access to notification settings
   - **Emergency Override**: Urgent notifications bypass all filters
