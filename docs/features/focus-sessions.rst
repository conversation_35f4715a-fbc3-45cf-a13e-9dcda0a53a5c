Focus Sessions & Pomodoro Features
===================================

.. currentmodule:: app.services.focus_service

Project Chronos provides ADHD-optimized focus session management with flexible timing, hyperfocus protection, and flow state optimization designed specifically for neurodivergent attention patterns.

Core Functionality
------------------

ADHD-Optimized Focus Sessions
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Flexible Session Management**
   Focus sessions designed to work with ADHD attention patterns rather than against them:

   - **Flexible Timing**: Sessions can be paused, resumed, and extended based on attention fluctuations
   - **Multiple Session Types**: Different modes for various energy levels and task types
   - **Flow State Protection**: Preserves hyperfocus while preventing burnout
   - **Gentle Transitions**: Non-disruptive break suggestions and session endings
   - **Progress Preservation**: Maintains context and progress across interruptions

   .. code-block:: python

      # Create a flexible focus session
      session_data = FocusSessionCreate(
          session_type=FocusSessionType.DEEP_WORK,
          title="Important Project Work",
          planned_duration=90,  # 90 minutes for deep work
          break_duration=15,    # Longer breaks for deep work
          task_id=task.id,      # Link to specific task
          focus_mode_id=focus_mode.id  # Custom environment
      )

**Session Types for Different Needs**
   Tailored session types that accommodate ADHD attention patterns:

   - **Pomodoro (25 min)**: Traditional focused work with flexibility
   - **Deep Work (45-120 min)**: Extended sessions for complex tasks
   - **Sprint (15-30 min)**: Quick bursts for simple tasks or low energy
   - **Custom**: User-defined timing for specific needs
   - **Hyperfocus**: Protected long sessions with monitoring

**Real-Time Session Management**
   Dynamic session control that adapts to ADHD needs:

   .. code-block:: python

      # Start session with focus mode activation
      await focus_service.start_focus_session(
          session_id=session.id,
          user_id=user.id,
          start_data=FocusSessionStart(
              focus_mode_id=deep_work_mode.id,
              enable_notifications=True
          )
      )

      # Pause when attention wavers (no judgment)
      await focus_service.pause_focus_session(
          session_id=session.id,
          user_id=user.id,
          reason="Need to handle interruption"
      )

      # Resume with optional extension
      await focus_service.resume_focus_session(
          session_id=session.id,
          user_id=user.id,
          extend_duration=15  # Add 15 more minutes
      )

Hyperfocus Protection System
~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Intelligent Hyperfocus Detection**
   ADHD-specific monitoring that protects against hyperfocus burnout:

   - **Duration Monitoring**: Tracks session length against healthy thresholds
   - **Gentle Alerts**: Non-disruptive notifications about extended focus
   - **Customizable Thresholds**: User-defined limits based on personal patterns
   - **Context Awareness**: Different thresholds for different session types
   - **Flow State Preservation**: Protects productive hyperfocus while preventing harm

   .. code-block:: python

      # Check for hyperfocus and get protection alerts
      alert = await focus_service.detect_hyperfocus(session_id)

      if alert:
          # HyperfocusAlert provides gentle guidance
          print(f"Alert: {alert.message}")
          print(f"Suggested actions: {alert.suggested_actions}")
          # ["Take a 10-15 minute break", "Hydrate and stretch",
          #  "Check in with your body", "Consider ending the session"]

**Break Reminder System**
   ADHD-friendly break suggestions that work with attention patterns:

   - **Gentle Suggestions**: Non-intrusive break recommendations
   - **Activity-Based Breaks**: Specific suggestions based on session type and duration
   - **Energy Awareness**: Break activities matched to current energy level
   - **Flexible Timing**: Optional break acceptance without pressure
   - **Return Support**: Gentle prompts to return to focus when ready

   .. code-block:: python

      # Get personalized break suggestions
      reminder = await focus_service.suggest_break(session_id)

      if reminder:
          print(f"Break suggestion: {reminder.message}")
          print(f"Suggested activities: {reminder.break_activities}")
          # ["Take a short walk", "Do some stretches", "Hydrate",
          #  "Look out the window", "Practice deep breathing"]

Focus Modes & Environment Control
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Customizable Focus Environments**
   Personalized focus modes that create optimal work environments:

   - **Notification Management**: Block distracting notifications during focus
   - **Emergency Contacts**: Allow important contacts through focus mode
   - **Environment Settings**: Customize lighting, sound, and other factors
   - **Default Configurations**: Pre-configured modes for different work types
   - **Usage Tracking**: Learn which modes work best for different situations

   .. code-block:: python

      # Create a custom focus mode
      focus_mode = FocusModeCreate(
          name="Deep Coding",
          description="For complex programming tasks",
          block_notifications=True,
          allowed_contacts=["<EMAIL>"],
          default_duration=90,
          break_duration=15,
          enable_hyperfocus_protection=True,
          hyperfocus_threshold=120,  # 2 hours
          settings={
              "ambient_sound": "brown_noise",
              "lighting": "warm_dim",
              "phone_mode": "do_not_disturb"
          }
      )

**Adaptive Focus Recommendations**
   AI-powered suggestions based on ADHD patterns and preferences:

   - **Energy Level Matching**: Session types matched to current energy
   - **Time-of-Day Optimization**: Recommendations based on personal productivity patterns
   - **Task Complexity Analysis**: Session duration suggestions based on task difficulty
   - **Historical Performance**: Learning from past session success patterns
   - **Context Awareness**: Recommendations based on current environment and schedule

Break Management & Activities
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**ADHD-Friendly Break Activities**
   Curated break suggestions that work with ADHD needs:

   - **Movement Breaks**: Physical activities to reset attention and energy
   - **Sensory Breaks**: Activities that address sensory processing needs
   - **Social Breaks**: Optional interaction for social energy restoration
   - **Mindfulness Breaks**: Brief meditation or breathing exercises
   - **Creative Breaks**: Quick creative activities for mental refreshment

**Intelligent Break Timing**
   Flexible break scheduling that accommodates ADHD attention patterns:

   - **Natural Break Points**: Suggestions aligned with attention cycles
   - **Task Completion Breaks**: Breaks timed with task milestones
   - **Energy-Based Timing**: Break suggestions based on energy depletion
   - **Flow State Respect**: Delayed break suggestions during productive flow
   - **Urgency Awareness**: Modified break timing for deadline-driven work

Session Analytics & Insights
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**ADHD-Focused Analytics**
   Insights that help understand and optimize attention patterns:

   - **Attention Pattern Recognition**: Identify personal focus rhythms
   - **Productivity Time Analysis**: Discover optimal focus times
   - **Session Type Effectiveness**: Learn which session types work best
   - **Break Pattern Optimization**: Understand break timing and effectiveness
   - **Hyperfocus Trend Monitoring**: Track hyperfocus patterns for health

   .. code-block:: python

      # Get comprehensive focus session statistics
      stats = await focus_service.get_focus_session_stats(user_id)

      # FocusSessionStats includes:
      # - total_sessions: Total number of focus sessions
      # - total_focus_time: Cumulative focused time in minutes
      # - average_session_duration: Average session length
      # - completion_rate: Percentage of sessions completed
      # - favorite_session_types: Most used session types
      # - hyperfocus_frequency: How often hyperfocus occurs
      # - productivity_trends: Time-based productivity patterns

**Progress Tracking Without Pressure**
   Supportive analytics that encourage without overwhelming:

   - **Celebration of Effort**: Recognition for attempting focus, not just completion
   - **Pattern Recognition**: Helpful insights without judgment
   - **Flexible Goals**: Adaptive targets based on personal patterns
   - **Context Awareness**: Understanding of external factors affecting focus
   - **Gentle Feedback**: Constructive insights delivered supportively
   - Social motivation

This documentation will be expanded as focus session features are developed.
