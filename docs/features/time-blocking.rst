Time Blocking & Scheduling Features
===================================

.. currentmodule:: app.services.time_service

Project Chronos provides ADHD-optimized time blocking and scheduling features designed to combat time blindness through visual, tangible time management that makes abstract time concrete and manageable.

Core Functionality
------------------

Visual Time Interface
~~~~~~~~~~~~~~~~~~~~

**Circular Clock View**
   24-hour circular representation that makes time proportions visually intuitive:
   
   - **Proportional Segments**: Time blocks displayed as proportional segments of a 24-hour circle
   - **Color Coding**: Visual differentiation by task type, energy level, or priority
   - **Current Time Indicator**: Real-time position marker showing current time
   - **Hour Markers**: Clear visual reference points for time orientation
   - **Intuitive Proportions**: Makes it easy to see how much time activities actually take

   .. code-block:: python
   
      # Generate circular clock view for ADHD time visualization
      viz_service = TimeVisualizationService()
      circular_view = viz_service.generate_circular_view(
          time_blocks=daily_blocks,
          date=target_date,
          center_x=200,
          center_y=200,
          radius=150
      )
      
      # CircularCalendarView includes:
      # - segments: Proportional time block segments
      # - hour_markers: 24-hour reference points
      # - current_time_indicator: Real-time position

**Timeline View**
   Linear timeline with drag-and-drop positioning for interactive scheduling:
   
   - **Hourly Grid**: Clear time reference with major and minor hour lines
   - **Positioned Blocks**: Time blocks with precise pixel positioning for drag-and-drop
   - **Buffer Visualization**: Visual representation of transition times
   - **Conflict Highlighting**: Clear indication of scheduling overlaps
   - **Responsive Scaling**: Adjustable hour height for different screen sizes

   .. code-block:: python
   
      # Generate timeline view with positioning data
      timeline_view = viz_service.generate_timeline_view(
          time_blocks=daily_blocks,
          date=target_date,
          hour_height=60,  # Pixels per hour
          start_hour=6,    # Timeline start
          end_hour=22      # Timeline end
      )
      
      # TimelineView includes:
      # - positioned_blocks: Blocks with x, y, width, height
      # - hour_lines: Grid lines for time reference
      # - current_time_line: Real-time position indicator

ADHD-Optimized Time Blocking
~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Intelligent Buffer Time Management**
   Automatic buffer insertion to prevent the stress of rushing between activities:
   
   - **Automatic Buffer Insertion**: Smart addition of transition time based on activity type
   - **Customizable Defaults**: User-defined buffer preferences for different situations
   - **Context-Aware Buffers**: Longer buffers for medical appointments, shorter for routine tasks
   - **Travel Time Integration**: Automatic calculation and insertion of travel time
   - **Buffer Optimization**: Learning from user patterns to suggest optimal buffer times

   .. code-block:: python
   
      # Create time block with automatic buffer management
      time_block_data = TimeBlockCreate(
          title="Important Meeting",
          block_type=TimeBlockType.MEETING,
          start_time=datetime(2024, 1, 15, 14, 0),
          end_time=datetime(2024, 1, 15, 15, 0),
          buffer_before=0,  # Will be auto-set based on preferences
          buffer_after=0    # Will be auto-set based on preferences
      )
      
      # Service automatically applies user preferences:
      # - meeting_buffer_before: 10 minutes
      # - meeting_buffer_after: 10 minutes
      # - Conflict checking includes buffer times

**Conflict Detection and Resolution**
   Comprehensive conflict detection with ADHD-friendly resolution suggestions:
   
   - **Real-Time Conflict Detection**: Immediate feedback when scheduling conflicts arise
   - **Buffer-Aware Conflicts**: Includes transition times in conflict calculations
   - **Severity Assessment**: Different conflict levels (low, medium, high) with appropriate responses
   - **Resolution Suggestions**: Practical, actionable suggestions for resolving conflicts
   - **Flexible Conflict Handling**: Options to auto-resolve, suggest alternatives, or warn user

   .. code-block:: python
   
      # Validate schedule for conflicts and over-scheduling
      validation = await time_service.validate_schedule(user_id, date)
      
      # ScheduleValidationResponse includes:
      # - is_valid: Overall schedule validity
      # - conflicts: List of detected conflicts with suggestions
      # - over_scheduled_by: Minutes over available time
      # - warnings: ADHD-specific warnings (too many blocks, no breaks)
      # - suggestions: Actionable improvement recommendations

**Schedule Optimization**
   ADHD-aware scheduling that considers energy patterns and cognitive load:
   
   - **Energy Level Matching**: Tasks scheduled during optimal energy periods
   - **Cognitive Load Balancing**: Heavy tasks balanced with lighter activities
   - **Break Insertion**: Automatic suggestion of break times for attention restoration
   - **Task Switching Costs**: Consideration of context switching overhead
   - **Deadline Awareness**: Urgent tasks prioritized while maintaining balance

Intelligent Scheduling Algorithms
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Time Slot Suggestions**
   AI-powered recommendations for optimal task scheduling:
   
   - **Pattern Recognition**: Learning from user's historical productivity patterns
   - **Energy Matching**: Aligning task energy requirements with user energy levels
   - **Context Awareness**: Considering existing schedule and task complexity
   - **Confidence Scoring**: Transparent confidence levels for each suggestion
   - **Multiple Options**: Providing several alternatives with clear reasoning

   .. code-block:: python
   
      # Get intelligent time slot suggestions
      suggestions = await time_service.suggest_optimal_times(
          user_id=user.id,
          task=complex_task,
          date=target_date,
          duration_minutes=90
      )
      
      # Each TimeSlotSuggestion includes:
      # - start_time, end_time: Suggested time slot
      # - confidence_score: AI confidence (0.0 to 1.0)
      # - reasoning: Human-readable explanation
      # - energy_match: Energy level alignment
      # - conflicts: Any potential issues

**Auto-Scheduling**
   Intelligent automatic scheduling with ADHD accommodations:
   
   - **Preference-Based Scheduling**: Respects user's time and energy preferences
   - **Constraint Handling**: Works within user-defined constraints and limitations
   - **Flexible Optimization**: Balances efficiency with ADHD-friendly pacing
   - **Iterative Improvement**: Learns from user feedback to improve suggestions
   - **Manual Override**: Always allows user control and manual adjustments

Schedule Validation & Health Checks
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Over-Scheduling Detection**
   Prevents the ADHD tendency to overcommit by providing reality checks:
   
   - **Time Budget Analysis**: Compares scheduled time to available time
   - **Realistic Capacity**: Accounts for ADHD-specific factors like transition time
   - **Warning Thresholds**: Alerts when approaching or exceeding capacity
   - **Suggestion Engine**: Provides specific recommendations for schedule adjustment
   - **Visual Feedback**: Clear visual indicators of schedule health

**ADHD-Specific Validations**
   Schedule checks tailored to ADHD challenges:
   
   - **Break Frequency**: Ensures adequate breaks for attention restoration
   - **Task Variety**: Prevents monotonous scheduling that leads to boredom
   - **Energy Distribution**: Balances high and low energy activities
   - **Decision Fatigue**: Warns when too many small blocks create choice overload
   - **Hyperfocus Protection**: Identifies potentially problematic long work blocks

   .. code-block:: python
   
      # ADHD-specific schedule validation
      validation = await time_service.validate_schedule(user_id, date)
      
      # Checks include:
      # - Over-scheduling by minutes
      # - Missing break periods
      # - Too many small time blocks (decision fatigue)
      # - Unbalanced energy distribution
      # - Potential hyperfocus triggers

Buffer Time Management
~~~~~~~~~~~~~~~~~~~~~

**Adaptive Buffer Calculation**
   Smart buffer time suggestions based on activity type and user patterns:
   
   - **Activity-Specific Buffers**: Different defaults for meetings, tasks, personal time
   - **Historical Learning**: Adjusts based on how long transitions actually take
   - **Context Awareness**: Longer buffers for stressful or complex transitions
   - **User Customization**: Fully customizable defaults with smart suggestions
   - **Emergency Flexibility**: Options to reduce buffers when schedule is tight

**Travel Time Integration**
   Automatic calculation and insertion of travel time between locations:
   
   - **Location-Aware Scheduling**: Considers physical location changes
   - **Transportation Mode**: Different calculations for walking, driving, public transit
   - **Traffic Considerations**: Real-time traffic data integration (future feature)
   - **Backup Time**: Additional buffer for unexpected delays
   - **Route Optimization**: Suggests optimal ordering of location-based activities

Technical Implementation
-----------------------

Database Models
~~~~~~~~~~~~~~

**TimeBlock Model**
   Core time blocking with ADHD-specific features:
   
   .. autoclass:: app.models.time_block.TimeBlock
      :members:
      :show-inheritance:

**SchedulingPreference Model**
   User preferences for ADHD-optimized scheduling:
   
   .. autoclass:: app.models.time_block.SchedulingPreference
      :members:
      :show-inheritance:

**ScheduleTemplate Model**
   Reusable schedule patterns to reduce planning overhead:
   
   .. autoclass:: app.models.time_block.ScheduleTemplate
      :members:
      :show-inheritance:

Service Architecture
~~~~~~~~~~~~~~~~~~~

**TimeBlockingService**
   Core business logic for ADHD-optimized time management:
   
   .. autoclass:: app.services.time_service.TimeBlockingService
      :members:
      :show-inheritance:

**TimeVisualizationService**
   Visual time interface generation for time blindness support:
   
   .. autoclass:: app.services.time_service.TimeVisualizationService
      :members:
      :show-inheritance:

API Integration
~~~~~~~~~~~~~~

**Time Blocking Endpoints**
   Complete REST API for time block management:
   
   - ``POST /time-blocks`` - Create time block with conflict checking
   - ``GET /time-blocks/daily/{date}`` - Get daily schedule with visual data
   - ``POST /time-blocks/validate/{date}`` - Validate schedule for ADHD issues
   - ``PUT /time-blocks/{id}/move`` - Move block with conflict detection
   - ``GET /time-blocks/{id}`` - Get detailed time block information
   - ``PUT /time-blocks/{id}`` - Update time block with validation
   - ``DELETE /time-blocks/{id}`` - Delete time block with impact assessment

**Visual Interface Endpoints**
   Specialized endpoints for ADHD-friendly time visualization:
   
   - ``GET /time-blocks/views/circular/{date}`` - Circular clock view data
   - ``GET /time-blocks/views/timeline/{date}`` - Timeline view with positioning
   - ``GET /time-blocks/suggestions/{date}`` - Intelligent time slot suggestions

**Request/Response Examples**
   
   **Create Time Block Request**:
   
   .. code-block:: json
   
      {
        "title": "Deep Work Session",
        "description": "Focus on important project",
        "block_type": "task",
        "start_time": "2024-01-15T09:00:00Z",
        "end_time": "2024-01-15T11:00:00Z",
        "task_id": "550e8400-e29b-41d4-a716-446655440000",
        "is_flexible": true,
        "buffer_before": 10,
        "buffer_after": 15,
        "energy_level_required": "high",
        "color": "#3B82F6",
        "category": "work"
      }
   
   **Daily Schedule Response**:
   
   .. code-block:: json
   
      {
        "date": "2024-01-15T00:00:00Z",
        "time_blocks": [...],
        "total_scheduled_time": 480,
        "total_available_time": 960,
        "utilization_percentage": 50.0,
        "conflicts": [],
        "suggestions": [
          "Consider adding a break between long work sessions",
          "Schedule high-energy tasks during your peak hours (9-11 AM)"
        ],
        "view_data": {
          "hour_height": 60,
          "total_height": 960,
          "positioned_blocks": [...],
          "current_time_line": {...}
        }
      }

ADHD-Specific Optimizations
--------------------------

Time Blindness Support
~~~~~~~~~~~~~~~~~~~~~

**Visual Time Representation**
   Making abstract time concrete and manageable:
   
   - **Proportional Visualization**: Time blocks sized proportionally to actual duration
   - **Color-Coded Categories**: Visual differentiation reduces cognitive load
   - **Real-Time Indicators**: Current time clearly marked for orientation
   - **Duration Labels**: Clear text labels showing actual time amounts
   - **Progress Indicators**: Visual feedback on time remaining or elapsed

**Time Estimation Assistance**
   Helping users develop better time awareness:
   
   - **Historical Duration Data**: Learning from actual vs. estimated times
   - **Estimation Feedback**: Gentle feedback on time estimation accuracy
   - **Template Durations**: Pre-filled durations based on similar past activities
   - **Buffer Recommendations**: Suggestions for realistic time padding
   - **Reality Check Warnings**: Alerts when estimates seem unrealistic

Executive Function Support
~~~~~~~~~~~~~~~~~~~~~~~~~

**Decision Fatigue Reduction**
   Minimizing cognitive overhead in schedule management:
   
   - **Smart Defaults**: Intelligent default values based on user patterns
   - **Template Scheduling**: Reusable schedule patterns for common days
   - **Batch Operations**: Ability to schedule multiple similar blocks at once
   - **Quick Actions**: One-click scheduling for routine activities
   - **Simplified Choices**: Reduced options when decision fatigue is detected

**Planning Assistance**
   Structured support for schedule creation and management:
   
   - **Guided Scheduling**: Step-by-step assistance for complex scheduling
   - **Constraint Checking**: Automatic validation of scheduling constraints
   - **Optimization Suggestions**: AI-powered recommendations for better scheduling
   - **Progress Tracking**: Visual feedback on scheduling completion
   - **Flexible Adjustment**: Easy modification without starting over

Energy Management Integration
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Energy-Aware Scheduling**
   Matching tasks to optimal energy levels:
   
   - **Energy Pattern Recognition**: Learning user's daily energy fluctuations
   - **Task-Energy Matching**: Scheduling high-energy tasks during peak times
   - **Energy Budget Tracking**: Monitoring energy expenditure throughout the day
   - **Recovery Time Planning**: Ensuring adequate rest between intensive activities
   - **Sustainable Pacing**: Preventing energy burnout through smart scheduling

**Spoon Theory Integration**
   Incorporating disability-aware energy management:
   
   - **Energy Spoon Allocation**: Visual representation of available energy
   - **Activity Energy Costs**: Clear indication of energy requirements
   - **Conservation Strategies**: Suggestions for energy-efficient scheduling
   - **Recovery Planning**: Scheduled downtime for energy restoration
   - **Flexible Energy Budgets**: Adaptive planning based on available energy

Usage Examples
-------------

Basic Time Blocking Flow
~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   # Create a time blocking service
   time_service = TimeBlockingService(db)
   
   # Create a time block with ADHD accommodations
   block_data = TimeBlockCreate(
       title="Project Planning Session",
       description="Plan next quarter's major initiatives",
       block_type=TimeBlockType.TASK,
       start_time=datetime(2024, 1, 15, 10, 0),
       end_time=datetime(2024, 1, 15, 12, 0),
       task_id=planning_task.id,
       is_flexible=True,
       energy_level_required="high",
       category="strategic_work"
   )
   
   # Service automatically adds appropriate buffers
   time_block = await time_service.create_time_block(
       user_id=user.id,
       block_data=block_data
   )
   
   # Validate the day's schedule
   validation = await time_service.validate_schedule(user.id, date)
   if not validation.is_valid:
       print(f"Schedule issues: {validation.warnings}")
       print(f"Suggestions: {validation.suggestions}")

Visual Time Interface Usage
~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   # Generate circular clock view for time blindness support
   viz_service = TimeVisualizationService()
   
   # Get daily time blocks
   time_blocks = await time_service.get_daily_schedule(
       user_id=user.id,
       date=today,
       include_buffers=True
   )
   
   # Create circular view for intuitive time proportions
   circular_view = viz_service.generate_circular_view(
       time_blocks=time_blocks,
       date=today
   )
   
   # Create timeline view for drag-and-drop interface
   timeline_view = viz_service.generate_timeline_view(
       time_blocks=time_blocks,
       date=today,
       hour_height=60
   )
   
   # Calculate time proportions for overview
   proportions = viz_service.calculate_time_proportions(time_blocks)
   print(f"Work: {proportions.get('task', 0):.1%}")
   print(f"Breaks: {proportions.get('break', 0):.1%}")
   print(f"Meetings: {proportions.get('meeting', 0):.1%}")

This comprehensive time blocking system directly addresses time blindness and executive dysfunction by making abstract time tangible, manageable, and visually intuitive for ADHD users.
