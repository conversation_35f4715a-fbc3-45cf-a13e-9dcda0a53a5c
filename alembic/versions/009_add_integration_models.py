"""Add integration models for external service connections

Revision ID: 009_add_integration_models
Revises: 008_add_notification_models
Create Date: 2024-01-15 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '009_add_integration_models'
down_revision = '008_add_notification_models'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Create integration_type enum
    integration_type_enum = postgresql.ENUM(
        'google_calendar',
        'outlook_calendar', 
        'todoist',
        'notion',
        'slack',
        'github',
        'trello',
        'asana',
        name='integrationtype'
    )
    integration_type_enum.create(op.get_bind())
    
    # Create integration_status enum
    integration_status_enum = postgresql.ENUM(
        'active',
        'inactive',
        'error',
        'expired',
        'revoked',
        name='integrationstatus'
    )
    integration_status_enum.create(op.get_bind())
    
    # Create sync_status enum
    sync_status_enum = postgresql.ENUM(
        'pending',
        'in_progress',
        'completed',
        'failed',
        'partial',
        name='syncstatus'
    )
    sync_status_enum.create(op.get_bind())
    
    # Create integrations table
    op.create_table(
        'integrations',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True),
        sa.Column('user_id', postgresql.UUID(as_uuid=True), sa.ForeignKey('users.id', ondelete='CASCADE'), nullable=False),
        sa.Column('integration_type', integration_type_enum, nullable=False),
        sa.Column('name', sa.String(100), nullable=False),
        sa.Column('description', sa.Text, nullable=True),
        sa.Column('external_id', sa.String(255), nullable=True),
        sa.Column('access_token', sa.Text, nullable=True),
        sa.Column('refresh_token', sa.Text, nullable=True),
        sa.Column('token_expires_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('config', postgresql.JSONB, nullable=False, default={}),
        sa.Column('sync_settings', postgresql.JSONB, nullable=False, default={}),
        sa.Column('status', integration_status_enum, nullable=False, default='active'),
        sa.Column('last_sync_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('last_error', sa.Text, nullable=True),
        sa.Column('error_count', sa.Integer, nullable=False, default=0),
        sa.Column('total_syncs', sa.Integer, nullable=False, default=0),
        sa.Column('successful_syncs', sa.Integer, nullable=False, default=0),
        sa.Column('webhook_id', sa.String(255), nullable=True),
        sa.Column('webhook_secret', sa.String(255), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.func.now(), onupdate=sa.func.now(), nullable=False),
    )
    
    # Create sync_logs table
    op.create_table(
        'sync_logs',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True),
        sa.Column('integration_id', postgresql.UUID(as_uuid=True), sa.ForeignKey('integrations.id', ondelete='CASCADE'), nullable=False),
        sa.Column('operation_type', sa.String(50), nullable=False),
        sa.Column('status', sync_status_enum, nullable=False, default='pending'),
        sa.Column('started_at', sa.DateTime(timezone=True), nullable=False, default=sa.func.now()),
        sa.Column('completed_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('items_processed', sa.Integer, nullable=False, default=0),
        sa.Column('items_created', sa.Integer, nullable=False, default=0),
        sa.Column('items_updated', sa.Integer, nullable=False, default=0),
        sa.Column('items_deleted', sa.Integer, nullable=False, default=0),
        sa.Column('conflicts_detected', sa.Integer, nullable=False, default=0),
        sa.Column('error_message', sa.Text, nullable=True),
        sa.Column('error_details', postgresql.JSONB, nullable=False, default={}),
        sa.Column('sync_data', postgresql.JSONB, nullable=False, default={}),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.func.now(), onupdate=sa.func.now(), nullable=False),
    )
    
    # Create webhook_events table
    op.create_table(
        'webhook_events',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True),
        sa.Column('integration_id', postgresql.UUID(as_uuid=True), sa.ForeignKey('integrations.id', ondelete='SET NULL'), nullable=True),
        sa.Column('integration_type', integration_type_enum, nullable=False),
        sa.Column('event_type', sa.String(100), nullable=False),
        sa.Column('external_id', sa.String(255), nullable=True),
        sa.Column('payload', postgresql.JSONB, nullable=False, default={}),
        sa.Column('headers', postgresql.JSONB, nullable=False, default={}),
        sa.Column('processed', sa.Boolean, nullable=False, default=False),
        sa.Column('processed_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('processing_error', sa.Text, nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.func.now(), onupdate=sa.func.now(), nullable=False),
    )
    
    # Create indexes for integrations table
    op.create_index('idx_integrations_user', 'integrations', ['user_id'])
    op.create_index('idx_integrations_type', 'integrations', ['integration_type'])
    op.create_index('idx_integrations_status', 'integrations', ['status'])
    op.create_index('idx_integrations_last_sync', 'integrations', ['last_sync_at'])
    
    # Create unique constraint for user integrations
    op.create_unique_constraint(
        'uq_user_integration',
        'integrations',
        ['user_id', 'integration_type', 'external_id']
    )
    
    # Create indexes for sync_logs table
    op.create_index('idx_sync_logs_integration', 'sync_logs', ['integration_id'])
    op.create_index('idx_sync_logs_status', 'sync_logs', ['status'])
    op.create_index('idx_sync_logs_started', 'sync_logs', ['started_at'])
    op.create_index('idx_sync_logs_operation', 'sync_logs', ['operation_type'])
    
    # Create indexes for webhook_events table
    op.create_index('idx_webhook_events_integration', 'webhook_events', ['integration_id'])
    op.create_index('idx_webhook_events_type', 'webhook_events', ['integration_type'])
    op.create_index('idx_webhook_events_processed', 'webhook_events', ['processed'])
    op.create_index('idx_webhook_events_created', 'webhook_events', ['created_at'])


def downgrade() -> None:
    # Drop indexes
    op.drop_index('idx_webhook_events_created', 'webhook_events')
    op.drop_index('idx_webhook_events_processed', 'webhook_events')
    op.drop_index('idx_webhook_events_type', 'webhook_events')
    op.drop_index('idx_webhook_events_integration', 'webhook_events')
    
    op.drop_index('idx_sync_logs_operation', 'sync_logs')
    op.drop_index('idx_sync_logs_started', 'sync_logs')
    op.drop_index('idx_sync_logs_status', 'sync_logs')
    op.drop_index('idx_sync_logs_integration', 'sync_logs')
    
    op.drop_constraint('uq_user_integration', 'integrations', type_='unique')
    op.drop_index('idx_integrations_last_sync', 'integrations')
    op.drop_index('idx_integrations_status', 'integrations')
    op.drop_index('idx_integrations_type', 'integrations')
    op.drop_index('idx_integrations_user', 'integrations')
    
    # Drop tables
    op.drop_table('webhook_events')
    op.drop_table('sync_logs')
    op.drop_table('integrations')
    
    # Drop enums
    op.execute('DROP TYPE syncstatus')
    op.execute('DROP TYPE integrationstatus')
    op.execute('DROP TYPE integrationtype')
