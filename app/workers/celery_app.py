"""
Celery configuration for Project Chronos.

This module sets up Celery for background task processing including
notification delivery, reminder scheduling, and periodic tasks.
"""

import os
from celery import Celery
from celery.schedules import crontab
from kombu import Queue

from app.core.config import settings

# Create Celery instance
celery_app = Celery(
    "chronos",
    broker=settings.celery_broker_url,
    backend=settings.celery_result_backend,
    include=[
        "app.workers.notification_worker",
        "app.workers.reminder_worker",
        "app.workers.cleanup_worker",
    ]
)

# Celery configuration
celery_app.conf.update(
    # Task routing
    task_routes={
        "deliver_notification": {"queue": "notifications"},
        "send_email_notification": {"queue": "notifications"},
        "send_sms_notification": {"queue": "notifications"},
        "check_notification_acknowledgment": {"queue": "notifications"},
        "escalate_notification": {"queue": "notifications"},
        "process_daily_reminders": {"queue": "reminders"},
        "process_staggered_reminders": {"queue": "reminders"},
        "cleanup_old_notifications": {"queue": "cleanup"},
        "cleanup_expired_notifications": {"queue": "cleanup"},
    },
    
    # Queue configuration
    task_default_queue="default",
    task_queues=(
        Queue("default", routing_key="default"),
        Queue("notifications", routing_key="notifications"),
        Queue("reminders", routing_key="reminders"),
        Queue("cleanup", routing_key="cleanup"),
    ),
    
    # Task execution settings
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="UTC",
    enable_utc=True,
    
    # Task retry settings
    task_acks_late=True,
    task_reject_on_worker_lost=True,
    task_default_retry_delay=60,  # 1 minute
    task_max_retries=3,
    
    # Worker settings
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=1000,
    
    # Result backend settings
    result_expires=3600,  # 1 hour
    result_backend_transport_options={
        "master_name": "mymaster",
        "visibility_timeout": 3600,
    },
    
    # Beat schedule for periodic tasks
    beat_schedule={
        # Process daily reminders every hour
        "process-daily-reminders": {
            "task": "process_daily_reminders",
            "schedule": crontab(minute=0),  # Every hour
        },
        
        # Process staggered reminders every 5 minutes
        "process-staggered-reminders": {
            "task": "process_staggered_reminders",
            "schedule": crontab(minute="*/5"),  # Every 5 minutes
        },
        
        # Cleanup expired notifications daily at 2 AM
        "cleanup-expired-notifications": {
            "task": "cleanup_expired_notifications",
            "schedule": crontab(hour=2, minute=0),  # Daily at 2 AM
        },
        
        # Cleanup old notifications weekly on Sunday at 3 AM
        "cleanup-old-notifications": {
            "task": "cleanup_old_notifications",
            "schedule": crontab(hour=3, minute=0, day_of_week=0),  # Weekly on Sunday
        },
        
        # Check for overdue notifications every 10 minutes
        "check-overdue-notifications": {
            "task": "check_overdue_notifications",
            "schedule": crontab(minute="*/10"),  # Every 10 minutes
        },
        
        # Process notification batches at designated times
        "process-notification-batches-morning": {
            "task": "process_notification_batches",
            "schedule": crontab(hour=9, minute=0),  # 9 AM
        },
        "process-notification-batches-afternoon": {
            "task": "process_notification_batches",
            "schedule": crontab(hour=13, minute=0),  # 1 PM
        },
        "process-notification-batches-evening": {
            "task": "process_notification_batches",
            "schedule": crontab(hour=17, minute=0),  # 5 PM
        },
    },
    
    # Monitoring and logging
    worker_send_task_events=True,
    task_send_sent_event=True,
    
    # Security settings
    task_always_eager=False,  # Set to True for testing
    task_eager_propagates=True,
    
    # ADHD-specific settings
    task_soft_time_limit=300,  # 5 minutes soft limit
    task_time_limit=600,       # 10 minutes hard limit
    
    # Custom settings for notification processing
    notification_batch_size=50,
    notification_retry_backoff=True,
    notification_retry_jitter=True,
)

# Task error handling
@celery_app.task(bind=True)
def debug_task(self):
    """Debug task for testing Celery setup."""
    print(f"Request: {self.request!r}")


# Custom task base class for ADHD-optimized error handling
class ADHDTask(celery_app.Task):
    """
    Custom task base class with ADHD-friendly error handling.
    
    Provides gentle error handling and retry logic that doesn't
    overwhelm users with technical failures.
    """
    
    def on_failure(self, exc, task_id, args, kwargs, einfo):
        """Handle task failure with ADHD-friendly logging."""
        print(f"Task {task_id} failed: {exc}")
        
        # Log failure for monitoring but don't overwhelm user
        # Could send a gentle notification about system issues
        
    def on_retry(self, exc, task_id, args, kwargs, einfo):
        """Handle task retry with appropriate delays."""
        print(f"Task {task_id} retrying: {exc}")
        
    def on_success(self, retval, task_id, args, kwargs):
        """Handle successful task completion."""
        # Could update metrics or send success notifications
        pass


# Set default task base class
celery_app.Task = ADHDTask


# Celery signals for monitoring
@celery_app.signals.worker_ready.connect
def worker_ready(sender=None, **kwargs):
    """Signal when worker is ready."""
    print("Celery worker is ready")


@celery_app.signals.worker_shutdown.connect
def worker_shutdown(sender=None, **kwargs):
    """Signal when worker is shutting down."""
    print("Celery worker is shutting down")


@celery_app.signals.task_prerun.connect
def task_prerun(sender=None, task_id=None, task=None, args=None, kwargs=None, **kwds):
    """Signal before task execution."""
    print(f"Task {task_id} starting: {task.name}")


@celery_app.signals.task_postrun.connect
def task_postrun(sender=None, task_id=None, task=None, args=None, kwargs=None, 
                 retval=None, state=None, **kwds):
    """Signal after task execution."""
    print(f"Task {task_id} finished: {state}")


# Health check task
@celery_app.task(name="health_check")
def health_check():
    """Health check task for monitoring."""
    return {"status": "healthy", "timestamp": "2024-01-01T00:00:00Z"}


# Configuration validation
def validate_celery_config():
    """Validate Celery configuration for ADHD requirements."""
    required_settings = [
        "celery_broker_url",
        "celery_result_backend",
    ]

    missing_settings = []
    for setting in required_settings:
        if not hasattr(settings, setting) or not getattr(settings, setting):
            missing_settings.append(setting)

    if missing_settings:
        raise ValueError(f"Missing required settings: {missing_settings}")

    print("Celery configuration validated successfully")


# Initialize configuration validation
try:
    validate_celery_config()
except ValueError as e:
    print(f"Celery configuration error: {e}")


# Export celery app
__all__ = ["celery_app"]
