"""
Motivation and dopamine menu schemas for Project Chronos.

This module defines Pydantic schemas for motivation features including
dopamine activities, user preferences, and motivation insights.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional
from uuid import UUID

from pydantic import BaseModel, Field, ConfigDict


class DopamineActivityBase(BaseModel):
    """Base schema for dopamine activities."""
    
    name: str = Field(description="Name of the dopamine activity")
    description: str = Field(description="Description of the activity")
    category: str = Field(description="Activity category")
    duration_min: int = Field(description="Minimum duration in minutes")
    duration_max: int = Field(description="Maximum duration in minutes")
    energy_requirement: str = Field(description="Energy level required")
    energy_boost: str = Field(description="Energy boost provided")
    requires_equipment: bool = Field(default=False, description="Requires special equipment")
    requires_space: bool = Field(default=False, description="Requires significant space")
    can_do_anywhere: bool = Field(default=True, description="Can be done anywhere")
    tags: List[str] = Field(default_factory=list, description="Activity tags")
    instructions: Optional[str] = None
    is_active: bool = Field(default=True, description="Whether activity is active")


class DopamineActivityCreate(DopamineActivityBase):
    """Schema for creating a dopamine activity."""
    pass


class DopamineActivityUpdate(BaseModel):
    """Schema for updating a dopamine activity."""
    
    name: Optional[str] = None
    description: Optional[str] = None
    category: Optional[str] = None
    duration_min: Optional[int] = None
    duration_max: Optional[int] = None
    energy_requirement: Optional[str] = None
    energy_boost: Optional[str] = None
    requires_equipment: Optional[bool] = None
    requires_space: Optional[bool] = None
    can_do_anywhere: Optional[bool] = None
    tags: Optional[List[str]] = None
    instructions: Optional[str] = None
    is_active: Optional[bool] = None


class DopamineActivityResponse(DopamineActivityBase):
    """Schema for dopamine activity responses."""
    
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID
    created_at: datetime
    updated_at: datetime


class DopamineMenuRequest(BaseModel):
    """Schema for dopamine menu requests."""
    
    energy_level: str = Field(default="medium", description="Current energy level")
    available_time: int = Field(default=5, description="Available time in minutes")
    context: str = Field(default="pre_task", description="Context for the activity")
    exclude_categories: List[str] = Field(default_factory=list, description="Categories to exclude")
    preferred_categories: List[str] = Field(default_factory=list, description="Preferred categories")


class DopamineMenuResponse(BaseModel):
    """Schema for dopamine menu responses."""
    
    activities: List[DopamineActivityResponse]
    personalization_score: float = Field(description="How personalized the suggestions are")
    context: str
    energy_level: str
    available_time: int


class UserDopaminePreferenceBase(BaseModel):
    """Base schema for user dopamine preferences."""
    
    preference_type: str = Field(description="Type of preference")
    rating: Optional[int] = None
    custom_name: Optional[str] = None
    custom_description: Optional[str] = None
    custom_duration: Optional[int] = None
    custom_category: Optional[str] = None


class UserDopaminePreferenceCreate(UserDopaminePreferenceBase):
    """Schema for creating user dopamine preferences."""
    
    user_id: UUID
    activity_id: Optional[UUID] = None


class UserDopaminePreferenceUpdate(BaseModel):
    """Schema for updating user dopamine preferences."""
    
    preference_type: Optional[str] = None
    rating: Optional[int] = None
    custom_name: Optional[str] = None
    custom_description: Optional[str] = None
    custom_duration: Optional[int] = None
    custom_category: Optional[str] = None


class UserDopaminePreferenceResponse(UserDopaminePreferenceBase):
    """Schema for user dopamine preference responses."""
    
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID
    user_id: UUID
    activity_id: Optional[UUID] = None
    activity: Optional[DopamineActivityResponse] = None
    times_suggested: int
    times_completed: int
    last_suggested: Optional[datetime] = None
    last_completed: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime


class DopamineActivityCompletionBase(BaseModel):
    """Base schema for dopamine activity completions."""
    
    actual_duration: int = Field(description="Actual duration in minutes")
    energy_before: str = Field(description="Energy level before activity")
    energy_after: str = Field(description="Energy level after activity")
    mood_before: Optional[int] = None
    mood_after: Optional[int] = None
    helped_with_task: Optional[bool] = None
    satisfaction_rating: Optional[int] = None
    would_do_again: Optional[bool] = None
    context: str = Field(description="Context of the activity")
    notes: Optional[str] = None


class DopamineActivityCompletionCreate(DopamineActivityCompletionBase):
    """Schema for creating dopamine activity completions."""
    
    user_id: UUID
    activity_id: Optional[UUID] = None
    task_id: Optional[UUID] = None


class DopamineActivityCompletionResponse(DopamineActivityCompletionBase):
    """Schema for dopamine activity completion responses."""
    
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID
    user_id: UUID
    activity_id: Optional[UUID] = None
    task_id: Optional[UUID] = None
    activity: Optional[DopamineActivityResponse] = None
    created_at: datetime


class MotivationInsightBase(BaseModel):
    """Base schema for motivation insights."""
    
    insight_type: str = Field(description="Type of insight")
    title: str = Field(description="Title of the insight")
    description: str = Field(description="Detailed description")
    insight_data: Dict[str, Any] = Field(default_factory=dict, description="Supporting data")
    confidence_score: float = Field(description="Confidence score (0.0-1.0)")
    is_active: bool = Field(default=True, description="Whether insight is active")
    is_read: bool = Field(default=False, description="Whether user has read this")
    valid_until: Optional[datetime] = None


class MotivationInsightResponse(MotivationInsightBase):
    """Schema for motivation insight responses."""
    
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID
    user_id: UUID
    read_at: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime


class MotivationAnalyticsResponse(BaseModel):
    """Schema for motivation analytics responses."""
    
    total_activities_completed: int
    favorite_categories: List[str]
    most_effective_activities: List[DopamineActivityResponse]
    energy_patterns: Dict[str, Any]
    mood_improvements: Dict[str, float]
    completion_rate_by_context: Dict[str, float]
    insights: List[MotivationInsightResponse]
    recommendations: List[str]


class MotivationDashboardResponse(BaseModel):
    """Schema for motivation dashboard responses."""
    
    recent_completions: List[DopamineActivityCompletionResponse]
    suggested_activities: List[DopamineActivityResponse]
    active_insights: List[MotivationInsightResponse]
    analytics: MotivationAnalyticsResponse
    personalization_tips: List[str]


class EnergyMoodTrackingRequest(BaseModel):
    """Schema for energy and mood tracking requests."""
    
    energy_level: str = Field(description="Current energy level")
    mood_rating: Optional[int] = Field(None, description="Mood rating 1-10")
    context: Dict[str, Any] = Field(default_factory=dict, description="Context information")


class EnergyMoodTrackingResponse(BaseModel):
    """Schema for energy and mood tracking responses."""
    
    recorded_at: datetime
    energy_level: str
    mood_rating: Optional[int]
    suggested_activities: List[DopamineActivityResponse]
    insights: List[str]


class CustomActivityRequest(BaseModel):
    """Schema for creating custom dopamine activities."""
    
    name: str
    description: str
    category: str
    duration: int = Field(description="Duration in minutes")
    energy_requirement: str = Field(default="medium")
    energy_boost: str = Field(default="medium")
    requires_equipment: bool = False
    requires_space: bool = False
    tags: List[str] = Field(default_factory=list)
    instructions: Optional[str] = None
