"""
Gamification schemas for Project Chronos.

This module defines Pydantic schemas for gamification features including
points, achievements, streaks, and user gamification profiles.
"""

from datetime import datetime, date
from typing import Any, Dict, List, Optional
from uuid import UUID

from pydantic import BaseModel, Field, ConfigDict


class GamificationProfileBase(BaseModel):
    """Base schema for gamification profile."""
    
    total_points: int = Field(default=0, description="Total points earned by the user")
    level: int = Field(default=1, description="Current user level")
    points_to_next_level: int = Field(default=100, description="Points needed for next level")
    gamification_enabled: bool = Field(default=True, description="Whether gamification is enabled")
    celebration_style: str = Field(default="moderate", description="Celebration intensity")
    preferred_rewards: Dict[str, Any] = Field(default_factory=dict, description="Preferred reward types")


class GamificationProfileCreate(GamificationProfileBase):
    """Schema for creating a gamification profile."""
    pass


class GamificationProfileUpdate(BaseModel):
    """Schema for updating a gamification profile."""
    
    gamification_enabled: Optional[bool] = None
    celebration_style: Optional[str] = None
    preferred_rewards: Optional[Dict[str, Any]] = None


class GamificationProfileResponse(GamificationProfileBase):
    """Schema for gamification profile responses."""
    
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID
    user_id: UUID
    created_at: datetime
    updated_at: datetime


class PointsAwardBase(BaseModel):
    """Base schema for points awards."""
    
    points_awarded: int = Field(description="Number of points awarded")
    reason: str = Field(description="Reason for the point award")
    multiplier: float = Field(default=1.0, description="Multiplier applied to base points")
    total_points_after: int = Field(description="User's total points after this award")
    award_metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")


class PointsAwardCreate(BaseModel):
    """Schema for creating a points award."""
    
    user_id: UUID
    points: int = Field(description="Base points to award")
    reason: str = Field(description="Reason for the award")
    task_id: Optional[UUID] = None
    multiplier: float = Field(default=1.0, description="Difficulty/energy multiplier")


class PointsAwardResponse(PointsAwardBase):
    """Schema for points award responses."""
    
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID
    user_id: UUID
    task_id: Optional[UUID] = None
    created_at: datetime


class AchievementBase(BaseModel):
    """Base schema for achievements."""
    
    achievement_key: str = Field(description="Unique key for the achievement")
    name: str = Field(description="Display name of the achievement")
    description: str = Field(description="Description of the achievement")
    category: str = Field(description="Achievement category")
    trigger_event: str = Field(description="Event that triggers this achievement")
    requirement_data: Dict[str, Any] = Field(default_factory=dict, description="Achievement requirements")
    reward_points: int = Field(default=0, description="Points awarded for unlocking")
    badge_icon: str = Field(description="Emoji or icon for the badge")
    is_hidden: bool = Field(default=False, description="Whether achievement is hidden")
    is_active: bool = Field(default=True, description="Whether achievement is active")


class AchievementCreate(AchievementBase):
    """Schema for creating an achievement."""
    pass


class AchievementUpdate(BaseModel):
    """Schema for updating an achievement."""
    
    name: Optional[str] = None
    description: Optional[str] = None
    requirement_data: Optional[Dict[str, Any]] = None
    reward_points: Optional[int] = None
    badge_icon: Optional[str] = None
    is_hidden: Optional[bool] = None
    is_active: Optional[bool] = None


class AchievementResponse(AchievementBase):
    """Schema for achievement responses."""
    
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID
    created_at: datetime
    updated_at: datetime


class UserAchievementBase(BaseModel):
    """Base schema for user achievements."""
    
    is_unlocked: bool = Field(default=False, description="Whether achievement is unlocked")
    unlocked_at: Optional[datetime] = None
    progress_data: Dict[str, Any] = Field(default_factory=dict, description="Progress toward achievement")


class UserAchievementResponse(UserAchievementBase):
    """Schema for user achievement responses."""
    
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID
    user_id: UUID
    achievement_id: UUID
    achievement: AchievementResponse
    created_at: datetime
    updated_at: datetime


class StreakBase(BaseModel):
    """Base schema for streaks."""
    
    streak_type: str = Field(description="Type of streak")
    current_streak: int = Field(default=0, description="Current streak length")
    longest_streak: int = Field(default=0, description="Longest streak achieved")
    last_activity_date: Optional[date] = None
    freeze_count: int = Field(default=0, description="Number of streak freezes used")
    max_freezes: int = Field(default=3, description="Maximum streak freezes allowed")


class StreakUpdate(BaseModel):
    """Schema for streak updates."""
    
    streak_type: str
    action_completed: bool = True


class StreakResponse(StreakBase):
    """Schema for streak responses."""
    
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID
    user_id: UUID
    created_at: datetime
    updated_at: datetime


class LevelUpCelebration(BaseModel):
    """Schema for level up celebration data."""
    
    old_level: int
    new_level: int
    points_earned: int
    celebration_message: str
    rewards_unlocked: List[str] = Field(default_factory=list)


class GamificationStatsResponse(BaseModel):
    """Schema for gamification statistics."""
    
    total_points: int
    current_level: int
    points_to_next_level: int
    achievements_unlocked: int
    total_achievements: int
    active_streaks: List[StreakResponse]
    recent_awards: List[PointsAwardResponse]
    level_progress_percentage: float


class PointsCalculationRequest(BaseModel):
    """Schema for points calculation requests."""
    
    base_points: int
    task_difficulty: Optional[str] = None
    energy_level: Optional[str] = None
    time_of_day: Optional[str] = None
    context: Dict[str, Any] = Field(default_factory=dict)


class PointsCalculationResponse(BaseModel):
    """Schema for points calculation responses."""
    
    base_points: int
    final_points: int
    multiplier: float
    breakdown: Dict[str, float]
    celebration_level: str


class AchievementUnlockResponse(BaseModel):
    """Schema for achievement unlock responses."""
    
    achievement: AchievementResponse
    points_awarded: int
    celebration_message: str
    is_first_unlock: bool
    unlock_timestamp: datetime


class GamificationDashboardResponse(BaseModel):
    """Schema for gamification dashboard data."""
    
    profile: GamificationProfileResponse
    stats: GamificationStatsResponse
    recent_achievements: List[UserAchievementResponse]
    trending_up: List[str] = Field(description="Areas where user is improving")
    suggestions: List[str] = Field(description="Gamification suggestions")
    next_milestones: List[Dict[str, Any]] = Field(description="Upcoming milestones")
