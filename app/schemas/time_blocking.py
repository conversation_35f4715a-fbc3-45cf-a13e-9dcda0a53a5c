"""
Time blocking schemas for Project Chronos.

This module defines Pydantic schemas for time blocking, calendar integration,
and ADHD-friendly scheduling features.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional
from uuid import UUID

from pydantic import BaseModel, Field, validator


class TimeBlockBase(BaseModel):
    """Base schema for time block data."""
    
    title: str = Field(
        ...,
        max_length=200,
        description="Time block title/description"
    )
    
    description: Optional[str] = Field(
        None,
        description="Detailed description of the time block"
    )
    
    block_type: str = Field(
        ...,
        description="Type of time block: task, focus, body_doubling, break, buffer, meeting, personal"
    )
    
    start_time: datetime = Field(
        ...,
        description="When the time block starts"
    )
    
    end_time: datetime = Field(
        ...,
        description="When the time block ends"
    )
    
    buffer_before: int = Field(
        default=0,
        ge=0,
        le=60,
        description="Buffer time before the block in minutes"
    )
    
    buffer_after: int = Field(
        default=0,
        ge=0,
        le=60,
        description="Buffer time after the block in minutes"
    )
    
    energy_level_required: str = Field(
        default="medium",
        description="Required energy level: low, medium, high"
    )
    
    flexibility_level: str = Field(
        default="medium",
        description="How flexible this block is: rigid, medium, flexible"
    )
    
    block_settings: Dict[str, Any] = Field(
        default_factory=dict,
        description="Block-specific settings and preferences"
    )
    
    reminder_settings: Dict[str, Any] = Field(
        default_factory=dict,
        description="Reminder and notification settings"
    )
    
    preparation_notes: Optional[str] = Field(
        None,
        description="Notes for preparing for this time block"
    )
    
    @validator('block_type')
    def validate_block_type(cls, v):
        """Validate block type."""
        allowed_types = ['task', 'focus', 'body_doubling', 'break', 'buffer', 'meeting', 'personal']
        if v not in allowed_types:
            raise ValueError(f'Block type must be one of: {allowed_types}')
        return v
    
    @validator('energy_level_required')
    def validate_energy_level(cls, v):
        """Validate energy level."""
        allowed_levels = ['low', 'medium', 'high']
        if v not in allowed_levels:
            raise ValueError(f'Energy level must be one of: {allowed_levels}')
        return v
    
    @validator('flexibility_level')
    def validate_flexibility_level(cls, v):
        """Validate flexibility level."""
        allowed_levels = ['rigid', 'medium', 'flexible']
        if v not in allowed_levels:
            raise ValueError(f'Flexibility level must be one of: {allowed_levels}')
        return v
    
    @validator('end_time')
    def validate_end_time(cls, v, values):
        """Validate that end time is after start time."""
        if 'start_time' in values and v <= values['start_time']:
            raise ValueError('End time must be after start time')
        return v


class TimeBlockCreate(TimeBlockBase):
    """Schema for creating a new time block."""
    
    task_id: Optional[UUID] = Field(
        None,
        description="Associated task ID (if this is a task block)"
    )
    
    focus_session_id: Optional[UUID] = Field(
        None,
        description="Associated focus session ID (if this is a focus block)"
    )
    
    body_doubling_session_id: Optional[UUID] = Field(
        None,
        description="Associated body doubling session ID (if this is a collaboration block)"
    )
    
    is_recurring: bool = Field(
        default=False,
        description="Whether this is a recurring time block"
    )
    
    recurrence_pattern: Optional[Dict[str, Any]] = Field(
        None,
        description="Recurrence pattern configuration"
    )


class TimeBlockUpdate(BaseModel):
    """Schema for updating a time block."""
    
    title: Optional[str] = Field(
        None,
        max_length=200,
        description="Time block title/description"
    )
    
    description: Optional[str] = Field(
        None,
        description="Detailed description of the time block"
    )
    
    start_time: Optional[datetime] = Field(
        None,
        description="When the time block starts"
    )
    
    end_time: Optional[datetime] = Field(
        None,
        description="When the time block ends"
    )
    
    status: Optional[str] = Field(
        None,
        description="Block status: scheduled, active, completed, cancelled, rescheduled"
    )
    
    completion_percentage: Optional[int] = Field(
        None,
        ge=0,
        le=100,
        description="Completion percentage (0-100)"
    )
    
    actual_start_time: Optional[datetime] = Field(
        None,
        description="When the block actually started"
    )
    
    actual_end_time: Optional[datetime] = Field(
        None,
        description="When the block actually ended"
    )
    
    completion_notes: Optional[str] = Field(
        None,
        description="Notes after completing the time block"
    )
    
    @validator('status')
    def validate_status(cls, v):
        """Validate block status."""
        if v is not None:
            allowed_statuses = ['scheduled', 'active', 'completed', 'cancelled', 'rescheduled']
            if v not in allowed_statuses:
                raise ValueError(f'Status must be one of: {allowed_statuses}')
        return v


class TimeBlockResponse(BaseModel):
    """Schema for time block responses."""
    
    id: UUID
    user_id: UUID
    task_id: Optional[UUID]
    focus_session_id: Optional[UUID]
    body_doubling_session_id: Optional[UUID]
    title: str
    description: Optional[str]
    block_type: str
    start_time: datetime
    end_time: datetime
    duration_minutes: int
    buffer_before: int
    buffer_after: int
    energy_level_required: str
    flexibility_level: str
    status: str
    completion_percentage: int
    actual_start_time: Optional[datetime]
    actual_end_time: Optional[datetime]
    is_recurring: bool
    recurrence_pattern: Optional[Dict[str, Any]]
    parent_block_id: Optional[UUID]
    external_calendar_id: Optional[str]
    calendar_provider: Optional[str]
    sync_status: str
    block_settings: Dict[str, Any]
    reminder_settings: Dict[str, Any]
    preparation_notes: Optional[str]
    completion_notes: Optional[str]
    created_at: datetime
    updated_at: datetime
    
    # Computed fields (will be added dynamically)
    total_duration_with_buffers: Optional[int] = Field(None, description="Total duration including buffers")
    actual_duration: Optional[int] = Field(None, description="Actual duration if completed")
    is_active: Optional[bool] = Field(None, description="Whether block is currently active")
    is_completed: Optional[bool] = Field(None, description="Whether block is completed")
    is_overdue: Optional[bool] = Field(None, description="Whether block is overdue")
    time_until_start: Optional[int] = Field(None, description="Minutes until block starts")
    
    class Config:
        from_attributes = True
        # Exclude computed fields from ORM validation
        fields = {
            'total_duration_with_buffers': {'exclude': True},
            'actual_duration': {'exclude': True},
            'is_active': {'exclude': True},
            'is_completed': {'exclude': True},
            'is_overdue': {'exclude': True},
            'time_until_start': {'exclude': True}
        }


class CalendarIntegrationBase(BaseModel):
    """Base schema for calendar integration data."""
    
    provider: str = Field(
        ...,
        description="Calendar provider: google, outlook, apple, caldav, etc."
    )
    
    provider_calendar_id: str = Field(
        ...,
        max_length=255,
        description="Calendar ID from the provider"
    )
    
    calendar_name: str = Field(
        ...,
        max_length=200,
        description="Display name for the calendar"
    )
    
    sync_direction: str = Field(
        default="bidirectional",
        description="Sync direction: import_only, export_only, bidirectional"
    )
    
    sync_settings: Dict[str, Any] = Field(
        default_factory=dict,
        description="Sync preferences and configuration"
    )
    
    @validator('provider')
    def validate_provider(cls, v):
        """Validate calendar provider."""
        allowed_providers = ['google', 'outlook', 'apple', 'caldav', 'ical']
        if v not in allowed_providers:
            raise ValueError(f'Provider must be one of: {allowed_providers}')
        return v
    
    @validator('sync_direction')
    def validate_sync_direction(cls, v):
        """Validate sync direction."""
        allowed_directions = ['import_only', 'export_only', 'bidirectional']
        if v not in allowed_directions:
            raise ValueError(f'Sync direction must be one of: {allowed_directions}')
        return v


class CalendarIntegrationCreate(CalendarIntegrationBase):
    """Schema for creating a calendar integration."""
    
    access_token: Optional[str] = Field(
        None,
        description="Access token for API access (will be encrypted)"
    )
    
    refresh_token: Optional[str] = Field(
        None,
        description="Refresh token for token renewal (will be encrypted)"
    )


class CalendarIntegrationUpdate(BaseModel):
    """Schema for updating a calendar integration."""
    
    calendar_name: Optional[str] = Field(
        None,
        max_length=200,
        description="Display name for the calendar"
    )
    
    sync_enabled: Optional[bool] = Field(
        None,
        description="Whether sync is enabled for this calendar"
    )
    
    sync_direction: Optional[str] = Field(
        None,
        description="Sync direction: import_only, export_only, bidirectional"
    )
    
    sync_settings: Optional[Dict[str, Any]] = Field(
        None,
        description="Sync preferences and configuration"
    )


class CalendarIntegrationResponse(BaseModel):
    """Schema for calendar integration responses."""
    
    id: UUID
    user_id: UUID
    provider: str
    provider_calendar_id: str
    calendar_name: str
    sync_enabled: bool
    sync_direction: str
    last_sync_at: Optional[datetime]
    sync_status: str
    sync_settings: Dict[str, Any]
    last_error: Optional[str]
    error_count: int
    created_at: datetime
    updated_at: datetime
    
    # Computed fields
    is_token_expired: Optional[bool] = None
    needs_refresh: Optional[bool] = None
    
    class Config:
        from_attributes = True


class TimeBlockCommand(BaseModel):
    """Schema for time block commands (start, complete, reschedule)."""
    
    command: str = Field(
        ...,
        description="Command to execute: start, complete, cancel, reschedule"
    )
    
    notes: Optional[str] = Field(
        None,
        description="Optional notes about the command"
    )
    
    new_start_time: Optional[datetime] = Field(
        None,
        description="New start time (for reschedule command)"
    )
    
    new_end_time: Optional[datetime] = Field(
        None,
        description="New end time (for reschedule command)"
    )
    
    @validator('command')
    def validate_command(cls, v):
        """Validate time block command."""
        allowed_commands = ['start', 'complete', 'cancel', 'reschedule']
        if v not in allowed_commands:
            raise ValueError(f'Command must be one of: {allowed_commands}')
        return v


class WeeklySchedule(BaseModel):
    """Schema for weekly schedule view."""
    
    week_start: datetime
    week_end: datetime
    time_blocks: List[TimeBlockResponse]
    total_scheduled_hours: float
    energy_distribution: Dict[str, int]
    block_type_distribution: Dict[str, int]
    conflicts: List[Dict[str, Any]]
    suggestions: List[str]


class SchedulingConflict(BaseModel):
    """Schema for scheduling conflicts."""
    
    conflict_type: str
    description: str
    affected_blocks: List[UUID]
    severity: str  # low, medium, high
    suggested_resolution: Optional[str]
