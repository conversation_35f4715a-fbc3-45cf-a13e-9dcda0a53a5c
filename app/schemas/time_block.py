"""
Time blocking schemas for Project Chronos.

This module defines Pydantic schemas for ADHD-optimized time blocking,
scheduling, and visual time management.
"""

from datetime import datetime, time
from typing import Any, Dict, List, Optional
from uuid import UUID

from pydantic import BaseModel, Field, ConfigDict, validator

from app.models.time_block import TimeBlockType, TimeBlockStatus


class TimeBlockBase(BaseModel):
    """Base schema for time blocks."""
    
    title: str = Field(max_length=200, description="Time block title")
    description: Optional[str] = Field(None, description="Block description")
    block_type: TimeBlockType = Field(
        default=TimeBlockType.TASK,
        description="Type of time block"
    )
    start_time: datetime = Field(description="Block start time")
    end_time: datetime = Field(description="Block end time")
    task_id: Optional[UUID] = Field(None, description="Associated task ID")
    is_flexible: bool = Field(
        default=False,
        description="Whether block can be moved automatically"
    )
    buffer_before: int = Field(
        default=0, ge=0, le=60,
        description="Buffer time before block in minutes"
    )
    buffer_after: int = Field(
        default=0, ge=0, le=60,
        description="Buffer time after block in minutes"
    )
    energy_level_required: Optional[str] = Field(
        None,
        description="Required energy level (low, medium, high)"
    )
    color: Optional[str] = Field(
        None, regex=r"^#[0-9A-Fa-f]{6}$",
        description="Hex color code for visual representation"
    )
    category: Optional[str] = Field(None, max_length=50, description="Block category")


class TimeBlockCreate(TimeBlockBase):
    """Schema for creating a time block."""
    
    @validator('end_time')
    def validate_end_after_start(cls, v, values):
        start_time = values.get('start_time')
        if start_time and v <= start_time:
            raise ValueError('End time must be after start time')
        return v
    
    @validator('energy_level_required')
    def validate_energy_level(cls, v):
        if v and v not in ['low', 'medium', 'high']:
            raise ValueError('Energy level must be low, medium, or high')
        return v


class TimeBlockUpdate(BaseModel):
    """Schema for updating a time block."""
    
    title: Optional[str] = Field(None, max_length=200)
    description: Optional[str] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    is_flexible: Optional[bool] = None
    buffer_before: Optional[int] = Field(None, ge=0, le=60)
    buffer_after: Optional[int] = Field(None, ge=0, le=60)
    energy_level_required: Optional[str] = None
    color: Optional[str] = Field(None, regex=r"^#[0-9A-Fa-f]{6}$")
    category: Optional[str] = Field(None, max_length=50)
    status: Optional[TimeBlockStatus] = None
    completion_percentage: Optional[float] = Field(None, ge=0.0, le=1.0)


class TimeBlockResponse(TimeBlockBase):
    """Schema for time block responses."""
    
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID
    user_id: UUID
    duration_minutes: int
    status: TimeBlockStatus
    actual_start_time: Optional[datetime] = None
    actual_end_time: Optional[datetime] = None
    completion_percentage: Optional[float] = None
    external_calendar_id: Optional[str] = None
    calendar_source: Optional[str] = None
    sync_status: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    
    # Calculated fields
    total_duration_with_buffers: Optional[int] = None
    is_in_past: Optional[bool] = None
    is_active_now: Optional[bool] = None
    visual_position: Optional[Dict[str, float]] = None


class TimeBlockMove(BaseModel):
    """Schema for moving a time block."""
    
    new_start_time: datetime = Field(description="New start time for the block")
    maintain_duration: bool = Field(
        default=True,
        description="Whether to maintain the original duration"
    )
    new_duration: Optional[int] = Field(
        None, ge=1, le=1440,
        description="New duration in minutes (if not maintaining original)"
    )


class ScheduleTemplateBase(BaseModel):
    """Base schema for schedule templates."""
    
    name: str = Field(max_length=100, description="Template name")
    description: Optional[str] = Field(None, description="Template description")
    template_type: str = Field(
        default="daily",
        description="Template type (daily, weekly, custom)"
    )
    time_blocks_template: List[Dict[str, Any]] = Field(
        default_factory=list,
        description="Template time block definitions"
    )
    settings: Dict[str, Any] = Field(
        default_factory=dict,
        description="Template settings and preferences"
    )


class ScheduleTemplateCreate(ScheduleTemplateBase):
    """Schema for creating a schedule template."""
    pass


class ScheduleTemplateResponse(ScheduleTemplateBase):
    """Schema for schedule template responses."""
    
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID
    user_id: UUID
    is_default: bool = False
    usage_count: int = 0
    last_used: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime


class SchedulingPreferencesBase(BaseModel):
    """Base schema for scheduling preferences."""
    
    work_start_time: Optional[str] = Field(
        "09:00", regex=r"^([01]?[0-9]|2[0-3]):[0-5][0-9]$",
        description="Preferred work start time (HH:MM)"
    )
    work_end_time: Optional[str] = Field(
        "17:00", regex=r"^([01]?[0-9]|2[0-3]):[0-5][0-9]$",
        description="Preferred work end time (HH:MM)"
    )
    default_buffer_before: int = Field(
        default=5, ge=0, le=60,
        description="Default buffer time before events (minutes)"
    )
    default_buffer_after: int = Field(
        default=5, ge=0, le=60,
        description="Default buffer time after events (minutes)"
    )
    meeting_buffer_before: int = Field(
        default=10, ge=0, le=60,
        description="Buffer time before meetings (minutes)"
    )
    meeting_buffer_after: int = Field(
        default=10, ge=0, le=60,
        description="Buffer time after meetings (minutes)"
    )
    peak_energy_times: List[str] = Field(
        default_factory=list,
        description="Time ranges when user has peak energy"
    )
    low_energy_times: List[str] = Field(
        default_factory=list,
        description="Time ranges when user has low energy"
    )
    preferred_task_duration: int = Field(
        default=25, ge=5, le=240,
        description="Preferred task duration in minutes"
    )
    max_continuous_work: int = Field(
        default=90, ge=30, le=480,
        description="Maximum continuous work time in minutes"
    )
    preferred_view: str = Field(
        default="timeline",
        description="Preferred calendar view"
    )
    color_coding_scheme: str = Field(
        default="by_type",
        description="Color coding preference"
    )
    show_buffer_times: bool = Field(
        default=True,
        description="Whether to show buffer times visually"
    )
    auto_schedule_enabled: bool = Field(
        default=False,
        description="Enable automatic task scheduling"
    )
    auto_buffer_enabled: bool = Field(
        default=True,
        description="Enable automatic buffer time insertion"
    )
    conflict_resolution: str = Field(
        default="suggest",
        description="Conflict resolution preference"
    )
    settings: Dict[str, Any] = Field(
        default_factory=dict,
        description="Additional scheduling preferences"
    )


class SchedulingPreferencesCreate(SchedulingPreferencesBase):
    """Schema for creating scheduling preferences."""
    pass


class SchedulingPreferencesUpdate(BaseModel):
    """Schema for updating scheduling preferences."""
    
    work_start_time: Optional[str] = Field(
        None, regex=r"^([01]?[0-9]|2[0-3]):[0-5][0-9]$"
    )
    work_end_time: Optional[str] = Field(
        None, regex=r"^([01]?[0-9]|2[0-3]):[0-5][0-9]$"
    )
    default_buffer_before: Optional[int] = Field(None, ge=0, le=60)
    default_buffer_after: Optional[int] = Field(None, ge=0, le=60)
    meeting_buffer_before: Optional[int] = Field(None, ge=0, le=60)
    meeting_buffer_after: Optional[int] = Field(None, ge=0, le=60)
    peak_energy_times: Optional[List[str]] = None
    low_energy_times: Optional[List[str]] = None
    preferred_task_duration: Optional[int] = Field(None, ge=5, le=240)
    max_continuous_work: Optional[int] = Field(None, ge=30, le=480)
    preferred_view: Optional[str] = None
    color_coding_scheme: Optional[str] = None
    show_buffer_times: Optional[bool] = None
    auto_schedule_enabled: Optional[bool] = None
    auto_buffer_enabled: Optional[bool] = None
    conflict_resolution: Optional[str] = None
    settings: Optional[Dict[str, Any]] = None


class SchedulingPreferencesResponse(SchedulingPreferencesBase):
    """Schema for scheduling preferences responses."""
    
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID
    user_id: UUID
    created_at: datetime
    updated_at: datetime


class DailyScheduleRequest(BaseModel):
    """Schema for requesting daily schedule."""
    
    date: datetime = Field(description="Date for the schedule")
    view_type: str = Field(
        default="timeline",
        description="View type (timeline, circular, grid)"
    )
    include_buffers: bool = Field(
        default=True,
        description="Whether to include buffer times"
    )
    include_completed: bool = Field(
        default=True,
        description="Whether to include completed blocks"
    )


class DailyScheduleResponse(BaseModel):
    """Schema for daily schedule response."""
    
    date: datetime
    time_blocks: List[TimeBlockResponse]
    total_scheduled_time: int  # in minutes
    total_available_time: int  # in minutes
    utilization_percentage: float
    conflicts: List[Dict[str, Any]]
    suggestions: List[str]
    view_data: Dict[str, Any]  # View-specific data


class ScheduleConflict(BaseModel):
    """Schema for schedule conflicts."""
    
    conflict_type: str = Field(description="Type of conflict")
    severity: str = Field(description="Conflict severity (low, medium, high)")
    message: str = Field(description="Human-readable conflict description")
    affected_blocks: List[UUID] = Field(description="IDs of affected time blocks")
    suggestions: List[str] = Field(description="Suggested resolutions")


class ScheduleValidationResponse(BaseModel):
    """Schema for schedule validation response."""
    
    is_valid: bool
    total_scheduled_time: int
    available_time: int
    over_scheduled_by: int  # minutes over-scheduled
    conflicts: List[ScheduleConflict]
    warnings: List[str]
    suggestions: List[str]


class CircularCalendarView(BaseModel):
    """Schema for circular calendar view data."""
    
    center_x: float
    center_y: float
    radius: float
    segments: List[Dict[str, Any]]  # Circular segments for time blocks
    hour_markers: List[Dict[str, Any]]  # Hour marker positions
    current_time_indicator: Optional[Dict[str, Any]] = None


class TimelineView(BaseModel):
    """Schema for timeline view data."""
    
    hour_height: float
    total_height: float
    time_blocks: List[Dict[str, Any]]  # Positioned time blocks
    hour_lines: List[Dict[str, Any]]  # Hour grid lines
    current_time_line: Optional[Dict[str, Any]] = None


class TimeSlotSuggestion(BaseModel):
    """Schema for time slot suggestions."""
    
    start_time: datetime
    end_time: datetime
    confidence_score: float = Field(ge=0.0, le=1.0)
    reasoning: str
    energy_match: Optional[str] = None
    conflicts: List[str] = Field(default_factory=list)


class AutoScheduleRequest(BaseModel):
    """Schema for auto-scheduling requests."""
    
    date: datetime = Field(description="Date to schedule for")
    task_ids: List[UUID] = Field(description="Tasks to schedule")
    preferences: Optional[Dict[str, Any]] = Field(
        None,
        description="Scheduling preferences override"
    )
    constraints: Optional[Dict[str, Any]] = Field(
        None,
        description="Scheduling constraints"
    )


class AutoScheduleResponse(BaseModel):
    """Schema for auto-scheduling response."""
    
    scheduled_blocks: List[TimeBlockResponse]
    unscheduled_tasks: List[UUID]
    conflicts_resolved: int
    optimization_score: float
    suggestions: List[str]


class BufferTimeAnalysis(BaseModel):
    """Schema for buffer time analysis."""
    
    recommended_buffer_before: int
    recommended_buffer_after: int
    reasoning: str
    historical_data: Dict[str, Any]
    confidence_level: float
