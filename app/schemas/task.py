"""
Pydantic schemas for task management with ADHD optimizations.

This module defines request/response schemas for task operations,
AI chunking, and adaptive filtering with comprehensive validation.
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from uuid import UUID
from pydantic import BaseModel, Field, field_validator, ConfigDict


class TaskBase(BaseModel):
    """
    Base schema for task data with ADHD-optimized validation.
    
    Provides common fields and validation for task operations.
    """
    
    title: str = Field(
        ...,
        min_length=1,
        max_length=500,
        description="Task title (concise for ADHD users)"
    )
    
    description: Optional[str] = Field(
        None,
        max_length=5000,
        description="Detailed task description"
    )
    
    priority: str = Field(
        default="medium",
        description="Task priority level"
    )
    
    energy_level: str = Field(
        default="medium",
        description="Required energy level for task completion"
    )
    
    estimated_duration: Optional[int] = Field(
        None,
        ge=1,
        le=480,  # Max 8 hours
        description="Estimated duration in minutes"
    )
    
    context_tags: Optional[List[str]] = Field(
        None,
        max_length=10,
        description="Context tags for adaptive filtering"
    )
    
    due_date: Optional[datetime] = Field(
        None,
        description="Task due date"
    )
    
    @field_validator("priority")
    @classmethod
    def validate_priority(cls, v):
        """Validate priority is one of allowed values."""
        allowed = ["low", "medium", "high", "urgent"]
        if v not in allowed:
            raise ValueError(f"Priority must be one of: {allowed}")
        return v

    @field_validator("energy_level")
    @classmethod
    def validate_energy_level(cls, v):
        """Validate energy level is one of allowed values."""
        allowed = ["low", "medium", "high"]
        if v not in allowed:
            raise ValueError(f"Energy level must be one of: {allowed}")
        return v

    @field_validator("context_tags")
    @classmethod
    def validate_context_tags(cls, v):
        """Validate context tags are reasonable."""
        if v is None:
            return v

        # Remove duplicates and empty strings
        cleaned = list(set(tag.strip().lower() for tag in v if tag.strip()))

        # Validate tag format
        for tag in cleaned:
            if not tag.replace("_", "").replace("-", "").isalnum():
                raise ValueError(f"Invalid context tag: {tag}")

        return cleaned


class TaskCreate(TaskBase):
    """
    Schema for creating new tasks.
    
    Includes ADHD-specific defaults and validation for task creation.
    """
    
    parent_task_id: Optional[UUID] = Field(
        None,
        description="Parent task ID if this is a subtask"
    )


class TaskUpdate(BaseModel):
    """
    Schema for updating existing tasks.
    
    All fields are optional to support partial updates.
    """
    
    title: Optional[str] = Field(
        None,
        min_length=1,
        max_length=500
    )
    
    description: Optional[str] = Field(
        None,
        max_length=5000
    )
    
    status: Optional[str] = Field(
        None,
        description="Task status"
    )
    
    priority: Optional[str] = Field(None)
    energy_level: Optional[str] = Field(None)
    estimated_duration: Optional[int] = Field(None, ge=1, le=480)
    actual_duration: Optional[int] = Field(None, ge=1)
    context_tags: Optional[List[str]] = Field(None)
    due_date: Optional[datetime] = Field(None)
    
    @field_validator("status")
    @classmethod
    def validate_status(cls, v):
        """Validate status is one of allowed values."""
        if v is None:
            return v
        allowed = ["pending", "in_progress", "completed", "cancelled"]
        if v not in allowed:
            raise ValueError(f"Status must be one of: {allowed}")
        return v


class TaskResponse(TaskBase):
    """
    Schema for task responses with computed fields.
    
    Includes all task data plus computed properties for ADHD users.
    """
    
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID
    status: str
    actual_duration: Optional[int] = None
    is_chunked: bool = False
    chunk_size: Optional[str] = None
    parent_task_id: Optional[UUID] = None
    user_id: UUID
    
    # Timestamps
    created_at: datetime
    updated_at: datetime
    completed_at: Optional[datetime] = None
    started_at: Optional[datetime] = None
    
    # Computed properties
    is_overdue: bool = Field(description="Whether task is past due date")
    is_completed: bool = Field(description="Whether task is completed")
    urgency_score: float = Field(description="Calculated urgency score")
    
    # Relationships
    subtasks: Optional[List["TaskResponse"]] = Field(
        None,
        description="Subtasks if this task has been chunked"
    )


class TaskChunkRequest(BaseModel):
    """
    Schema for AI task chunking requests.
    
    Specifies parameters for breaking down tasks using AI.
    """
    
    chunk_size: str = Field(
        default="small",
        description="Desired chunk size for AI breakdown"
    )
    
    context: Optional[str] = Field(
        None,
        max_length=1000,
        description="Additional context for better chunking"
    )
    
    user_preferences: Optional[Dict[str, Any]] = Field(
        None,
        description="User-specific preferences for chunking"
    )
    
    @field_validator("chunk_size")
    @classmethod
    def validate_chunk_size(cls, v):
        """Validate chunk size is one of allowed values."""
        allowed = ["small", "medium", "large"]
        if v not in allowed:
            raise ValueError(f"Chunk size must be one of: {allowed}")
        return v


class TaskFilterRequest(BaseModel):
    """
    Schema for adaptive task filtering requests.
    
    Supports ADHD-specific filtering by energy, context, and duration.
    """
    
    energy_level: Optional[str] = Field(
        None,
        description="Filter by required energy level"
    )
    
    max_duration: Optional[int] = Field(
        None,
        ge=1,
        le=480,
        description="Maximum task duration in minutes"
    )
    
    context_tags: Optional[List[str]] = Field(
        None,
        description="Required context tags"
    )
    
    status: Optional[List[str]] = Field(
        None,
        description="Task statuses to include"
    )
    
    priority: Optional[List[str]] = Field(
        None,
        description="Priority levels to include"
    )
    
    include_overdue: bool = Field(
        default=True,
        description="Whether to include overdue tasks"
    )


class TaskJarRequest(BaseModel):
    """
    Schema for task jar (random selection) requests.
    
    Helps reduce decision fatigue for ADHD users.
    """
    
    jar_size: int = Field(
        default=5,
        ge=1,
        le=10,
        description="Number of tasks to randomly select"
    )
    
    filters: Optional[TaskFilterRequest] = Field(
        None,
        description="Filters to apply before random selection"
    )
    
    exclude_recent: bool = Field(
        default=True,
        description="Exclude recently completed or skipped tasks"
    )


class TaskStatsResponse(BaseModel):
    """
    Schema for task statistics and insights.
    
    Provides ADHD-relevant metrics and patterns.
    """
    
    total_tasks: int
    completed_tasks: int
    pending_tasks: int
    overdue_tasks: int
    
    completion_rate: float = Field(description="Percentage of completed tasks")
    average_duration_accuracy: Optional[float] = Field(
        None,
        description="How accurate time estimates are"
    )
    
    energy_level_distribution: Dict[str, int] = Field(
        description="Distribution of tasks by energy level"
    )
    
    context_tag_usage: Dict[str, int] = Field(
        description="Most used context tags"
    )
    
    productivity_patterns: Dict[str, Any] = Field(
        description="Patterns in task completion"
    )


# Update forward references
TaskResponse.model_rebuild()
