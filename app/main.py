<<<<<<< HEAD
"""
Main FastAPI application for Project Chronos - Agent 3.

This module sets up the ADHD-optimized task management system with
AI chunking, adaptive filtering, and decision fatigue reduction features.
"""

import logging
from contextlib import asynccontextmanager
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from app.core.config import settings
from app.core.database import init_db, close_db
from app.core.exceptions import ChronosException
from app.api.v1 import api_router
from app.api.v1.websocket import register_websocket_routes
from app.api.v1 import auth
# Additional routers for Agents 4-6
try:
    from app.api.v1.body_doubling import router as body_doubling_router
    from app.api.v1.focus import router as focus_router
    from app.api.v1.time_blocks import router as time_blocking_router
    from app.api.websockets.body_doubling import websocket_endpoint as body_doubling_ws
    from app.api.websockets.focus import handle_focus_websocket
    HAS_AGENTS_456 = True
except ImportError:
    HAS_AGENTS_456 = False


# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Application lifespan manager.
    
    Handles startup and shutdown events for the ADHD task management system.
    """
    # Startup
    logger.info("Starting Project Chronos - Task Management & AI Chunking Agent")
    
    try:
        # Initialize database
        await init_db()
        logger.info("Database initialized successfully")
        
        # Log configuration
        logger.info(f"AI Services configured: "
                   f"OpenAI={'✓' if settings.openai_api_key else '✗'}, "
                   f"Anthropic={'✓' if settings.anthropic_api_key else '✗'}")
        
        yield
        
    except Exception as e:
        logger.error(f"Startup failed: {e}")
        raise
    
    finally:
        # Shutdown
        logger.info("Shutting down Project Chronos")
        await close_db()
        logger.info("Database connections closed")


# Create FastAPI application
app = FastAPI(
    title="Project Chronos - ADHD-Optimized Productivity Platform",
    description="""
    Comprehensive ADHD-optimized productivity platform with task management,
    gamification, motivation, and real-time collaboration features.

    ## Core Features

    * **Task Management**: CRUD operations with ADHD-specific optimizations
    * **AI Chunking**: Break down overwhelming tasks using OpenAI/Anthropic
    * **Gamification**: Points, achievements, and dopamine-driven motivation
    * **Body Doubling**: Virtual co-working sessions for accountability
    * **Real-time Collaboration**: WebSocket-based live updates and communication
    * **Motivation System**: Personalized dopamine menu and activity tracking

    ## ADHD-Specific Features

    * Energy level matching for task selection and activities
    * Flexible streak systems that accommodate ADHD patterns
    * Immediate dopamine rewards for task initiation and completion
    * Virtual body doubling for social accountability
    * Context-aware recommendations and adaptive filtering
    * Executive function support and decision fatigue reduction
    """,
    version=settings.app_version,
    lifespan=lifespan,
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Global exception handler for Chronos exceptions
@app.exception_handler(ChronosException)
async def chronos_exception_handler(request, exc: ChronosException):
    """
    Handle custom Chronos exceptions.
    
    Provides consistent error responses for ADHD-specific exceptions.
    """
    status_code = 400
    
    # Map exception types to HTTP status codes
    if "NOT_FOUND" in exc.error_code:
        status_code = 404
    elif "ACCESS_DENIED" in exc.error_code:
        status_code = 403
    elif "AI_SERVICE" in exc.error_code:
        status_code = 503
    elif "AUTHENTICATION" in exc.error_code:
        status_code = 401
    
    return JSONResponse(
        status_code=status_code,
        content={
            "error": exc.error_code,
            "message": exc.message,
            "details": exc.details
        }
    )


# Include routers
app.include_router(api_router, prefix="/api/v1")

# Include authentication router
app.include_router(
    auth.router,
    prefix="/api/v1/auth",
    tags=["authentication"],
)

# Register WebSocket routes
register_websocket_routes(app)

# Include additional routers for Agents 4-6 if available
if HAS_AGENTS_456:
    app.include_router(body_doubling_router, prefix="/api/v1/body-doubling", tags=["body-doubling"])
    app.include_router(focus_router, prefix="/api/v1", tags=["focus"])
    app.include_router(time_blocking_router, prefix="/api/v1", tags=["time-blocking"])

    # Additional WebSocket endpoints
    @app.websocket("/ws/body-doubling/{session_id}")
    async def websocket_body_doubling(websocket, session_id: str, token: str = None):
        """WebSocket endpoint for body doubling sessions."""
        await body_doubling_ws(websocket, session_id, token)

    @app.websocket("/ws/focus/{session_id}")
    async def websocket_focus(websocket, session_id: str):
        """WebSocket endpoint for focus sessions."""
        await handle_focus_websocket(websocket, session_id)


@app.get("/")
async def root():
    """
    Root endpoint with system information.
    
    Provides basic information about the ADHD task management system.
    """
    return {
        "message": "Project Chronos - Task Management & AI Chunking Agent",
        "version": settings.app_version,
        "features": [
            "ADHD-optimized task management",
            "AI-powered task chunking",
            "Adaptive filtering by energy level",
            "Context-aware task selection",
            "Decision fatigue reduction (task jar)",
            "Smart prioritization and urgency scoring"
        ],
        "ai_services": {
            "openai": bool(settings.openai_api_key),
            "anthropic": bool(settings.anthropic_api_key)
        }
    }


@app.get("/health")
async def health_check():
    """
    Health check endpoint.
    
    Provides system health status for monitoring and deployment.
    """
    return {
        "status": "healthy",
        "service": "task-management-ai-chunking",
        "version": settings.app_version,
        "timestamp": "2024-01-01T00:00:00Z"  # Will be replaced with actual timestamp
    }


@app.get("/api/v1/features")
async def get_features():
    """
    Get available features and their status.
    
    Returns information about ADHD-specific features and AI service availability.
    """
    return {
        "task_management": {
            "available": True,
            "features": [
                "CRUD operations",
                "Energy level tracking",
                "Context tags",
                "Soft delete",
                "Time estimation"
            ]
        },
        "ai_chunking": {
            "available": bool(settings.openai_api_key or settings.anthropic_api_key),
            "providers": {
                "openai": bool(settings.openai_api_key),
                "anthropic": bool(settings.anthropic_api_key)
            },
            "chunk_sizes": ["small", "medium", "large"]
        },
        "adaptive_filtering": {
            "available": True,
            "filters": [
                "energy_level",
                "max_duration",
                "context_tags",
                "priority",
                "status"
            ]
        },
        "task_jar": {
            "available": True,
            "description": "Random task selection for decision fatigue reduction",
            "max_size": settings.max_task_jar_size
        }
    }


if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.debug,
        log_level="info"
    )
=======
"""Main FastAPI application for Project Chronos.

This module creates and configures the FastAPI application with ADHD-friendly
features, security middleware, and API routing.
"""

from contextlib import asynccontextmanager
from typing import AsyncGenerator

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware

from app.api.v1 import auth
from app.core.config import settings
from app.core.database import close_db, init_db


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """Application lifespan manager.
    
    Handles startup and shutdown events for the FastAPI application.
    
    Args:
        app: FastAPI application instance
        
    Yields:
        None during application runtime
    """
    # Startup
    if settings.DEBUG:
        await init_db()  # Initialize database in development
    
    yield
    
    # Shutdown
    await close_db()


def create_application() -> FastAPI:
    """Create and configure FastAPI application.
    
    Returns:
        Configured FastAPI application instance
    """
    app = FastAPI(
        title=settings.PROJECT_NAME,
        description=settings.PROJECT_DESCRIPTION,
        version=settings.VERSION,
        openapi_url=f"{settings.API_V1_STR}/openapi.json",
        docs_url="/docs",
        redoc_url="/redoc",
        lifespan=lifespan,
    )
    
    # Add security headers middleware
    @app.middleware("http")
    async def add_security_headers(request, call_next):
        """Add security headers to all responses."""
        response = await call_next(request)
        
        for header, value in settings.SECURITY_HEADERS.items():
            response.headers[header] = value
        
        return response
    
    # Add CORS middleware
    if settings.BACKEND_CORS_ORIGINS:
        app.add_middleware(
            CORSMiddleware,
            allow_origins=[str(origin) for origin in settings.BACKEND_CORS_ORIGINS],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
    
    # Add trusted host middleware for production (disabled for now)
    # if not settings.DEBUG and not settings.TESTING:
    #     app.add_middleware(
    #         TrustedHostMiddleware,
    #         allowed_hosts=["localhost", "127.0.0.1", "*.chronos.app"]
    #     )
    
    # Include API routers
    app.include_router(
        auth.router,
        prefix=f"{settings.API_V1_STR}/auth",
        tags=["authentication"],
    )
    
    @app.get("/")
    async def root():
        """Root endpoint with basic application information."""
        return {
            "message": "Welcome to Project Chronos",
            "description": "Neuro-affirming digital planner for ADHD users",
            "version": settings.VERSION,
            "docs": "/docs",
        }
    
    @app.get("/health")
    async def health_check():
        """Health check endpoint for monitoring."""
        return {"status": "healthy", "service": "project-chronos"}
    
    return app


# Create application instance
app = create_application()
>>>>>>> origin/agent2-authentication
