"""
WebSocket authentication middleware for Project Chronos.

This module provides JWT-based authentication for WebSocket connections,
ensuring secure real-time communication for body doubling and focus sessions.
"""

import logging
from typing import Optional
from uuid import UUID

from fastapi import WebSocket, WebSocketException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.core.database import get_db
from app.core.security import security
from app.models.user import User

logger = logging.getLogger(__name__)


class WebSocketAuthenticationError(Exception):
    """Exception raised when WebSocket authentication fails."""
    
    def __init__(self, message: str, code: int = 4001):
        self.message = message
        self.code = code
        super().__init__(self.message)


async def authenticate_websocket_user(
    websocket: WebSocket,
    token: Optional[str] = None
) -> User:
    """
    Authenticate a WebSocket connection using JWT token.
    
    Args:
        websocket: WebSocket connection
        token: JWT token from query parameters or headers
        
    Returns:
        Authenticated user
        
    Raises:
        WebSocketAuthenticationError: If authentication fails
    """
    # Extract token from query parameters if not provided
    if not token:
        token = websocket.query_params.get("token")
    
    # Check for token in headers as fallback
    if not token:
        auth_header = websocket.headers.get("authorization")
        if auth_header and auth_header.startswith("Bearer "):
            token = auth_header[7:]  # Remove "Bearer " prefix
    
    if not token:
        raise WebSocketAuthenticationError(
            "Authentication token required",
            code=4001
        )
    
    # Verify JWT token
    try:
        payload = security.verify_token(token, "access")
        if not payload:
            raise WebSocketAuthenticationError(
                "Invalid or expired token",
                code=4001
            )
    except Exception as e:
        logger.warning(f"WebSocket token verification failed: {e}")
        raise WebSocketAuthenticationError(
            "Invalid token format",
            code=4001
        )
    
    # Extract user ID from token
    user_id_str = payload.get("sub")
    if not user_id_str:
        raise WebSocketAuthenticationError(
            "Invalid token payload",
            code=4001
        )
    
    try:
        user_id = UUID(user_id_str)
    except ValueError:
        raise WebSocketAuthenticationError(
            "Invalid user ID in token",
            code=4001
        )
    
    # Get user from database
    async for db in get_db():
        try:
            result = await db.execute(
                select(User).where(User.id == user_id)
            )
            user = result.scalar_one_or_none()
            
            if not user:
                raise WebSocketAuthenticationError(
                    "User not found",
                    code=4001
                )
            
            if not user.is_active:
                raise WebSocketAuthenticationError(
                    "User account is inactive",
                    code=4001
                )
            
            return user
            
        except WebSocketAuthenticationError:
            raise
        except Exception as e:
            logger.error(f"Database error during WebSocket authentication: {e}")
            raise WebSocketAuthenticationError(
                "Authentication service error",
                code=4000
            )
        finally:
            await db.close()


async def websocket_auth_dependency(websocket: WebSocket) -> User:
    """
    FastAPI dependency for WebSocket authentication.
    
    Args:
        websocket: WebSocket connection
        
    Returns:
        Authenticated user
        
    Raises:
        WebSocketException: If authentication fails
    """
    try:
        user = await authenticate_websocket_user(websocket)
        return user
    except WebSocketAuthenticationError as e:
        logger.warning(f"WebSocket authentication failed: {e.message}")
        await websocket.close(code=e.code, reason=e.message)
        raise WebSocketException(code=e.code, reason=e.message)


class WebSocketAuthMiddleware:
    """
    Middleware class for WebSocket authentication.
    
    Provides reusable authentication logic for WebSocket endpoints
    with proper error handling and logging.
    """
    
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    async def authenticate(self, websocket: WebSocket, token: Optional[str] = None) -> Optional[User]:
        """
        Authenticate WebSocket connection.
        
        Args:
            websocket: WebSocket connection
            token: Optional JWT token
            
        Returns:
            Authenticated user or None if authentication fails
        """
        try:
            user = await authenticate_websocket_user(websocket, token)
            self.logger.info(f"WebSocket authenticated for user {user.id}")
            return user
        except WebSocketAuthenticationError as e:
            self.logger.warning(f"WebSocket authentication failed: {e.message}")
            await websocket.close(code=e.code, reason=e.message)
            return None
    
    async def require_authentication(self, websocket: WebSocket, token: Optional[str] = None) -> User:
        """
        Require authentication for WebSocket connection.
        
        Args:
            websocket: WebSocket connection
            token: Optional JWT token
            
        Returns:
            Authenticated user
            
        Raises:
            WebSocketException: If authentication fails
        """
        user = await self.authenticate(websocket, token)
        if not user:
            raise WebSocketException(
                code=4001,
                reason="Authentication required"
            )
        return user
    
    async def validate_session_access(
        self, 
        user: User, 
        session_id: UUID, 
        db: AsyncSession
    ) -> bool:
        """
        Validate if user has access to a specific session.
        
        Args:
            user: Authenticated user
            session_id: Session ID to validate access for
            db: Database session
            
        Returns:
            True if user has access, False otherwise
        """
        try:
            # Import here to avoid circular imports
            from app.models.body_doubling import BodyDoublingSession, SessionParticipant
            
            # Check if user is host
            result = await db.execute(
                select(BodyDoublingSession).where(
                    BodyDoublingSession.id == session_id,
                    BodyDoublingSession.host_user_id == user.id
                )
            )
            if result.scalar_one_or_none():
                return True
            
            # Check if user is participant
            result = await db.execute(
                select(SessionParticipant).where(
                    SessionParticipant.session_id == session_id,
                    SessionParticipant.user_id == user.id,
                    SessionParticipant.status != "left"
                )
            )
            if result.scalar_one_or_none():
                return True
            
            # Check if session is public and user can join
            result = await db.execute(
                select(BodyDoublingSession).where(
                    BodyDoublingSession.id == session_id,
                    BodyDoublingSession.is_public == True,
                    BodyDoublingSession.status.in_(["waiting", "active"])
                )
            )
            session = result.scalar_one_or_none()
            if session and session.can_join():
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error validating session access: {e}")
            return False


# Global middleware instance
websocket_auth = WebSocketAuthMiddleware()


async def get_websocket_auth() -> WebSocketAuthMiddleware:
    """Get the global WebSocket authentication middleware instance."""
    return websocket_auth
