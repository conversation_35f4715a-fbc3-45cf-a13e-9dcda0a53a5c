"""
Google Calendar integration for Project Chronos.

This module provides integration with Google Calendar API for
bidirectional synchronization of calendar events and time blocks.
"""

import logging
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional
from urllib.parse import urlencode

from app.integrations.base import BaseIntegration, SyncResult, IntegrationError
from app.schemas.integration import OAuthTokens, CalendarEventSync
from app.core.config import settings

logger = logging.getLogger(__name__)


class GoogleCalendarIntegration(BaseIntegration):
    """Google Calendar API integration."""
    
    def __init__(self):
        super().__init__()
        self.base_url = "https://www.googleapis.com/calendar/v3"
        self.oauth_url = "https://oauth2.googleapis.com/token"
        self.auth_url = "https://accounts.google.com/o/oauth2/v2/auth"
        self.scopes = [
            "https://www.googleapis.com/auth/calendar",
            "https://www.googleapis.com/auth/calendar.events"
        ]
    
    async def authenticate(self, credentials: Dict[str, Any]) -> OAuthTokens:
        """
        Exchange OAuth authorization code for access tokens.
        
        Args:
            credentials: Dict containing 'code', 'redirect_uri', and optionally 'state'
            
        Returns:
            OAuth tokens for API access
        """
        
        token_data = {
            "client_id": settings.GOOGLE_CLIENT_ID,
            "client_secret": settings.GOOGLE_CLIENT_SECRET,
            "code": credentials["code"],
            "grant_type": "authorization_code",
            "redirect_uri": credentials["redirect_uri"]
        }
        
        try:
            response = await self._make_request(
                method="POST",
                url=self.oauth_url,
                json_data=token_data
            )
            
            return OAuthTokens(
                access_token=response["access_token"],
                refresh_token=response.get("refresh_token"),
                token_type=response.get("token_type", "Bearer"),
                expires_in=response.get("expires_in"),
                scope=response.get("scope")
            )
            
        except Exception as e:
            logger.error(f"Google Calendar authentication failed: {e}")
            raise IntegrationError(f"Authentication failed: {e}")
    
    async def refresh_token(self, refresh_token: str) -> OAuthTokens:
        """
        Refresh OAuth access token using refresh token.
        
        Args:
            refresh_token: OAuth refresh token
            
        Returns:
            New OAuth tokens
        """
        
        token_data = {
            "client_id": settings.GOOGLE_CLIENT_ID,
            "client_secret": settings.GOOGLE_CLIENT_SECRET,
            "refresh_token": refresh_token,
            "grant_type": "refresh_token"
        }
        
        try:
            response = await self._make_request(
                method="POST",
                url=self.oauth_url,
                json_data=token_data
            )
            
            return OAuthTokens(
                access_token=response["access_token"],
                refresh_token=refresh_token,  # Keep existing refresh token
                token_type=response.get("token_type", "Bearer"),
                expires_in=response.get("expires_in"),
                scope=response.get("scope")
            )
            
        except Exception as e:
            logger.error(f"Google Calendar token refresh failed: {e}")
            raise IntegrationError(f"Token refresh failed: {e}")
    
    async def test_connection(self, access_token: str) -> bool:
        """
        Test connection to Google Calendar API.
        
        Args:
            access_token: OAuth access token
            
        Returns:
            True if connection is successful
        """
        
        try:
            headers = self._add_auth_header({}, access_token)
            
            await self._make_request(
                method="GET",
                url=f"{self.base_url}/users/me/calendarList",
                headers=headers,
                params={"maxResults": 1}
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Google Calendar connection test failed: {e}")
            return False
    
    async def get_calendars(self, access_token: str) -> List[Dict[str, Any]]:
        """
        Get list of user's calendars.
        
        Args:
            access_token: OAuth access token
            
        Returns:
            List of calendar information
        """
        
        try:
            headers = self._add_auth_header({}, access_token)
            
            response = await self._make_request(
                method="GET",
                url=f"{self.base_url}/users/me/calendarList",
                headers=headers
            )
            
            calendars = []
            for item in response.get("items", []):
                calendars.append({
                    "id": item["id"],
                    "name": item["summary"],
                    "description": item.get("description", ""),
                    "primary": item.get("primary", False),
                    "access_role": item.get("accessRole", "reader"),
                    "color": item.get("backgroundColor", "#3174ad")
                })
            
            return calendars
            
        except Exception as e:
            logger.error(f"Failed to get Google calendars: {e}")
            raise IntegrationError(f"Failed to get calendars: {e}")
    
    async def import_calendar_events(
        self,
        access_token: str,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        calendar_ids: Optional[List[str]] = None
    ) -> SyncResult:
        """
        Import calendar events from Google Calendar.
        
        Args:
            access_token: OAuth access token
            start_date: Start date for event import (defaults to today)
            end_date: End date for event import (defaults to 30 days from start)
            calendar_ids: Specific calendar IDs to import from (defaults to primary)
            
        Returns:
            Sync result with imported events
        """
        
        if not start_date:
            start_date = datetime.utcnow().replace(hour=0, minute=0, second=0, microsecond=0)
        
        if not end_date:
            end_date = start_date + timedelta(days=30)
        
        try:
            headers = self._add_auth_header({}, access_token)
            
            # Get calendars to import from
            if not calendar_ids:
                calendars = await self.get_calendars(access_token)
                calendar_ids = [cal["id"] for cal in calendars if cal.get("primary", False)]
                
                if not calendar_ids:
                    calendar_ids = [cal["id"] for cal in calendars[:1]]  # Use first calendar
            
            all_events = []
            errors = []
            
            for calendar_id in calendar_ids:
                try:
                    events = await self._get_events_for_calendar(
                        access_token, calendar_id, start_date, end_date
                    )
                    all_events.extend(events)
                    
                except Exception as e:
                    logger.error(f"Failed to import from calendar {calendar_id}: {e}")
                    errors.append(f"Calendar {calendar_id}: {e}")
            
            # Convert to CalendarEventSync format
            converted_events = []
            for event in all_events:
                try:
                    converted_event = self._convert_google_event(event)
                    if converted_event:
                        converted_events.append(converted_event)
                except Exception as e:
                    logger.error(f"Failed to convert event {event.get('id')}: {e}")
                    errors.append(f"Event conversion error: {e}")
            
            return SyncResult(
                success=len(errors) == 0,
                items_processed=len(all_events),
                items_created=len(converted_events),
                errors=errors,
                metadata={
                    "calendar_ids": calendar_ids,
                    "date_range": {
                        "start": start_date.isoformat(),
                        "end": end_date.isoformat()
                    },
                    "events": converted_events
                }
            )
            
        except Exception as e:
            logger.error(f"Google Calendar import failed: {e}")
            return SyncResult(
                success=False,
                errors=[f"Import failed: {e}"]
            )
    
    async def export_calendar_events(
        self,
        access_token: str,
        events: List[CalendarEventSync],
        calendar_id: Optional[str] = None
    ) -> SyncResult:
        """
        Export calendar events to Google Calendar.
        
        Args:
            access_token: OAuth access token
            events: Events to export
            calendar_id: Target calendar ID (defaults to primary)
            
        Returns:
            Sync result with export status
        """
        
        try:
            headers = self._add_auth_header({}, access_token)
            
            # Get target calendar
            if not calendar_id:
                calendars = await self.get_calendars(access_token)
                primary_calendars = [cal for cal in calendars if cal.get("primary", False)]
                if primary_calendars:
                    calendar_id = primary_calendars[0]["id"]
                else:
                    calendar_id = "primary"
            
            created_count = 0
            updated_count = 0
            errors = []
            
            for event in events:
                try:
                    google_event = self._convert_to_google_event(event)
                    
                    if event.external_id:
                        # Update existing event
                        await self._make_request(
                            method="PUT",
                            url=f"{self.base_url}/calendars/{calendar_id}/events/{event.external_id}",
                            headers=headers,
                            json_data=google_event
                        )
                        updated_count += 1
                    else:
                        # Create new event
                        response = await self._make_request(
                            method="POST",
                            url=f"{self.base_url}/calendars/{calendar_id}/events",
                            headers=headers,
                            json_data=google_event
                        )
                        created_count += 1
                        
                        # Store the Google event ID for future updates
                        event.external_id = response["id"]
                        
                except Exception as e:
                    logger.error(f"Failed to export event {event.title}: {e}")
                    errors.append(f"Event '{event.title}': {e}")
            
            return SyncResult(
                success=len(errors) == 0,
                items_processed=len(events),
                items_created=created_count,
                items_updated=updated_count,
                errors=errors,
                metadata={
                    "calendar_id": calendar_id,
                    "exported_events": [
                        {"title": event.title, "external_id": event.external_id}
                        for event in events
                    ]
                }
            )
            
        except Exception as e:
            logger.error(f"Google Calendar export failed: {e}")
            return SyncResult(
                success=False,
                errors=[f"Export failed: {e}"]
            )
    
    async def setup_webhook(
        self,
        access_token: str,
        webhook_url: str,
        events: List[str]
    ) -> str:
        """
        Set up webhook for Google Calendar notifications.
        
        Args:
            access_token: OAuth access token
            webhook_url: URL to receive webhook notifications
            events: List of events to subscribe to (not used for Google Calendar)
            
        Returns:
            Webhook ID for management
        """
        
        try:
            headers = self._add_auth_header({}, access_token)
            
            # Google Calendar uses "watch" requests for webhooks
            watch_data = {
                "id": f"chronos-{datetime.utcnow().timestamp()}",
                "type": "web_hook",
                "address": webhook_url,
                "params": {
                    "ttl": "3600"  # 1 hour TTL
                }
            }
            
            response = await self._make_request(
                method="POST",
                url=f"{self.base_url}/calendars/primary/events/watch",
                headers=headers,
                json_data=watch_data
            )
            
            return response["id"]
            
        except Exception as e:
            logger.error(f"Google Calendar webhook setup failed: {e}")
            raise IntegrationError(f"Webhook setup failed: {e}")
    
    # Private helper methods
    
    async def _get_events_for_calendar(
        self,
        access_token: str,
        calendar_id: str,
        start_date: datetime,
        end_date: datetime
    ) -> List[Dict[str, Any]]:
        """Get events for a specific calendar."""
        
        headers = self._add_auth_header({}, access_token)
        
        params = {
            "timeMin": self._format_datetime(start_date),
            "timeMax": self._format_datetime(end_date),
            "singleEvents": "true",
            "orderBy": "startTime",
            "maxResults": 2500
        }
        
        response = await self._make_request(
            method="GET",
            url=f"{self.base_url}/calendars/{calendar_id}/events",
            headers=headers,
            params=params
        )
        
        return response.get("items", [])
    
    def _convert_google_event(self, google_event: Dict[str, Any]) -> Optional[CalendarEventSync]:
        """Convert Google Calendar event to CalendarEventSync."""
        
        try:
            # Handle different event types (all-day vs timed)
            start_info = google_event.get("start", {})
            end_info = google_event.get("end", {})
            
            if "date" in start_info:
                # All-day event
                start_time = self._parse_datetime(start_info["date"] + "T00:00:00Z")
                end_time = self._parse_datetime(end_info["date"] + "T23:59:59Z")
                all_day = True
            else:
                # Timed event
                start_time = self._parse_datetime(start_info.get("dateTime"))
                end_time = self._parse_datetime(end_info.get("dateTime"))
                all_day = False
            
            if not start_time or not end_time:
                logger.warning(f"Could not parse times for event {google_event.get('id')}")
                return None
            
            # Extract attendees
            attendees = []
            for attendee in google_event.get("attendees", []):
                if attendee.get("email"):
                    attendees.append(attendee["email"])
            
            # ADHD-specific field mapping
            energy_level = "medium"  # Default
            is_focus_time = False
            
            # Check event title/description for ADHD keywords
            title = google_event.get("summary", "")
            description = google_event.get("description", "")
            
            if any(keyword in title.lower() for keyword in ["focus", "deep work", "concentrate"]):
                is_focus_time = True
                energy_level = "high"
            elif any(keyword in title.lower() for keyword in ["meeting", "call", "standup"]):
                energy_level = "medium"
            elif any(keyword in title.lower() for keyword in ["break", "lunch", "rest"]):
                energy_level = "low"
            
            return CalendarEventSync(
                external_id=google_event["id"],
                title=title,
                description=description,
                start_time=start_time,
                end_time=end_time,
                all_day=all_day,
                location=google_event.get("location"),
                attendees=attendees,
                calendar_id=google_event.get("organizer", {}).get("email"),
                energy_level=energy_level,
                buffer_before=5 if not all_day else 0,  # 5 min buffer for timed events
                buffer_after=5 if not all_day else 0,
                is_focus_time=is_focus_time
            )
            
        except Exception as e:
            logger.error(f"Error converting Google event: {e}")
            return None
    
    def _convert_to_google_event(self, event: CalendarEventSync) -> Dict[str, Any]:
        """Convert CalendarEventSync to Google Calendar event format."""
        
        google_event = {
            "summary": event.title,
            "description": event.description or "",
            "location": event.location or ""
        }
        
        if event.all_day:
            google_event["start"] = {"date": event.start_time.strftime("%Y-%m-%d")}
            google_event["end"] = {"date": event.end_time.strftime("%Y-%m-%d")}
        else:
            google_event["start"] = {"dateTime": self._format_datetime(event.start_time)}
            google_event["end"] = {"dateTime": self._format_datetime(event.end_time)}
        
        # Add attendees
        if event.attendees:
            google_event["attendees"] = [
                {"email": email} for email in event.attendees
            ]
        
        # Add ADHD-specific information to description
        adhd_info = []
        if event.energy_level:
            adhd_info.append(f"Energy Level: {event.energy_level}")
        if event.is_focus_time:
            adhd_info.append("Focus Time: Yes")
        if event.buffer_before or event.buffer_after:
            adhd_info.append(f"Buffer: {event.buffer_before}min before, {event.buffer_after}min after")
        
        if adhd_info:
            description = google_event.get("description", "")
            if description:
                description += "\n\n"
            description += "ADHD Info:\n" + "\n".join(adhd_info)
            google_event["description"] = description
        
        return google_event
    
    def get_oauth_url(self, redirect_uri: str, state: Optional[str] = None) -> str:
        """Generate OAuth authorization URL."""
        
        params = {
            "client_id": settings.GOOGLE_CLIENT_ID,
            "redirect_uri": redirect_uri,
            "scope": " ".join(self.scopes),
            "response_type": "code",
            "access_type": "offline",
            "prompt": "consent"
        }
        
        if state:
            params["state"] = state
        
        return f"{self.auth_url}?{urlencode(params)}"
