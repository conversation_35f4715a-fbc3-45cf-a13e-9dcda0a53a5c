"""
Notion integration for Project Chronos.

This module provides integration with Notion API for
bidirectional synchronization of tasks and databases.
"""

import logging
from datetime import datetime
from typing import Any, Dict, List, Optional
from urllib.parse import urlencode

from app.integrations.base import BaseIntegration, SyncResult, IntegrationError
from app.schemas.integration import OAuthTokens, TaskSync
from app.core.config import settings

logger = logging.getLogger(__name__)


class NotionIntegration(BaseIntegration):
    """Notion API integration."""
    
    def __init__(self):
        super().__init__()
        self.base_url = "https://api.notion.com/v1"
        self.oauth_url = "https://api.notion.com/v1/oauth/token"
        self.auth_url = "https://api.notion.com/v1/oauth/authorize"
        self.api_version = "2022-06-28"
    
    def _get_default_headers(self) -> Dict[str, str]:
        """Get default HTTP headers for Notion API requests."""
        headers = super()._get_default_headers()
        headers["Notion-Version"] = self.api_version
        return headers
    
    async def authenticate(self, credentials: Dict[str, Any]) -> OAuthTokens:
        """
        Exchange OAuth authorization code for access tokens.
        
        Args:
            credentials: Dict containing 'code', 'redirect_uri', and optionally 'state'
            
        Returns:
            OAuth tokens for API access
        """
        
        # Notion uses Basic auth for token exchange
        import base64
        auth_string = f"{settings.NOTION_CLIENT_ID}:{settings.NOTION_CLIENT_SECRET}"
        auth_bytes = auth_string.encode('ascii')
        auth_b64 = base64.b64encode(auth_bytes).decode('ascii')
        
        headers = {
            "Authorization": f"Basic {auth_b64}",
            "Content-Type": "application/json"
        }
        
        token_data = {
            "grant_type": "authorization_code",
            "code": credentials["code"],
            "redirect_uri": credentials["redirect_uri"]
        }
        
        try:
            response = await self._make_request(
                method="POST",
                url=self.oauth_url,
                headers=headers,
                json_data=token_data
            )
            
            return OAuthTokens(
                access_token=response["access_token"],
                token_type="Bearer",
                # Notion tokens don't expire, but we'll set a long expiration
                expires_in=365 * 24 * 3600  # 1 year
            )
            
        except Exception as e:
            logger.error(f"Notion authentication failed: {e}")
            raise IntegrationError(f"Authentication failed: {e}")
    
    async def refresh_token(self, refresh_token: str) -> OAuthTokens:
        """
        Notion tokens don't expire, so this is a no-op.
        
        Args:
            refresh_token: Not used for Notion
            
        Returns:
            Original token information
        """
        
        raise IntegrationError("Notion tokens do not require refresh")
    
    async def test_connection(self, access_token: str) -> bool:
        """
        Test connection to Notion API.
        
        Args:
            access_token: OAuth access token
            
        Returns:
            True if connection is successful
        """
        
        try:
            headers = self._add_auth_header({}, access_token)
            
            await self._make_request(
                method="GET",
                url=f"{self.base_url}/users/me",
                headers=headers
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Notion connection test failed: {e}")
            return False
    
    async def get_databases(self, access_token: str) -> List[Dict[str, Any]]:
        """
        Get list of accessible databases.
        
        Args:
            access_token: OAuth access token
            
        Returns:
            List of database information
        """
        
        try:
            headers = self._add_auth_header({}, access_token)
            
            # Search for databases
            search_data = {
                "filter": {
                    "value": "database",
                    "property": "object"
                }
            }
            
            response = await self._make_request(
                method="POST",
                url=f"{self.base_url}/search",
                headers=headers,
                json_data=search_data
            )
            
            databases = []
            for db in response.get("results", []):
                databases.append({
                    "id": db["id"],
                    "title": self._extract_title(db.get("title", [])),
                    "url": db.get("url", ""),
                    "created_time": db.get("created_time"),
                    "last_edited_time": db.get("last_edited_time"),
                    "properties": list(db.get("properties", {}).keys())
                })
            
            return databases
            
        except Exception as e:
            logger.error(f"Failed to get Notion databases: {e}")
            raise IntegrationError(f"Failed to get databases: {e}")
    
    async def import_tasks(
        self,
        access_token: str,
        database_ids: Optional[List[str]] = None,
        completed: Optional[bool] = None
    ) -> SyncResult:
        """
        Import tasks from Notion databases.
        
        Args:
            access_token: OAuth access token
            database_ids: Specific database IDs to import from
            completed: Filter by completion status
            
        Returns:
            Sync result with imported tasks
        """
        
        try:
            headers = self._add_auth_header({}, access_token)
            
            # Get databases to import from
            if not database_ids:
                databases = await self.get_databases(access_token)
                database_ids = [db["id"] for db in databases]
            
            all_pages = []
            errors = []
            
            for database_id in database_ids:
                try:
                    pages = await self._get_pages_from_database(
                        access_token, database_id, completed
                    )
                    all_pages.extend(pages)
                except Exception as e:
                    logger.error(f"Failed to import from database {database_id}: {e}")
                    errors.append(f"Database {database_id}: {e}")
            
            # Convert to TaskSync format
            converted_tasks = []
            for page in all_pages:
                try:
                    converted_task = self._convert_notion_page(page)
                    if converted_task:
                        converted_tasks.append(converted_task)
                except Exception as e:
                    logger.error(f"Failed to convert page {page.get('id')}: {e}")
                    errors.append(f"Page conversion error: {e}")
            
            return SyncResult(
                success=len(errors) == 0,
                items_processed=len(all_pages),
                items_created=len(converted_tasks),
                errors=errors,
                metadata={
                    "database_ids": database_ids,
                    "completed_filter": completed,
                    "tasks": converted_tasks
                }
            )
            
        except Exception as e:
            logger.error(f"Notion import failed: {e}")
            return SyncResult(
                success=False,
                errors=[f"Import failed: {e}"]
            )
    
    async def export_tasks(
        self,
        access_token: str,
        tasks: List[TaskSync],
        database_id: Optional[str] = None
    ) -> SyncResult:
        """
        Export tasks to Notion database.
        
        Args:
            access_token: OAuth access token
            tasks: Tasks to export
            database_id: Target database ID
            
        Returns:
            Sync result with export status
        """
        
        try:
            headers = self._add_auth_header({}, access_token)
            
            # Get or create target database
            if not database_id:
                databases = await self.get_databases(access_token)
                chronos_dbs = [db for db in databases if "chronos" in db["title"].lower()]
                if chronos_dbs:
                    database_id = chronos_dbs[0]["id"]
                else:
                    # Would need to create a database, but that requires a parent page
                    raise IntegrationError("No suitable database found. Please specify database_id.")
            
            created_count = 0
            updated_count = 0
            errors = []
            
            for task in tasks:
                try:
                    notion_page = self._convert_to_notion_page(task, database_id)
                    
                    if task.external_id:
                        # Update existing page
                        await self._make_request(
                            method="PATCH",
                            url=f"{self.base_url}/pages/{task.external_id}",
                            headers=headers,
                            json_data=notion_page
                        )
                        updated_count += 1
                    else:
                        # Create new page
                        response = await self._make_request(
                            method="POST",
                            url=f"{self.base_url}/pages",
                            headers=headers,
                            json_data=notion_page
                        )
                        created_count += 1
                        
                        # Store the Notion page ID for future updates
                        task.external_id = response["id"]
                        
                except Exception as e:
                    logger.error(f"Failed to export task {task.title}: {e}")
                    errors.append(f"Task '{task.title}': {e}")
            
            return SyncResult(
                success=len(errors) == 0,
                items_processed=len(tasks),
                items_created=created_count,
                items_updated=updated_count,
                errors=errors,
                metadata={
                    "database_id": database_id,
                    "exported_tasks": [
                        {"title": task.title, "external_id": task.external_id}
                        for task in tasks
                    ]
                }
            )
            
        except Exception as e:
            logger.error(f"Notion export failed: {e}")
            return SyncResult(
                success=False,
                errors=[f"Export failed: {e}"]
            )
    
    # Private helper methods
    
    async def _get_pages_from_database(
        self,
        access_token: str,
        database_id: str,
        completed: Optional[bool] = None
    ) -> List[Dict[str, Any]]:
        """Get pages from a specific database."""
        
        headers = self._add_auth_header({}, access_token)
        
        query_data = {}
        
        # Add completion filter if specified
        if completed is not None:
            query_data["filter"] = {
                "property": "Done",  # Common checkbox property name
                "checkbox": {
                    "equals": completed
                }
            }
        
        response = await self._make_request(
            method="POST",
            url=f"{self.base_url}/databases/{database_id}/query",
            headers=headers,
            json_data=query_data
        )
        
        return response.get("results", [])
    
    def _convert_notion_page(self, notion_page: Dict[str, Any]) -> Optional[TaskSync]:
        """Convert Notion page to TaskSync."""
        
        try:
            properties = notion_page.get("properties", {})
            
            # Extract title (usually from "Name" or "Title" property)
            title = ""
            for prop_name, prop_data in properties.items():
                if prop_data.get("type") == "title":
                    title = self._extract_title(prop_data.get("title", []))
                    break
            
            if not title:
                return None
            
            # Extract other properties
            description = ""
            due_date = None
            completed = False
            priority = "medium"
            labels = []
            
            for prop_name, prop_data in properties.items():
                prop_type = prop_data.get("type")
                
                if prop_name.lower() in ["description", "notes"] and prop_type == "rich_text":
                    description = self._extract_rich_text(prop_data.get("rich_text", []))
                elif prop_name.lower() in ["due", "due_date"] and prop_type == "date":
                    date_info = prop_data.get("date")
                    if date_info and date_info.get("start"):
                        due_date = self._parse_datetime(date_info["start"])
                elif prop_name.lower() in ["done", "completed"] and prop_type == "checkbox":
                    completed = prop_data.get("checkbox", False)
                elif prop_name.lower() == "priority" and prop_type == "select":
                    select_info = prop_data.get("select")
                    if select_info:
                        priority = select_info.get("name", "medium").lower()
                elif prop_name.lower() in ["tags", "labels"] and prop_type == "multi_select":
                    for tag in prop_data.get("multi_select", []):
                        labels.append(tag.get("name", ""))
            
            # ADHD-specific field mapping
            energy_level = "medium"
            complexity = "medium"
            estimated_duration = None
            
            # Check for ADHD-related properties or content
            for prop_name, prop_data in properties.items():
                if "energy" in prop_name.lower() and prop_data.get("type") == "select":
                    select_info = prop_data.get("select")
                    if select_info:
                        energy_level = select_info.get("name", "medium").lower()
                elif "complexity" in prop_name.lower() and prop_data.get("type") == "select":
                    select_info = prop_data.get("select")
                    if select_info:
                        complexity = select_info.get("name", "medium").lower()
                elif "duration" in prop_name.lower() and prop_data.get("type") == "number":
                    estimated_duration = prop_data.get("number")
            
            return TaskSync(
                external_id=notion_page["id"],
                title=title,
                description=description,
                due_date=due_date,
                completed=completed,
                priority=priority,
                labels=labels,
                energy_level=energy_level,
                complexity=complexity,
                estimated_duration=estimated_duration
            )
            
        except Exception as e:
            logger.error(f"Error converting Notion page: {e}")
            return None
    
    def _convert_to_notion_page(self, task: TaskSync, database_id: str) -> Dict[str, Any]:
        """Convert TaskSync to Notion page format."""
        
        notion_page = {
            "parent": {"database_id": database_id},
            "properties": {
                "Name": {
                    "title": [
                        {
                            "text": {"content": task.title}
                        }
                    ]
                }
            }
        }
        
        # Add other properties
        if task.description:
            notion_page["properties"]["Description"] = {
                "rich_text": [
                    {
                        "text": {"content": task.description}
                    }
                ]
            }
        
        if task.due_date:
            notion_page["properties"]["Due Date"] = {
                "date": {
                    "start": task.due_date.isoformat()
                }
            }
        
        notion_page["properties"]["Done"] = {
            "checkbox": task.completed
        }
        
        if task.priority:
            notion_page["properties"]["Priority"] = {
                "select": {"name": task.priority.title()}
            }
        
        if task.energy_level:
            notion_page["properties"]["Energy Level"] = {
                "select": {"name": task.energy_level.title()}
            }
        
        if task.complexity:
            notion_page["properties"]["Complexity"] = {
                "select": {"name": task.complexity.title()}
            }
        
        if task.estimated_duration:
            notion_page["properties"]["Duration (min)"] = {
                "number": task.estimated_duration
            }
        
        if task.labels:
            notion_page["properties"]["Tags"] = {
                "multi_select": [
                    {"name": label} for label in task.labels
                ]
            }
        
        return notion_page
    
    def _extract_title(self, title_array: List[Dict[str, Any]]) -> str:
        """Extract title text from Notion title array."""
        if not title_array:
            return ""
        
        return "".join([item.get("text", {}).get("content", "") for item in title_array])
    
    def _extract_rich_text(self, rich_text_array: List[Dict[str, Any]]) -> str:
        """Extract text from Notion rich text array."""
        if not rich_text_array:
            return ""
        
        return "".join([item.get("text", {}).get("content", "") for item in rich_text_array])
    
    def get_oauth_url(self, redirect_uri: str, state: Optional[str] = None) -> str:
        """Generate OAuth authorization URL."""
        
        params = {
            "client_id": settings.NOTION_CLIENT_ID,
            "redirect_uri": redirect_uri,
            "response_type": "code",
            "owner": "user",
            "state": state or "default"
        }
        
        return f"{self.auth_url}?{urlencode(params)}"
