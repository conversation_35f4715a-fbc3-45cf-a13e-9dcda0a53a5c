"""
External service integrations for Project Chronos.

This package provides integration classes for connecting with external
productivity services like Google Calendar, Todoist, Notion, etc.
"""

from app.integrations.google_calendar import GoogleCalendarIntegration
from app.integrations.todoist import TodoistIntegration
from app.integrations.notion import NotionIntegration
from app.integrations.slack import SlackIntegration

__all__ = [
    "GoogleCalendarIntegration",
    "TodoistIntegration", 
    "NotionIntegration",
    "SlackIntegration",
]
