"""
Slack integration for Project Chronos.

This module provides integration with Slack API for
notifications and body doubling session invitations.
"""

import logging
from datetime import datetime
from typing import Any, Dict, List, Optional
from urllib.parse import urlencode

from app.integrations.base import BaseIntegration, SyncResult, IntegrationError
from app.schemas.integration import OAuthTokens
from app.core.config import settings

logger = logging.getLogger(__name__)


class SlackIntegration(BaseIntegration):
    """Slack API integration."""
    
    def __init__(self):
        super().__init__()
        self.base_url = "https://slack.com/api"
        self.oauth_url = "https://slack.com/api/oauth.v2.access"
        self.auth_url = "https://slack.com/oauth/v2/authorize"
        self.scopes = [
            "chat:write",
            "chat:write.public",
            "users:read",
            "channels:read",
            "groups:read",
            "im:read",
            "mpim:read"
        ]
    
    async def authenticate(self, credentials: Dict[str, Any]) -> OAuthTokens:
        """
        Exchange OAuth authorization code for access tokens.
        
        Args:
            credentials: Dict containing 'code', 'redirect_uri', and optionally 'state'
            
        Returns:
            OAuth tokens for API access
        """
        
        token_data = {
            "client_id": settings.SLACK_CLIENT_ID,
            "client_secret": settings.SLACK_CLIENT_SECRET,
            "code": credentials["code"],
            "redirect_uri": credentials["redirect_uri"]
        }
        
        try:
            response = await self._make_request(
                method="POST",
                url=self.oauth_url,
                json_data=token_data
            )
            
            if not response.get("ok"):
                raise IntegrationError(f"Slack OAuth error: {response.get('error')}")
            
            return OAuthTokens(
                access_token=response["access_token"],
                token_type="Bearer",
                scope=response.get("scope"),
                # Slack tokens don't expire, but we'll set a long expiration
                expires_in=365 * 24 * 3600  # 1 year
            )
            
        except Exception as e:
            logger.error(f"Slack authentication failed: {e}")
            raise IntegrationError(f"Authentication failed: {e}")
    
    async def refresh_token(self, refresh_token: str) -> OAuthTokens:
        """
        Slack tokens don't expire, so this is a no-op.
        
        Args:
            refresh_token: Not used for Slack
            
        Returns:
            Original token information
        """
        
        raise IntegrationError("Slack tokens do not require refresh")
    
    async def test_connection(self, access_token: str) -> bool:
        """
        Test connection to Slack API.
        
        Args:
            access_token: OAuth access token
            
        Returns:
            True if connection is successful
        """
        
        try:
            headers = self._add_auth_header({}, access_token)
            
            response = await self._make_request(
                method="GET",
                url=f"{self.base_url}/auth.test",
                headers=headers
            )
            
            return response.get("ok", False)
            
        except Exception as e:
            logger.error(f"Slack connection test failed: {e}")
            return False
    
    async def get_user_info(self, access_token: str) -> Dict[str, Any]:
        """
        Get authenticated user information.
        
        Args:
            access_token: OAuth access token
            
        Returns:
            User information
        """
        
        try:
            headers = self._add_auth_header({}, access_token)
            
            response = await self._make_request(
                method="GET",
                url=f"{self.base_url}/auth.test",
                headers=headers
            )
            
            if not response.get("ok"):
                raise IntegrationError(f"Failed to get user info: {response.get('error')}")
            
            return {
                "user_id": response["user_id"],
                "team_id": response["team_id"],
                "team_name": response.get("team"),
                "user_name": response.get("user"),
                "url": response.get("url")
            }
            
        except Exception as e:
            logger.error(f"Failed to get Slack user info: {e}")
            raise IntegrationError(f"Failed to get user info: {e}")
    
    async def get_channels(self, access_token: str) -> List[Dict[str, Any]]:
        """
        Get list of channels the bot can access.
        
        Args:
            access_token: OAuth access token
            
        Returns:
            List of channel information
        """
        
        try:
            headers = self._add_auth_header({}, access_token)
            
            # Get public channels
            channels_response = await self._make_request(
                method="GET",
                url=f"{self.base_url}/conversations.list",
                headers=headers,
                params={"types": "public_channel,private_channel"}
            )
            
            if not channels_response.get("ok"):
                raise IntegrationError(f"Failed to get channels: {channels_response.get('error')}")
            
            channels = []
            for channel in channels_response.get("channels", []):
                channels.append({
                    "id": channel["id"],
                    "name": channel["name"],
                    "is_private": channel.get("is_private", False),
                    "is_member": channel.get("is_member", False),
                    "purpose": channel.get("purpose", {}).get("value", ""),
                    "topic": channel.get("topic", {}).get("value", "")
                })
            
            return channels
            
        except Exception as e:
            logger.error(f"Failed to get Slack channels: {e}")
            raise IntegrationError(f"Failed to get channels: {e}")
    
    async def send_message(
        self,
        access_token: str,
        channel: str,
        text: str,
        blocks: Optional[List[Dict[str, Any]]] = None,
        thread_ts: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Send a message to a Slack channel.
        
        Args:
            access_token: OAuth access token
            channel: Channel ID or name
            text: Message text
            blocks: Optional rich message blocks
            thread_ts: Optional thread timestamp for replies
            
        Returns:
            Message information
        """
        
        try:
            headers = self._add_auth_header({}, access_token)
            
            message_data = {
                "channel": channel,
                "text": text
            }
            
            if blocks:
                message_data["blocks"] = blocks
            
            if thread_ts:
                message_data["thread_ts"] = thread_ts
            
            response = await self._make_request(
                method="POST",
                url=f"{self.base_url}/chat.postMessage",
                headers=headers,
                json_data=message_data
            )
            
            if not response.get("ok"):
                raise IntegrationError(f"Failed to send message: {response.get('error')}")
            
            return {
                "ts": response["ts"],
                "channel": response["channel"],
                "message": response.get("message", {})
            }
            
        except Exception as e:
            logger.error(f"Failed to send Slack message: {e}")
            raise IntegrationError(f"Failed to send message: {e}")
    
    async def send_notification(
        self,
        access_token: str,
        channel: str,
        notification_type: str,
        title: str,
        message: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> SyncResult:
        """
        Send an ADHD-friendly notification to Slack.
        
        Args:
            access_token: OAuth access token
            channel: Target channel
            notification_type: Type of notification
            title: Notification title
            message: Notification message
            metadata: Additional metadata
            
        Returns:
            Sync result with notification status
        """
        
        try:
            # Create ADHD-friendly message blocks
            blocks = self._create_notification_blocks(
                notification_type, title, message, metadata
            )
            
            result = await self.send_message(
                access_token=access_token,
                channel=channel,
                text=f"{title}: {message}",  # Fallback text
                blocks=blocks
            )
            
            return SyncResult(
                success=True,
                items_processed=1,
                items_created=1,
                metadata={
                    "message_ts": result["ts"],
                    "channel": result["channel"],
                    "notification_type": notification_type
                }
            )
            
        except Exception as e:
            logger.error(f"Failed to send Slack notification: {e}")
            return SyncResult(
                success=False,
                errors=[f"Notification failed: {e}"]
            )
    
    async def send_body_doubling_invitation(
        self,
        access_token: str,
        channel: str,
        session_title: str,
        start_time: datetime,
        duration_minutes: int,
        join_url: str
    ) -> SyncResult:
        """
        Send a body doubling session invitation to Slack.
        
        Args:
            access_token: OAuth access token
            channel: Target channel
            session_title: Session title
            start_time: Session start time
            duration_minutes: Session duration
            join_url: URL to join the session
            
        Returns:
            Sync result with invitation status
        """
        
        try:
            # Create invitation blocks
            blocks = [
                {
                    "type": "header",
                    "text": {
                        "type": "plain_text",
                        "text": "🤝 Body Doubling Session Invitation"
                    }
                },
                {
                    "type": "section",
                    "text": {
                        "type": "mrkdwn",
                        "text": f"*{session_title}*\n\n"
                               f"📅 *When:* {start_time.strftime('%Y-%m-%d at %H:%M')}\n"
                               f"⏱️ *Duration:* {duration_minutes} minutes\n\n"
                               f"Join others for focused work time! Body doubling helps with "
                               f"accountability and motivation. 💪"
                    }
                },
                {
                    "type": "actions",
                    "elements": [
                        {
                            "type": "button",
                            "text": {
                                "type": "plain_text",
                                "text": "Join Session"
                            },
                            "style": "primary",
                            "url": join_url
                        }
                    ]
                },
                {
                    "type": "context",
                    "elements": [
                        {
                            "type": "mrkdwn",
                            "text": "💡 *Tip:* Body doubling works great for ADHD brains! "
                                   "The presence of others helps maintain focus."
                        }
                    ]
                }
            ]
            
            result = await self.send_message(
                access_token=access_token,
                channel=channel,
                text=f"Body Doubling Session: {session_title}",
                blocks=blocks
            )
            
            return SyncResult(
                success=True,
                items_processed=1,
                items_created=1,
                metadata={
                    "message_ts": result["ts"],
                    "channel": result["channel"],
                    "session_title": session_title,
                    "start_time": start_time.isoformat()
                }
            )
            
        except Exception as e:
            logger.error(f"Failed to send body doubling invitation: {e}")
            return SyncResult(
                success=False,
                errors=[f"Invitation failed: {e}"]
            )
    
    def _create_notification_blocks(
        self,
        notification_type: str,
        title: str,
        message: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """Create Slack blocks for ADHD-friendly notifications."""
        
        # Emoji mapping for different notification types
        emoji_map = {
            "task_reminder": "📋",
            "deadline_warning": "⚠️",
            "achievement": "🎉",
            "focus_break": "☕",
            "body_doubling": "🤝",
            "energy_check": "⚡",
            "daily_review": "📊",
            "medication_reminder": "💊",
            "habit_reminder": "🔄"
        }
        
        emoji = emoji_map.get(notification_type, "📢")
        
        blocks = [
            {
                "type": "header",
                "text": {
                    "type": "plain_text",
                    "text": f"{emoji} {title}"
                }
            },
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": message
                }
            }
        ]
        
        # Add metadata context if available
        if metadata:
            context_elements = []
            
            if metadata.get("energy_level"):
                context_elements.append({
                    "type": "mrkdwn",
                    "text": f"⚡ Energy: {metadata['energy_level']}"
                })
            
            if metadata.get("priority"):
                context_elements.append({
                    "type": "mrkdwn",
                    "text": f"🎯 Priority: {metadata['priority']}"
                })
            
            if metadata.get("due_date"):
                context_elements.append({
                    "type": "mrkdwn",
                    "text": f"📅 Due: {metadata['due_date']}"
                })
            
            if context_elements:
                blocks.append({
                    "type": "context",
                    "elements": context_elements
                })
        
        # Add action buttons for certain notification types
        if notification_type in ["task_reminder", "deadline_warning"]:
            blocks.append({
                "type": "actions",
                "elements": [
                    {
                        "type": "button",
                        "text": {
                            "type": "plain_text",
                            "text": "Mark Complete"
                        },
                        "style": "primary",
                        "value": f"complete_{metadata.get('task_id', '')}"
                    },
                    {
                        "type": "button",
                        "text": {
                            "type": "plain_text",
                            "text": "Snooze 15min"
                        },
                        "value": f"snooze_{metadata.get('task_id', '')}"
                    }
                ]
            })
        
        return blocks
    
    def get_oauth_url(self, redirect_uri: str, state: Optional[str] = None) -> str:
        """Generate OAuth authorization URL."""
        
        params = {
            "client_id": settings.SLACK_CLIENT_ID,
            "scope": ",".join(self.scopes),
            "redirect_uri": redirect_uri,
            "state": state or "default"
        }
        
        return f"{self.auth_url}?{urlencode(params)}"
