"""
Focus session models for Project Chronos.

This module defines models for focus sessions, including Pomodoro technique,
deep work sessions, and ADHD-specific focus patterns with group synchronization.
"""

from datetime import datetime, timezone
from typing import Any, Dict, List, Optional
from uuid import UUID

from sqlalchemy import <PERSON>olean, <PERSON><PERSON>ey, Integer, String, DateTime, Text, JSON
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.models.base import BaseModel


class FocusSession(BaseModel):
    """
    Focus session model for tracking work sessions and breaks.
    
    This model supports various focus techniques including Pomodoro,
    deep work sessions, and ADHD-specific focus patterns with group synchronization.
    """
    
    __tablename__ = "focus_sessions"
    
    # Foreign keys
    user_id: Mapped[UUID] = mapped_column(
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="ID of the user who owns this focus session"
    )
    
    task_id: Mapped[Optional[UUID]] = mapped_column(
        ForeignKey("tasks.id", ondelete="SET NULL"),
        nullable=True,
        doc="Associated task (optional)"
    )
    
    body_doubling_session_id: Mapped[Optional[UUID]] = mapped_column(
        ForeignKey("body_doubling_sessions.id", ondelete="SET NULL"),
        nullable=True,
        doc="Associated body doubling session for group focus"
    )
    
    # Session configuration
    session_type: Mapped[str] = mapped_column(
        String(20),
        default="pomodoro",
        nullable=False,
        doc="Session type: pomodoro, deep_work, sprint, custom"
    )
    
    title: Mapped[Optional[str]] = mapped_column(
        String(200),
        nullable=True,
        doc="Optional session title/description"
    )
    
    planned_duration: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        doc="Planned session duration in minutes"
    )
    
    actual_duration: Mapped[Optional[int]] = mapped_column(
        Integer,
        nullable=True,
        doc="Actual session duration in minutes"
    )
    
    break_duration: Mapped[int] = mapped_column(
        Integer,
        default=5,
        nullable=False,
        doc="Break duration in minutes"
    )
    
    # Session status
    status: Mapped[str] = mapped_column(
        String(20),
        default="planned",
        nullable=False,
        doc="Session status: planned, active, paused, completed, cancelled"
    )
    
    # ADHD-specific focus mode settings
    focus_mode_settings: Mapped[Dict[str, Any]] = mapped_column(
        JSON,
        default=dict,
        nullable=False,
        doc="Focus mode configuration and preferences"
    )
    
    # Session timing
    started_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        doc="When the session was started"
    )
    
    completed_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        doc="When the session was completed"
    )
    
    paused_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        doc="When the session was paused (if applicable)"
    )
    
    # Group focus features
    is_group_session: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        nullable=False,
        doc="Whether this is a synchronized group focus session"
    )
    
    group_session_leader: Mapped[Optional[UUID]] = mapped_column(
        ForeignKey("users.id", ondelete="SET NULL"),
        nullable=True,
        doc="User who initiated the group focus session"
    )
    
    # ADHD-specific tracking
    hyperfocus_detected: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        nullable=False,
        doc="Whether hyperfocus was detected during this session"
    )
    
    hyperfocus_duration: Mapped[Optional[int]] = mapped_column(
        Integer,
        nullable=True,
        doc="Duration of hyperfocus in minutes"
    )
    
    distraction_count: Mapped[int] = mapped_column(
        Integer,
        default=0,
        nullable=False,
        doc="Number of distractions during the session"
    )
    
    energy_level_start: Mapped[Optional[int]] = mapped_column(
        Integer,
        nullable=True,
        doc="Energy level at session start (1-5)"
    )
    
    energy_level_end: Mapped[Optional[int]] = mapped_column(
        Integer,
        nullable=True,
        doc="Energy level at session end (1-5)"
    )
    
    # Session notes
    session_notes: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        doc="Optional notes about the session"
    )
    
    # Relationships
    user: Mapped["User"] = relationship(
        "User",
        foreign_keys=[user_id],
        back_populates="focus_sessions",
        doc="User who owns this focus session"
    )
    
    task: Mapped[Optional["Task"]] = relationship(
        "Task",
        back_populates="focus_sessions",
        doc="Associated task (if any)"
    )
    
    body_doubling_session: Mapped[Optional["BodyDoublingSession"]] = relationship(
        "BodyDoublingSession",
        foreign_keys=[body_doubling_session_id],
        doc="Associated body doubling session for group focus"
    )
    
    leader: Mapped[Optional["User"]] = relationship(
        "User",
        foreign_keys=[group_session_leader],
        doc="User who initiated the group focus session"
    )
    
    def is_active(self) -> bool:
        """Check if session is currently active."""
        return self.status == "active"
    
    def is_completed(self) -> bool:
        """Check if session is completed."""
        return self.status == "completed"
    
    def get_elapsed_minutes(self) -> int:
        """Get elapsed time in minutes."""
        if not self.started_at:
            return 0

        # Ensure both datetimes are timezone-aware
        started_at = self.started_at
        if started_at.tzinfo is None:
            started_at = started_at.replace(tzinfo=timezone.utc)

        end_time = self.completed_at or datetime.now(timezone.utc)
        if end_time.tzinfo is None:
            end_time = end_time.replace(tzinfo=timezone.utc)

        delta = end_time - started_at
        return int(delta.total_seconds() / 60)
    
    def get_remaining_minutes(self) -> int:
        """Get remaining time in minutes."""
        if not self.started_at or self.status != "active":
            return self.planned_duration

        elapsed = self.get_elapsed_minutes()
        return max(0, self.planned_duration - elapsed)
    
    def get_progress_percentage(self) -> float:
        """Get progress as percentage (0.0-1.0)."""
        if self.planned_duration == 0:
            return 0.0
        
        elapsed = self.get_elapsed_minutes()
        return min(1.0, elapsed / self.planned_duration)


class FocusSessionParticipant(BaseModel):
    """
    Model for tracking participants in group focus sessions.
    
    Tracks individual participant status and progress within
    a synchronized group focus session.
    """
    
    __tablename__ = "focus_session_participants"
    
    # Foreign keys
    focus_session_id: Mapped[UUID] = mapped_column(
        ForeignKey("focus_sessions.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Focus session ID"
    )
    
    user_id: Mapped[UUID] = mapped_column(
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Participating user ID"
    )
    
    # Participation details
    joined_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        default=lambda: datetime.now(timezone.utc),
        nullable=False,
        doc="When the user joined the focus session"
    )
    
    left_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        doc="When the user left the focus session"
    )
    
    # Participant status
    status: Mapped[str] = mapped_column(
        String(20),
        default="active",
        nullable=False,
        doc="Participant status: active, paused, completed, left"
    )
    
    # Progress tracking
    personal_task_id: Mapped[Optional[UUID]] = mapped_column(
        ForeignKey("tasks.id", ondelete="SET NULL"),
        nullable=True,
        doc="Personal task being worked on during this session"
    )
    
    progress_notes: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        doc="Personal progress notes"
    )
    
    # Relationships
    focus_session: Mapped["FocusSession"] = relationship(
        "FocusSession",
        doc="Focus session"
    )
    
    user: Mapped["User"] = relationship(
        "User",
        doc="Participating user"
    )
    
    personal_task: Mapped[Optional["Task"]] = relationship(
        "Task",
        doc="Personal task being worked on"
    )
    
    def is_active(self) -> bool:
        """Check if participant is currently active."""
        return self.status == "active" and self.left_at is None
