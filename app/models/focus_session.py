"""
Focus session models for Project Chronos.

This module defines SQLAlchemy models for ADHD-optimized focus sessions,
Pomodoro timers, and flow state management.
"""

from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from uuid import UUID, uuid4
from enum import Enum

from sqlalchemy import (
    String, Integer, Boolean, DateTime, Text, JSON, Float,
    ForeignKey, UniqueConstraint, Index, Enum as SQLEnum
)
from sqlalchemy.dialects.postgresql import UUID as PostgresUUID, JSONB
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.sql import func

from app.models.base import Base, TimestampMixin


class FocusSessionType(str, Enum):
    """Types of focus sessions."""
    POMODORO = "pomodoro"
    DEEP_WORK = "deep_work"
    SPRINT = "sprint"
    CUSTOM = "custom"
    HYPERFOCUS = "hyperfocus"


class FocusSessionStatus(str, Enum):
    """Focus session status states."""
    PLANNED = "planned"
    ACTIVE = "active"
    PAUSED = "paused"
    BREAK = "break"
    COMPLETED = "completed"
    CANCELLED = "cancelled"


class FocusSession(Base, TimestampMixin):
    """
    Focus session model for ADHD-optimized focus management.
    
    Represents individual focus sessions with flexible timing,
    pause/resume functionality, and hyperfocus protection.
    """
    
    __tablename__ = "focus_sessions"
    
    # Primary key
    id: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True),
        primary_key=True,
        default=uuid4,
        doc="Unique focus session identifier"
    )
    
    # User association
    user_id: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="User who owns this focus session"
    )
    
    # Optional task association
    task_id: Mapped[Optional[UUID]] = mapped_column(
        PostgresUUID(as_uuid=True),
        ForeignKey("tasks.id", ondelete="SET NULL"),
        nullable=True,
        index=True,
        doc="Associated task (optional)"
    )
    
    # Session configuration
    session_type: Mapped[FocusSessionType] = mapped_column(
        SQLEnum(FocusSessionType),
        nullable=False,
        default=FocusSessionType.POMODORO,
        doc="Type of focus session"
    )
    
    title: Mapped[Optional[str]] = mapped_column(
        String(200),
        nullable=True,
        doc="Optional session title"
    )
    
    description: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        doc="Session description or notes"
    )
    
    # Timing configuration
    planned_duration: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        doc="Planned duration in minutes"
    )
    
    break_duration: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        default=5,
        doc="Break duration in minutes"
    )
    
    # Session state
    status: Mapped[FocusSessionStatus] = mapped_column(
        SQLEnum(FocusSessionStatus),
        nullable=False,
        default=FocusSessionStatus.PLANNED,
        doc="Current session status"
    )
    
    # Timing tracking
    started_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        doc="When session actually started"
    )
    
    ended_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        doc="When session ended"
    )
    
    paused_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        doc="When session was paused"
    )
    
    total_paused_duration: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        default=0,
        doc="Total paused time in seconds"
    )
    
    actual_duration: Mapped[Optional[int]] = mapped_column(
        Integer,
        nullable=True,
        doc="Actual session duration in minutes"
    )
    
    # Focus mode settings
    focus_mode_id: Mapped[Optional[UUID]] = mapped_column(
        PostgresUUID(as_uuid=True),
        ForeignKey("focus_modes.id", ondelete="SET NULL"),
        nullable=True,
        doc="Associated focus mode"
    )
    
    # Session data and analytics
    session_data: Mapped[Dict[str, Any]] = mapped_column(
        JSONB,
        nullable=False,
        default=dict,
        doc="Session configuration and metadata"
    )
    
    completion_notes: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        doc="Notes added upon session completion"
    )
    
    productivity_rating: Mapped[Optional[int]] = mapped_column(
        Integer,
        nullable=True,
        doc="User-rated productivity (1-5 scale)"
    )
    
    focus_quality_rating: Mapped[Optional[int]] = mapped_column(
        Integer,
        nullable=True,
        doc="User-rated focus quality (1-5 scale)"
    )
    
    # Hyperfocus tracking
    hyperfocus_detected: Mapped[bool] = mapped_column(
        Boolean,
        nullable=False,
        default=False,
        doc="Whether hyperfocus was detected"
    )
    
    hyperfocus_duration: Mapped[Optional[int]] = mapped_column(
        Integer,
        nullable=True,
        doc="Duration of hyperfocus in minutes"
    )
    
    break_reminders_sent: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        default=0,
        doc="Number of break reminders sent"
    )
    
    # Relationships
    user: Mapped["User"] = relationship(
        "User",
        back_populates="focus_sessions",
        doc="User who owns this session"
    )
    
    task: Mapped[Optional["Task"]] = relationship(
        "Task",
        back_populates="focus_sessions",
        doc="Associated task"
    )
    
    focus_mode: Mapped[Optional["FocusMode"]] = relationship(
        "FocusMode",
        back_populates="focus_sessions",
        doc="Associated focus mode"
    )
    
    breaks: Mapped[List["FocusBreak"]] = relationship(
        "FocusBreak",
        back_populates="focus_session",
        cascade="all, delete-orphan",
        doc="Breaks taken during this session"
    )

    notifications: Mapped[List["Notification"]] = relationship(
        "Notification",
        back_populates="focus_session",
        doc="Notifications associated with this focus session"
    )
    
    # Indexes
    __table_args__ = (
        Index("idx_focus_sessions_user", "user_id"),
        Index("idx_focus_sessions_task", "task_id"),
        Index("idx_focus_sessions_status", "status"),
        Index("idx_focus_sessions_started", "started_at"),
        Index("idx_focus_sessions_type", "session_type"),
    )
    
    def get_elapsed_time(self) -> Optional[timedelta]:
        """Get elapsed time for active or completed session."""
        if not self.started_at:
            return None
        
        end_time = self.ended_at or datetime.utcnow()
        elapsed = end_time - self.started_at
        
        # Subtract paused time
        if self.total_paused_duration > 0:
            elapsed -= timedelta(seconds=self.total_paused_duration)
        
        return elapsed
    
    def get_remaining_time(self) -> Optional[timedelta]:
        """Get remaining time for active session."""
        if self.status != FocusSessionStatus.ACTIVE or not self.started_at:
            return None
        
        elapsed = self.get_elapsed_time()
        if not elapsed:
            return None
        
        planned = timedelta(minutes=self.planned_duration)
        remaining = planned - elapsed
        
        return remaining if remaining.total_seconds() > 0 else timedelta(0)
    
    def is_overdue(self) -> bool:
        """Check if session has exceeded planned duration."""
        if self.status != FocusSessionStatus.ACTIVE:
            return False
        
        remaining = self.get_remaining_time()
        return remaining is not None and remaining.total_seconds() <= 0
    
    def should_suggest_break(self) -> bool:
        """Check if break should be suggested based on duration and type."""
        if self.status != FocusSessionStatus.ACTIVE:
            return False
        
        elapsed = self.get_elapsed_time()
        if not elapsed:
            return False
        
        # Different break suggestion thresholds by session type
        thresholds = {
            FocusSessionType.POMODORO: 25,  # Standard Pomodoro
            FocusSessionType.DEEP_WORK: 90,  # Deep work sessions
            FocusSessionType.SPRINT: 15,     # Quick sprints
            FocusSessionType.CUSTOM: self.planned_duration,
            FocusSessionType.HYPERFOCUS: 120  # Hyperfocus protection
        }
        
        threshold_minutes = thresholds.get(self.session_type, self.planned_duration)
        return elapsed.total_seconds() >= (threshold_minutes * 60)


class FocusMode(Base, TimestampMixin):
    """
    Focus mode configuration for notification shielding and environment setup.
    
    Represents customizable focus environments with specific settings
    for different types of work.
    """
    
    __tablename__ = "focus_modes"
    
    # Primary key
    id: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True),
        primary_key=True,
        default=uuid4,
        doc="Unique focus mode identifier"
    )
    
    # User association
    user_id: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="User who owns this focus mode"
    )
    
    # Mode configuration
    name: Mapped[str] = mapped_column(
        String(100),
        nullable=False,
        doc="Focus mode name"
    )
    
    description: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        doc="Focus mode description"
    )
    
    is_default: Mapped[bool] = mapped_column(
        Boolean,
        nullable=False,
        default=False,
        doc="Whether this is a default system mode"
    )
    
    # Notification settings
    block_notifications: Mapped[bool] = mapped_column(
        Boolean,
        nullable=False,
        default=True,
        doc="Block non-essential notifications"
    )
    
    allowed_contacts: Mapped[List[str]] = mapped_column(
        JSONB,
        nullable=False,
        default=list,
        doc="Emergency contacts allowed through"
    )
    
    # Timer settings
    default_duration: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        default=25,
        doc="Default session duration in minutes"
    )
    
    break_duration: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        default=5,
        doc="Default break duration in minutes"
    )
    
    enable_break_reminders: Mapped[bool] = mapped_column(
        Boolean,
        nullable=False,
        default=True,
        doc="Enable gentle break reminders"
    )
    
    break_reminder_interval: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        default=25,
        doc="Break reminder interval in minutes"
    )
    
    # Hyperfocus protection
    enable_hyperfocus_protection: Mapped[bool] = mapped_column(
        Boolean,
        nullable=False,
        default=True,
        doc="Enable hyperfocus detection and protection"
    )
    
    hyperfocus_threshold: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        default=90,
        doc="Hyperfocus detection threshold in minutes"
    )
    
    # Environment settings
    settings: Mapped[Dict[str, Any]] = mapped_column(
        JSONB,
        nullable=False,
        default=dict,
        doc="Additional focus mode settings"
    )
    
    # Usage tracking
    usage_count: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        default=0,
        doc="Number of times this mode has been used"
    )
    
    last_used: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        doc="When this mode was last used"
    )
    
    # Relationships
    user: Mapped["User"] = relationship(
        "User",
        back_populates="focus_modes",
        doc="User who owns this focus mode"
    )
    
    focus_sessions: Mapped[List["FocusSession"]] = relationship(
        "FocusSession",
        back_populates="focus_mode",
        doc="Sessions using this focus mode"
    )
    
    # Indexes
    __table_args__ = (
        Index("idx_focus_modes_user", "user_id"),
        Index("idx_focus_modes_default", "is_default"),
        Index("idx_focus_modes_usage", "usage_count"),
    )


class FocusBreak(Base, TimestampMixin):
    """
    Focus break tracking for session analytics.
    
    Tracks breaks taken during focus sessions for pattern analysis
    and optimization recommendations.
    """
    
    __tablename__ = "focus_breaks"
    
    # Primary key
    id: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True),
        primary_key=True,
        default=uuid4,
        doc="Unique break identifier"
    )
    
    # Session association
    focus_session_id: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True),
        ForeignKey("focus_sessions.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Associated focus session"
    )
    
    # Break timing
    started_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        nullable=False,
        server_default=func.now(),
        doc="When break started"
    )
    
    ended_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        doc="When break ended"
    )
    
    planned_duration: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        doc="Planned break duration in minutes"
    )
    
    actual_duration: Mapped[Optional[int]] = mapped_column(
        Integer,
        nullable=True,
        doc="Actual break duration in minutes"
    )
    
    # Break type and reason
    break_type: Mapped[str] = mapped_column(
        String(50),
        nullable=False,
        default="scheduled",
        doc="Type of break (scheduled, spontaneous, forced)"
    )
    
    break_reason: Mapped[Optional[str]] = mapped_column(
        String(200),
        nullable=True,
        doc="Reason for taking break"
    )
    
    # Break activities
    activities: Mapped[List[str]] = mapped_column(
        JSONB,
        nullable=False,
        default=list,
        doc="Activities during break"
    )
    
    # Break effectiveness
    effectiveness_rating: Mapped[Optional[int]] = mapped_column(
        Integer,
        nullable=True,
        doc="User-rated break effectiveness (1-5)"
    )
    
    # Relationships
    focus_session: Mapped["FocusSession"] = relationship(
        "FocusSession",
        back_populates="breaks",
        doc="Associated focus session"
    )
    
    # Indexes
    __table_args__ = (
        Index("idx_focus_breaks_session", "focus_session_id"),
        Index("idx_focus_breaks_started", "started_at"),
        Index("idx_focus_breaks_type", "break_type"),
    )
    
    def get_duration(self) -> Optional[timedelta]:
        """Get actual break duration."""
        if not self.ended_at:
            return None
        return self.ended_at - self.started_at
