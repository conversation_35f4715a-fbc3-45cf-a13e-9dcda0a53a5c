"""
Body doubling models for Project Chronos.

This module defines models for virtual body doubling sessions, participants,
and real-time collaboration features for ADHD users.
"""

from datetime import datetime, timezone
from typing import Any, Dict, List, Optional
from uuid import UUID

from sqlalchemy import <PERSON><PERSON><PERSON>, Foreign<PERSON>ey, Integer, String, DateTime, Text, JSON
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.models.base import BaseModel


class BodyDoublingSession(BaseModel):
    """
    Body doubling session model for virtual co-working sessions.
    
    This model supports virtual body doubling sessions where users can work
    together for accountability and shared focus, particularly helpful for ADHD users.
    """
    
    __tablename__ = "body_doubling_sessions"
    
    # Session host
    host_user_id: Mapped[UUID] = mapped_column(
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="ID of the user who created/hosts this session"
    )
    
    # Session configuration
    title: Mapped[str] = mapped_column(
        String(200),
        nullable=False,
        doc="Session title/description"
    )
    
    description: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        doc="Optional detailed session description"
    )
    
    # Participant limits
    max_participants: Mapped[int] = mapped_column(
        Integer,
        default=4,
        nullable=False,
        doc="Maximum number of participants allowed"
    )
    
    current_participants: Mapped[int] = mapped_column(
        Integer,
        default=0,
        nullable=False,
        doc="Current number of active participants"
    )
    
    # Session type and access control
    session_type: Mapped[str] = mapped_column(
        String(20),
        default="open",
        nullable=False,
        doc="Session type: open, focused, study, work, creative"
    )
    
    is_public: Mapped[bool] = mapped_column(
        Boolean,
        default=True,
        nullable=False,
        doc="Whether session is publicly discoverable"
    )
    
    requires_approval: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        nullable=False,
        doc="Whether host approval is required to join"
    )
    
    password_protected: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        nullable=False,
        doc="Whether session requires password to join"
    )
    
    session_password: Mapped[Optional[str]] = mapped_column(
        String(255),
        nullable=True,
        doc="Hashed password for protected sessions"
    )
    
    # Scheduling
    scheduled_start: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        doc="When the session is scheduled to start"
    )
    
    scheduled_end: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        doc="When the session is scheduled to end"
    )
    
    actual_start: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        doc="When the session actually started"
    )
    
    actual_end: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        doc="When the session actually ended"
    )
    
    # Session status
    status: Mapped[str] = mapped_column(
        String(20),
        default="waiting",
        nullable=False,
        doc="Session status: waiting, active, paused, completed, cancelled"
    )
    
    # Session settings and preferences
    session_settings: Mapped[Dict[str, Any]] = mapped_column(
        JSON,
        default=dict,
        nullable=False,
        doc="Session configuration including focus modes, break schedules, etc."
    )
    
    # Focus session integration (will be linked when Agent 5 is implemented)
    current_focus_session_id: Mapped[Optional[UUID]] = mapped_column(
        nullable=True,
        doc="Currently active group focus session (will be FK when focus_sessions table exists)"
    )
    
    # Relationships
    host: Mapped["User"] = relationship(
        "User",
        foreign_keys=[host_user_id],
        doc="User who hosts this session"
    )
    
    participants: Mapped[List["SessionParticipant"]] = relationship(
        "SessionParticipant",
        back_populates="session",
        cascade="all, delete-orphan",
        doc="Session participants"
    )
    
    messages: Mapped[List["SessionMessage"]] = relationship(
        "SessionMessage",
        back_populates="session",
        cascade="all, delete-orphan",
        order_by="SessionMessage.created_at",
        doc="Session messages and communications"
    )

    focus_sessions: Mapped[List["FocusSession"]] = relationship(
        "FocusSession",
        back_populates="body_doubling_session",
        doc="Focus sessions running within this body doubling session"
    )
    
    def is_active(self) -> bool:
        """Check if session is currently active."""
        return self.status == "active"
    
    def can_join(self) -> bool:
        """Check if new participants can join."""
        return (
            self.status in ["waiting", "active"] and
            self.current_participants < self.max_participants
        )
    
    def get_duration_minutes(self) -> Optional[int]:
        """Get session duration in minutes if both start and end times are set."""
        if self.actual_start and self.actual_end:
            delta = self.actual_end - self.actual_start
            return int(delta.total_seconds() / 60)
        return None


class SessionParticipant(BaseModel):
    """
    Model for tracking participants in body doubling sessions.
    
    Tracks individual participant status, preferences, and engagement
    within a body doubling session.
    """
    
    __tablename__ = "session_participants"
    
    # Foreign keys
    session_id: Mapped[UUID] = mapped_column(
        ForeignKey("body_doubling_sessions.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Body doubling session ID"
    )
    
    user_id: Mapped[UUID] = mapped_column(
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Participating user ID"
    )
    
    # Participation details
    joined_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        default=lambda: datetime.now(timezone.utc),
        nullable=False,
        doc="When the user joined the session"
    )
    
    left_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        doc="When the user left the session"
    )
    
    # Participant status
    status: Mapped[str] = mapped_column(
        String(20),
        default="active",
        nullable=False,
        doc="Participant status: active, away, focused, break, left"
    )
    
    # Privacy and sharing preferences
    share_progress: Mapped[bool] = mapped_column(
        Boolean,
        default=True,
        nullable=False,
        doc="Whether to share task progress with other participants"
    )
    
    anonymous_mode: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        nullable=False,
        doc="Whether to participate anonymously"
    )
    
    # Engagement tracking
    last_activity: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        default=lambda: datetime.now(timezone.utc),
        nullable=False,
        doc="Last activity timestamp for presence detection"
    )
    
    # Relationships
    session: Mapped["BodyDoublingSession"] = relationship(
        "BodyDoublingSession",
        back_populates="participants",
        doc="Body doubling session"
    )
    
    user: Mapped["User"] = relationship(
        "User",
        doc="Participating user"
    )
    
    def is_active(self) -> bool:
        """Check if participant is currently active."""
        return self.status in ["active", "focused", "break"] and self.left_at is None
    
    def get_session_duration_minutes(self) -> int:
        """Get how long the participant has been in the session."""
        end_time = self.left_at or datetime.now(timezone.utc)
        delta = end_time - self.joined_at
        return int(delta.total_seconds() / 60)


class SessionMessage(BaseModel):
    """
    Model for messages within body doubling sessions.
    
    Supports various message types including chat, encouragement,
    system notifications, and progress updates.
    """
    
    __tablename__ = "session_messages"
    
    # Foreign keys
    session_id: Mapped[UUID] = mapped_column(
        ForeignKey("body_doubling_sessions.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Body doubling session ID"
    )
    
    sender_id: Mapped[Optional[UUID]] = mapped_column(
        ForeignKey("users.id", ondelete="SET NULL"),
        nullable=True,
        index=True,
        doc="User who sent the message (null for system messages)"
    )
    
    # Message content
    message_type: Mapped[str] = mapped_column(
        String(20),
        default="chat",
        nullable=False,
        doc="Message type: chat, encouragement, system, progress_update"
    )
    
    content: Mapped[str] = mapped_column(
        Text,
        nullable=False,
        doc="Message content"
    )
    
    # Message metadata
    message_data: Mapped[Dict[str, Any]] = mapped_column(
        JSON,
        default=dict,
        nullable=False,
        doc="Additional message data (reactions, attachments, etc.)"
    )
    
    # Relationships
    session: Mapped["BodyDoublingSession"] = relationship(
        "BodyDoublingSession",
        back_populates="messages",
        doc="Body doubling session this message belongs to"
    )
    
    sender: Mapped[Optional["User"]] = relationship(
        "User",
        doc="User who sent the message"
    )
    
    def is_system_message(self) -> bool:
        """Check if this is a system-generated message."""
        return self.sender_id is None or self.message_type == "system"
    
    def is_encouragement(self) -> bool:
        """Check if this is an encouragement message."""
        return self.message_type == "encouragement"
