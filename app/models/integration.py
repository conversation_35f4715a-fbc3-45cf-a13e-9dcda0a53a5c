"""
Integration models for Project Chronos.

This module defines SQLAlchemy models for external service integrations,
OAuth credentials, and synchronization tracking.
"""

from datetime import datetime
from typing import Dict, Any, Optional, List
from uuid import UUID, uuid4
from enum import Enum

from sqlalchemy import (
    String, Integer, Boolean, DateTime, Text, JSON, Float,
    ForeignKey, UniqueConstraint, Index, Enum as SQLEnum
)
from sqlalchemy.dialects.postgresql import UUID as PostgresUUID, JSONB
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.sql import func

from app.models.base import Base, TimestampMixin


class IntegrationType(str, Enum):
    """Types of external integrations."""
    GOOGLE_CALENDAR = "google_calendar"
    OUTLOOK_CALENDAR = "outlook_calendar"
    TODOIST = "todoist"
    NOTION = "notion"
    SLACK = "slack"
    GITHUB = "github"
    TRELLO = "trello"
    ASANA = "asana"


class IntegrationStatus(str, Enum):
    """Integration status states."""
    ACTIVE = "active"
    INACTIVE = "inactive"
    ERROR = "error"
    EXPIRED = "expired"
    REVOKED = "revoked"


class SyncStatus(str, Enum):
    """Synchronization status states."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    PARTIAL = "partial"


class Integration(Base, TimestampMixin):
    """
    External service integration configuration.
    
    Stores OAuth credentials and integration settings for
    external services like Google Calendar, Todoist, etc.
    """
    
    __tablename__ = "integrations"
    
    # Primary key
    id: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True),
        primary_key=True,
        default=uuid4,
        doc="Unique integration identifier"
    )
    
    # User association
    user_id: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="User who owns this integration"
    )
    
    # Integration details
    integration_type: Mapped[IntegrationType] = mapped_column(
        SQLEnum(IntegrationType),
        nullable=False,
        index=True,
        doc="Type of external service"
    )
    
    name: Mapped[str] = mapped_column(
        String(100),
        nullable=False,
        doc="User-friendly name for this integration"
    )
    
    description: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        doc="Optional description of the integration"
    )
    
    # OAuth and authentication
    external_id: Mapped[Optional[str]] = mapped_column(
        String(255),
        nullable=True,
        doc="External service user/account ID"
    )
    
    access_token: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        doc="OAuth access token (encrypted)"
    )
    
    refresh_token: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        doc="OAuth refresh token (encrypted)"
    )
    
    token_expires_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        doc="When the access token expires"
    )
    
    # Integration configuration
    config: Mapped[Dict[str, Any]] = mapped_column(
        JSONB,
        nullable=False,
        default=dict,
        doc="Integration-specific configuration"
    )
    
    sync_settings: Mapped[Dict[str, Any]] = mapped_column(
        JSONB,
        nullable=False,
        default=dict,
        doc="Synchronization preferences and settings"
    )
    
    # Status and health
    status: Mapped[IntegrationStatus] = mapped_column(
        SQLEnum(IntegrationStatus),
        nullable=False,
        default=IntegrationStatus.ACTIVE,
        doc="Current integration status"
    )
    
    last_sync_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        doc="When last synchronization occurred"
    )
    
    last_error: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        doc="Last error message if sync failed"
    )
    
    error_count: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        default=0,
        doc="Number of consecutive errors"
    )
    
    # Sync statistics
    total_syncs: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        default=0,
        doc="Total number of sync operations"
    )
    
    successful_syncs: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        default=0,
        doc="Number of successful sync operations"
    )
    
    # Webhook configuration
    webhook_id: Mapped[Optional[str]] = mapped_column(
        String(255),
        nullable=True,
        doc="External service webhook ID"
    )
    
    webhook_secret: Mapped[Optional[str]] = mapped_column(
        String(255),
        nullable=True,
        doc="Webhook verification secret"
    )
    
    # Relationships
    user: Mapped["User"] = relationship(
        "User",
        back_populates="integrations",
        doc="User who owns this integration"
    )
    
    sync_logs: Mapped[List["SyncLog"]] = relationship(
        "SyncLog",
        back_populates="integration",
        cascade="all, delete-orphan",
        doc="Synchronization history logs"
    )
    
    # Indexes
    __table_args__ = (
        Index("idx_integrations_user", "user_id"),
        Index("idx_integrations_type", "integration_type"),
        Index("idx_integrations_status", "status"),
        Index("idx_integrations_last_sync", "last_sync_at"),
        UniqueConstraint("user_id", "integration_type", "external_id", name="uq_user_integration"),
    )
    
    def is_token_expired(self) -> bool:
        """Check if the access token is expired."""
        if not self.token_expires_at:
            return False
        return datetime.utcnow() >= self.token_expires_at
    
    def needs_refresh(self) -> bool:
        """Check if token needs refresh (expires within 5 minutes)."""
        if not self.token_expires_at:
            return False
        return datetime.utcnow() >= (self.token_expires_at - timedelta(minutes=5))
    
    def increment_error_count(self):
        """Increment error count and update status if needed."""
        self.error_count += 1
        if self.error_count >= 5:
            self.status = IntegrationStatus.ERROR


class SyncLog(Base, TimestampMixin):
    """
    Synchronization operation log.
    
    Tracks the history of sync operations between Project Chronos
    and external services for debugging and monitoring.
    """
    
    __tablename__ = "sync_logs"
    
    # Primary key
    id: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True),
        primary_key=True,
        default=uuid4,
        doc="Unique sync log identifier"
    )
    
    # Integration association
    integration_id: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True),
        ForeignKey("integrations.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Integration this sync belongs to"
    )
    
    # Sync operation details
    operation_type: Mapped[str] = mapped_column(
        String(50),
        nullable=False,
        doc="Type of sync operation (import, export, webhook)"
    )
    
    status: Mapped[SyncStatus] = mapped_column(
        SQLEnum(SyncStatus),
        nullable=False,
        default=SyncStatus.PENDING,
        doc="Current sync status"
    )
    
    started_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        nullable=False,
        default=func.now(),
        doc="When sync operation started"
    )
    
    completed_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        doc="When sync operation completed"
    )
    
    # Sync results
    items_processed: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        default=0,
        doc="Number of items processed"
    )
    
    items_created: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        default=0,
        doc="Number of items created"
    )
    
    items_updated: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        default=0,
        doc="Number of items updated"
    )
    
    items_deleted: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        default=0,
        doc="Number of items deleted"
    )
    
    conflicts_detected: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        default=0,
        doc="Number of conflicts detected"
    )
    
    # Error tracking
    error_message: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        doc="Error message if sync failed"
    )
    
    error_details: Mapped[Dict[str, Any]] = mapped_column(
        JSONB,
        nullable=False,
        default=dict,
        doc="Detailed error information"
    )
    
    # Sync metadata
    sync_data: Mapped[Dict[str, Any]] = mapped_column(
        JSONB,
        nullable=False,
        default=dict,
        doc="Additional sync operation data"
    )
    
    # Relationships
    integration: Mapped["Integration"] = relationship(
        "Integration",
        back_populates="sync_logs",
        doc="Integration this sync belongs to"
    )
    
    # Indexes
    __table_args__ = (
        Index("idx_sync_logs_integration", "integration_id"),
        Index("idx_sync_logs_status", "status"),
        Index("idx_sync_logs_started", "started_at"),
        Index("idx_sync_logs_operation", "operation_type"),
    )
    
    @property
    def duration_seconds(self) -> Optional[float]:
        """Calculate sync operation duration in seconds."""
        if not self.completed_at:
            return None
        return (self.completed_at - self.started_at).total_seconds()
    
    def mark_completed(self, status: SyncStatus = SyncStatus.COMPLETED):
        """Mark sync operation as completed."""
        self.status = status
        self.completed_at = datetime.utcnow()
    
    def mark_failed(self, error_message: str, error_details: Optional[Dict[str, Any]] = None):
        """Mark sync operation as failed."""
        self.status = SyncStatus.FAILED
        self.completed_at = datetime.utcnow()
        self.error_message = error_message
        if error_details:
            self.error_details = error_details


class WebhookEvent(Base, TimestampMixin):
    """
    Webhook event from external services.
    
    Stores incoming webhook events for processing and debugging.
    """
    
    __tablename__ = "webhook_events"
    
    # Primary key
    id: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True),
        primary_key=True,
        default=uuid4,
        doc="Unique webhook event identifier"
    )
    
    # Integration association
    integration_id: Mapped[Optional[UUID]] = mapped_column(
        PostgresUUID(as_uuid=True),
        ForeignKey("integrations.id", ondelete="SET NULL"),
        nullable=True,
        index=True,
        doc="Integration this webhook belongs to"
    )
    
    # Webhook details
    integration_type: Mapped[IntegrationType] = mapped_column(
        SQLEnum(IntegrationType),
        nullable=False,
        index=True,
        doc="Type of external service"
    )
    
    event_type: Mapped[str] = mapped_column(
        String(100),
        nullable=False,
        doc="Type of webhook event"
    )
    
    external_id: Mapped[Optional[str]] = mapped_column(
        String(255),
        nullable=True,
        doc="External event or resource ID"
    )
    
    # Event data
    payload: Mapped[Dict[str, Any]] = mapped_column(
        JSONB,
        nullable=False,
        default=dict,
        doc="Raw webhook payload"
    )
    
    headers: Mapped[Dict[str, Any]] = mapped_column(
        JSONB,
        nullable=False,
        default=dict,
        doc="HTTP headers from webhook request"
    )
    
    # Processing status
    processed: Mapped[bool] = mapped_column(
        Boolean,
        nullable=False,
        default=False,
        doc="Whether event has been processed"
    )
    
    processed_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        doc="When event was processed"
    )
    
    processing_error: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        doc="Error message if processing failed"
    )
    
    # Relationships
    integration: Mapped[Optional["Integration"]] = relationship(
        "Integration",
        doc="Integration this webhook belongs to"
    )
    
    # Indexes
    __table_args__ = (
        Index("idx_webhook_events_integration", "integration_id"),
        Index("idx_webhook_events_type", "integration_type"),
        Index("idx_webhook_events_processed", "processed"),
        Index("idx_webhook_events_created", "created_at"),
    )
