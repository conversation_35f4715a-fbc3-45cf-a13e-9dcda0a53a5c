"""
Time blocking models for Project Chronos.

This module defines SQLAlchemy models for ADHD-optimized time blocking,
scheduling, and visual time management.
"""

from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from uuid import UUID, uuid4
from enum import Enum

from sqlalchemy import (
    String, Integer, Boolean, DateTime, Text, JSON, Float,
    ForeignKey, UniqueConstraint, Index, Enum as SQLEnum
)
from sqlalchemy.dialects.postgresql import UUID as PostgresUUID, JSONB
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.sql import func

from app.models.base import Base, TimestampMixin


class TimeBlockType(str, Enum):
    """Types of time blocks."""
    TASK = "task"
    BREAK = "break"
    BUFFER = "buffer"
    EVENT = "event"
    MEETING = "meeting"
    TRAVEL = "travel"
    PERSONAL = "personal"
    FLEXIBLE = "flexible"


class TimeBlockStatus(str, Enum):
    """Time block status states."""
    PLANNED = "planned"
    ACTIVE = "active"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    MOVED = "moved"


class TimeBlock(Base, TimestampMixin):
    """
    Time block model for ADHD-optimized scheduling.
    
    Represents scheduled time periods with visual and cognitive
    accommodations for time blindness and executive dysfunction.
    """
    
    __tablename__ = "time_blocks"
    
    # Primary key
    id: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True),
        primary_key=True,
        default=uuid4,
        doc="Unique time block identifier"
    )
    
    # User association
    user_id: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="User who owns this time block"
    )
    
    # Optional task association
    task_id: Mapped[Optional[UUID]] = mapped_column(
        PostgresUUID(as_uuid=True),
        ForeignKey("tasks.id", ondelete="SET NULL"),
        nullable=True,
        index=True,
        doc="Associated task (optional)"
    )
    
    # Block identification
    title: Mapped[str] = mapped_column(
        String(200),
        nullable=False,
        doc="Time block title"
    )
    
    description: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        doc="Detailed description of the time block"
    )
    
    block_type: Mapped[TimeBlockType] = mapped_column(
        SQLEnum(TimeBlockType),
        nullable=False,
        default=TimeBlockType.TASK,
        doc="Type of time block"
    )
    
    # Timing information
    start_time: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        nullable=False,
        index=True,
        doc="Block start time"
    )
    
    end_time: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        nullable=False,
        index=True,
        doc="Block end time"
    )
    
    duration_minutes: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        doc="Duration in minutes"
    )
    
    # ADHD-specific features
    is_flexible: Mapped[bool] = mapped_column(
        Boolean,
        nullable=False,
        default=False,
        doc="Whether block can be moved automatically"
    )
    
    buffer_before: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        default=0,
        doc="Buffer time before block in minutes"
    )
    
    buffer_after: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        default=0,
        doc="Buffer time after block in minutes"
    )
    
    energy_level_required: Mapped[Optional[str]] = mapped_column(
        String(20),
        nullable=True,
        doc="Required energy level (low, medium, high)"
    )
    
    # Visual and organizational
    color: Mapped[Optional[str]] = mapped_column(
        String(7),  # Hex color code
        nullable=True,
        doc="Color for visual representation"
    )
    
    category: Mapped[Optional[str]] = mapped_column(
        String(50),
        nullable=True,
        doc="Category for grouping and filtering"
    )
    
    # Status and tracking
    status: Mapped[TimeBlockStatus] = mapped_column(
        SQLEnum(TimeBlockStatus),
        nullable=False,
        default=TimeBlockStatus.PLANNED,
        doc="Current block status"
    )
    
    actual_start_time: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        doc="When block actually started"
    )
    
    actual_end_time: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        doc="When block actually ended"
    )
    
    completion_percentage: Mapped[Optional[float]] = mapped_column(
        Float,
        nullable=True,
        doc="Completion percentage (0.0 to 1.0)"
    )
    
    # External calendar integration
    external_calendar_id: Mapped[Optional[str]] = mapped_column(
        String(255),
        nullable=True,
        doc="External calendar event ID"
    )
    
    calendar_source: Mapped[Optional[str]] = mapped_column(
        String(50),
        nullable=True,
        doc="Source calendar (google, outlook, etc.)"
    )
    
    sync_status: Mapped[Optional[str]] = mapped_column(
        String(20),
        nullable=True,
        default="local",
        doc="Synchronization status"
    )
    
    # Metadata and settings
    block_data: Mapped[Dict[str, Any]] = mapped_column(
        JSONB,
        nullable=False,
        default=dict,
        doc="Additional block metadata"
    )
    
    # Relationships
    user: Mapped["User"] = relationship(
        "User",
        back_populates="time_blocks",
        doc="User who owns this time block"
    )
    
    task: Mapped[Optional["Task"]] = relationship(
        "Task",
        back_populates="time_blocks",
        doc="Associated task"
    )

    notifications: Mapped[List["Notification"]] = relationship(
        "Notification",
        back_populates="time_block",
        doc="Notifications associated with this time block"
    )
    
    # Indexes
    __table_args__ = (
        Index("idx_time_blocks_user", "user_id"),
        Index("idx_time_blocks_task", "task_id"),
        Index("idx_time_blocks_time_range", "start_time", "end_time"),
        Index("idx_time_blocks_date", "start_time"),
        Index("idx_time_blocks_type", "block_type"),
        Index("idx_time_blocks_status", "status"),
        Index("idx_time_blocks_external", "external_calendar_id"),
    )
    
    def get_total_duration_with_buffers(self) -> int:
        """Get total duration including buffer times."""
        return self.duration_minutes + self.buffer_before + self.buffer_after
    
    def get_actual_duration(self) -> Optional[timedelta]:
        """Get actual duration if block was completed."""
        if self.actual_start_time and self.actual_end_time:
            return self.actual_end_time - self.actual_start_time
        return None
    
    def overlaps_with(self, other: "TimeBlock") -> bool:
        """Check if this block overlaps with another block."""
        # Include buffer times in overlap calculation
        self_start = self.start_time - timedelta(minutes=self.buffer_before)
        self_end = self.end_time + timedelta(minutes=self.buffer_after)
        other_start = other.start_time - timedelta(minutes=other.buffer_before)
        other_end = other.end_time + timedelta(minutes=other.buffer_after)
        
        return not (self_end <= other_start or other_end <= self_start)
    
    def is_in_past(self) -> bool:
        """Check if block is in the past."""
        return self.end_time < datetime.utcnow()
    
    def is_active_now(self) -> bool:
        """Check if block is currently active."""
        now = datetime.utcnow()
        return self.start_time <= now <= self.end_time
    
    def get_visual_position(self, day_start: datetime, pixels_per_minute: float) -> Dict[str, float]:
        """Calculate visual position for timeline view."""
        minutes_from_start = (self.start_time - day_start).total_seconds() / 60
        height = self.duration_minutes * pixels_per_minute
        
        return {
            "top": minutes_from_start * pixels_per_minute,
            "height": height,
            "buffer_before_height": self.buffer_before * pixels_per_minute,
            "buffer_after_height": self.buffer_after * pixels_per_minute
        }


class ScheduleTemplate(Base, TimestampMixin):
    """
    Schedule template for recurring time block patterns.
    
    Allows users to save and reuse common scheduling patterns
    to reduce decision fatigue and planning overhead.
    """
    
    __tablename__ = "schedule_templates"
    
    # Primary key
    id: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True),
        primary_key=True,
        default=uuid4,
        doc="Unique template identifier"
    )
    
    # User association
    user_id: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="User who owns this template"
    )
    
    # Template identification
    name: Mapped[str] = mapped_column(
        String(100),
        nullable=False,
        doc="Template name"
    )
    
    description: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        doc="Template description"
    )
    
    # Template configuration
    template_type: Mapped[str] = mapped_column(
        String(50),
        nullable=False,
        default="daily",
        doc="Template type (daily, weekly, custom)"
    )
    
    is_default: Mapped[bool] = mapped_column(
        Boolean,
        nullable=False,
        default=False,
        doc="Whether this is a default template"
    )
    
    # Template data
    time_blocks_template: Mapped[List[Dict[str, Any]]] = mapped_column(
        JSONB,
        nullable=False,
        default=list,
        doc="Template time block definitions"
    )
    
    settings: Mapped[Dict[str, Any]] = mapped_column(
        JSONB,
        nullable=False,
        default=dict,
        doc="Template settings and preferences"
    )
    
    # Usage tracking
    usage_count: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        default=0,
        doc="Number of times template has been used"
    )
    
    last_used: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        doc="When template was last used"
    )
    
    # Relationships
    user: Mapped["User"] = relationship(
        "User",
        back_populates="schedule_templates",
        doc="User who owns this template"
    )
    
    # Indexes
    __table_args__ = (
        Index("idx_schedule_templates_user", "user_id"),
        Index("idx_schedule_templates_type", "template_type"),
        Index("idx_schedule_templates_usage", "usage_count"),
    )


class SchedulingPreference(Base, TimestampMixin):
    """
    User preferences for scheduling and time management.
    
    Stores ADHD-specific preferences for automatic scheduling,
    buffer times, and visual representations.
    """
    
    __tablename__ = "scheduling_preferences"
    
    # Primary key
    id: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True),
        primary_key=True,
        default=uuid4,
        doc="Unique preference identifier"
    )
    
    # User association (one-to-one)
    user_id: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        unique=True,
        doc="User these preferences belong to"
    )
    
    # Time preferences
    work_start_time: Mapped[Optional[str]] = mapped_column(
        String(5),  # HH:MM format
        nullable=True,
        default="09:00",
        doc="Preferred work start time"
    )
    
    work_end_time: Mapped[Optional[str]] = mapped_column(
        String(5),  # HH:MM format
        nullable=True,
        default="17:00",
        doc="Preferred work end time"
    )
    
    # Buffer time preferences
    default_buffer_before: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        default=5,
        doc="Default buffer time before events (minutes)"
    )
    
    default_buffer_after: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        default=5,
        doc="Default buffer time after events (minutes)"
    )
    
    meeting_buffer_before: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        default=10,
        doc="Buffer time before meetings (minutes)"
    )
    
    meeting_buffer_after: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        default=10,
        doc="Buffer time after meetings (minutes)"
    )
    
    # Energy and scheduling preferences
    peak_energy_times: Mapped[List[str]] = mapped_column(
        JSONB,
        nullable=False,
        default=list,
        doc="Time ranges when user has peak energy"
    )
    
    low_energy_times: Mapped[List[str]] = mapped_column(
        JSONB,
        nullable=False,
        default=list,
        doc="Time ranges when user has low energy"
    )
    
    preferred_task_duration: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        default=25,
        doc="Preferred task duration in minutes"
    )
    
    max_continuous_work: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        default=90,
        doc="Maximum continuous work time in minutes"
    )
    
    # Visual preferences
    preferred_view: Mapped[str] = mapped_column(
        String(20),
        nullable=False,
        default="timeline",
        doc="Preferred calendar view (timeline, circular, grid)"
    )
    
    color_coding_scheme: Mapped[str] = mapped_column(
        String(20),
        nullable=False,
        default="by_type",
        doc="Color coding preference (by_type, by_priority, by_energy)"
    )
    
    show_buffer_times: Mapped[bool] = mapped_column(
        Boolean,
        nullable=False,
        default=True,
        doc="Whether to show buffer times visually"
    )
    
    # Automation preferences
    auto_schedule_enabled: Mapped[bool] = mapped_column(
        Boolean,
        nullable=False,
        default=False,
        doc="Enable automatic task scheduling"
    )
    
    auto_buffer_enabled: Mapped[bool] = mapped_column(
        Boolean,
        nullable=False,
        default=True,
        doc="Enable automatic buffer time insertion"
    )
    
    conflict_resolution: Mapped[str] = mapped_column(
        String(20),
        nullable=False,
        default="suggest",
        doc="Conflict resolution preference (suggest, auto_move, warn)"
    )
    
    # Additional settings
    settings: Mapped[Dict[str, Any]] = mapped_column(
        JSONB,
        nullable=False,
        default=dict,
        doc="Additional scheduling preferences"
    )
    
    # Relationships
    user: Mapped["User"] = relationship(
        "User",
        back_populates="scheduling_preferences",
        doc="User these preferences belong to"
    )
    
    # Indexes
    __table_args__ = (
        Index("idx_scheduling_preferences_user", "user_id"),
    )
