"""
Gamification models for Project Chronos.

This module defines models for the gamification system including points,
levels, achievements, streaks, and motivation features for ADHD users.
"""

from datetime import datetime, date
from typing import Any, Dict, Optional, List
from uuid import UUID, uuid4

from sqlalchemy import <PERSON>olean, Foreign<PERSON>ey, Integer, String, DateTime, Date, Float, Text, JSON
from sqlalchemy.dialects.postgresql import UUID as PostgresUUID
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.models.base import BaseModel
from app.core.database import Base


class UserGamification(BaseModel):
    """
    User gamification profile tracking points, level, and overall progress.
    
    This model stores the user's overall gamification state including
    total points, current level, and gamification preferences.
    """
    
    __tablename__ = "user_gamification"
    
    # Foreign keys
    user_id: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        unique=True,
        index=True,
        doc="ID of the user this gamification profile belongs to"
    )
    
    # Points and level
    total_points: Mapped[int] = mapped_column(
        Integer,
        default=0,
        nullable=False,
        doc="Total points earned by the user"
    )
    
    level: Mapped[int] = mapped_column(
        Integer,
        default=1,
        nullable=False,
        doc="Current user level based on points"
    )
    
    points_to_next_level: Mapped[int] = mapped_column(
        Integer,
        default=100,
        nullable=False,
        doc="Points needed to reach next level"
    )
    
    # Gamification preferences
    gamification_enabled: Mapped[bool] = mapped_column(
        Boolean,
        default=True,
        nullable=False,
        doc="Whether gamification features are enabled for this user"
    )
    
    celebration_style: Mapped[str] = mapped_column(
        String(20),
        default="moderate",
        nullable=False,
        doc="Celebration intensity: minimal, moderate, enthusiastic"
    )
    
    preferred_rewards: Mapped[Dict[str, Any]] = mapped_column(
        JSON,
        default=dict,
        nullable=False,
        doc="User's preferred reward types and settings"
    )
    
    # Relationships
    user: Mapped["User"] = relationship("User", back_populates="gamification")
    points_awards: Mapped[List["PointsAward"]] = relationship(
        "PointsAward", back_populates="user_gamification", cascade="all, delete-orphan"
    )
    achievements: Mapped[List["UserAchievement"]] = relationship(
        "UserAchievement", back_populates="user_gamification", cascade="all, delete-orphan"
    )
    streaks: Mapped[List["UserStreak"]] = relationship(
        "UserStreak", back_populates="user_gamification", cascade="all, delete-orphan"
    )


class PointsAward(BaseModel):
    """
    Individual points award record for tracking point history.

    This model records each instance of points being awarded to a user,
    including the reason, multipliers, and associated tasks.
    """
    
    __tablename__ = "points_awards"
    
    # Foreign keys
    user_id: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="ID of the user who received the points"
    )
    
    user_gamification_id: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True),
        ForeignKey("user_gamification.id", ondelete="CASCADE"),
        nullable=False,
        doc="ID of the user's gamification profile"
    )
    
    task_id: Mapped[Optional[UUID]] = mapped_column(
        PostgresUUID(as_uuid=True),
        ForeignKey("tasks.id", ondelete="SET NULL"),
        nullable=True,
        doc="Associated task (if applicable)"
    )
    
    # Award details
    points_awarded: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        doc="Number of points awarded"
    )
    
    reason: Mapped[str] = mapped_column(
        String(200),
        nullable=False,
        doc="Reason for the point award"
    )
    
    multiplier: Mapped[float] = mapped_column(
        Float,
        default=1.0,
        nullable=False,
        doc="Multiplier applied to base points"
    )
    
    total_points_after: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        doc="User's total points after this award"
    )
    
    # Metadata
    award_metadata: Mapped[Dict[str, Any]] = mapped_column(
        JSON,
        default=dict,
        nullable=False,
        doc="Additional metadata about the award"
    )
    
    # Relationships
    user_gamification: Mapped["UserGamification"] = relationship(
        "UserGamification", back_populates="points_awards"
    )


class Achievement(BaseModel):
    """
    Achievement definition model for available achievements.

    This model defines the achievements that users can unlock,
    including their requirements and rewards.
    """
    
    __tablename__ = "achievements"
    
    # Achievement details
    achievement_key: Mapped[str] = mapped_column(
        String(50),
        unique=True,
        nullable=False,
        index=True,
        doc="Unique key for the achievement"
    )
    
    name: Mapped[str] = mapped_column(
        String(100),
        nullable=False,
        doc="Display name of the achievement"
    )
    
    description: Mapped[str] = mapped_column(
        Text,
        nullable=False,
        doc="Description of what the achievement requires"
    )
    
    category: Mapped[str] = mapped_column(
        String(30),
        nullable=False,
        index=True,
        doc="Achievement category: task_completion, consistency, focus_time, social, milestone"
    )
    
    # Requirements
    trigger_event: Mapped[str] = mapped_column(
        String(50),
        nullable=False,
        doc="Event that can trigger this achievement"
    )
    
    requirement_data: Mapped[Dict[str, Any]] = mapped_column(
        JSON,
        default=dict,
        nullable=False,
        doc="Data defining the achievement requirements"
    )
    
    # Rewards
    reward_points: Mapped[int] = mapped_column(
        Integer,
        default=0,
        nullable=False,
        doc="Points awarded for unlocking this achievement"
    )
    
    badge_icon: Mapped[str] = mapped_column(
        String(10),
        nullable=False,
        doc="Emoji or icon for the achievement badge"
    )
    
    # Properties
    is_hidden: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        nullable=False,
        doc="Whether this achievement is hidden until unlocked"
    )
    
    is_active: Mapped[bool] = mapped_column(
        Boolean,
        default=True,
        nullable=False,
        doc="Whether this achievement is currently active"
    )
    
    # Relationships
    user_achievements: Mapped[List["UserAchievement"]] = relationship(
        "UserAchievement", back_populates="achievement", cascade="all, delete-orphan"
    )


class UserAchievement(BaseModel):
    """
    User achievement progress and unlocking record.

    This model tracks which achievements users have unlocked
    and their progress toward achievements.
    """
    
    __tablename__ = "user_achievements"
    
    # Foreign keys
    user_id: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="ID of the user"
    )
    
    user_gamification_id: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True),
        ForeignKey("user_gamification.id", ondelete="CASCADE"),
        nullable=False,
        doc="ID of the user's gamification profile"
    )
    
    achievement_id: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True),
        ForeignKey("achievements.id", ondelete="CASCADE"),
        nullable=False,
        doc="ID of the achievement"
    )
    
    # Progress tracking
    is_unlocked: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        nullable=False,
        doc="Whether the achievement has been unlocked"
    )
    
    unlocked_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime,
        nullable=True,
        doc="When the achievement was unlocked"
    )
    
    progress_data: Mapped[Dict[str, Any]] = mapped_column(
        JSON,
        default=dict,
        nullable=False,
        doc="Current progress toward the achievement"
    )
    
    # Relationships
    user_gamification: Mapped["UserGamification"] = relationship(
        "UserGamification", back_populates="achievements"
    )
    achievement: Mapped["Achievement"] = relationship(
        "Achievement", back_populates="user_achievements"
    )


class UserStreak(BaseModel):
    """
    User streak tracking for consistency rewards.

    This model tracks various types of streaks for users,
    with ADHD-friendly flexibility and recovery options.
    """
    
    __tablename__ = "user_streaks"
    
    # Foreign keys
    user_id: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="ID of the user"
    )
    
    user_gamification_id: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True),
        ForeignKey("user_gamification.id", ondelete="CASCADE"),
        nullable=False,
        doc="ID of the user's gamification profile"
    )
    
    # Streak details
    streak_type: Mapped[str] = mapped_column(
        String(30),
        nullable=False,
        index=True,
        doc="Type of streak: daily_tasks, focus_sessions, login, etc."
    )
    
    current_streak: Mapped[int] = mapped_column(
        Integer,
        default=0,
        nullable=False,
        doc="Current streak length"
    )
    
    longest_streak: Mapped[int] = mapped_column(
        Integer,
        default=0,
        nullable=False,
        doc="Longest streak achieved"
    )
    
    last_activity_date: Mapped[Optional[date]] = mapped_column(
        Date,
        nullable=True,
        doc="Date of last activity for this streak"
    )
    
    # ADHD-friendly features
    freeze_count: Mapped[int] = mapped_column(
        Integer,
        default=0,
        nullable=False,
        doc="Number of streak freezes used"
    )
    
    max_freezes: Mapped[int] = mapped_column(
        Integer,
        default=3,
        nullable=False,
        doc="Maximum number of streak freezes allowed"
    )
    
    # Relationships
    user_gamification: Mapped["UserGamification"] = relationship(
        "UserGamification", back_populates="streaks"
    )
