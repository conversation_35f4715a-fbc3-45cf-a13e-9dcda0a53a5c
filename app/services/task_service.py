"""
Task management service for Project Chronos.

This module provides ADHD-optimized task CRUD operations, adaptive filtering,
and integration with AI chunking capabilities.
"""

import logging
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from uuid import UUID
from sqlalchemy import select, and_, or_, func
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload
from app.models.task import Task
from app.models.user import User
from app.schemas.task import TaskCreate, TaskUpdate, TaskFilterRequest
from app.services.ai_service import AIChunkingService
from app.utils.gamification_utils import GamificationIntegration
from app.core.exceptions import TaskNotFoundError, TaskValidationError, AuthorizationError


logger = logging.getLogger(__name__)


class TaskService:
    """
    ADHD-optimized task management service.
    
    Provides comprehensive task operations with features specifically
    designed to combat task paralysis and support neurodivergent users.
    """
    
    def __init__(self):
        """Initialize task service with AI chunking capability."""
        self.ai_service = AIChunkingService()
    
    async def create_task(
        self,
        db: AsyncSession,
        task_data: TaskCreate,
        user_id: UUID
    ) -> Task:
        """
        Create a new task with ADHD-optimized defaults.
        
        Args:
            db: Database session
            task_data: Task creation data
            user_id: ID of the user creating the task
            
        Returns:
            Task: Created task instance
            
        Raises:
            TaskValidationError: If task data is invalid
        """
        # Validate parent task if specified
        if task_data.parent_task_id:
            parent_task = await self.get_task_by_id(db, task_data.parent_task_id, user_id)
            if not parent_task:
                raise TaskValidationError(
                    "parent_task_id",
                    task_data.parent_task_id,
                    "Parent task not found"
                )
        
        # Create task instance
        task = Task(
            title=task_data.title,
            description=task_data.description,
            priority=task_data.priority,
            energy_level=task_data.energy_level,
            estimated_duration=task_data.estimated_duration,
            context_tags=task_data.context_tags,
            due_date=task_data.due_date,
            parent_task_id=task_data.parent_task_id,
            user_id=user_id
        )
        
        db.add(task)
        await db.commit()
        await db.refresh(task)
        
        logger.info(f"Created task {task.id} for user {user_id}: {task.title}")
        return task
    
    async def get_task_by_id(
        self,
        db: AsyncSession,
        task_id: UUID,
        user_id: UUID
    ) -> Optional[Task]:
        """
        Get a task by ID with user authorization check.
        
        Args:
            db: Database session
            task_id: Task ID to retrieve
            user_id: ID of the requesting user
            
        Returns:
            Task: Task instance if found and authorized
            
        Raises:
            TaskNotFoundError: If task doesn't exist
            AuthorizationError: If user doesn't own the task
        """
        stmt = select(Task).where(
            and_(
                Task.id == task_id,
                Task.deleted_at.is_(None)
            )
        ).options(selectinload(Task.subtasks))
        
        result = await db.execute(stmt)
        task = result.scalar_one_or_none()
        
        if not task:
            raise TaskNotFoundError(str(task_id))
        
        if task.user_id != user_id:
            raise AuthorizationError("task", "read")
        
        return task
    
    async def get_user_tasks(
        self,
        db: AsyncSession,
        user_id: UUID,
        filters: Optional[TaskFilterRequest] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[Task]:
        """
        Get user's tasks with adaptive filtering.
        
        Args:
            db: Database session
            user_id: User ID
            filters: Optional filtering criteria
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List[Task]: Filtered and sorted tasks
        """
        # Base query
        stmt = select(Task).where(
            and_(
                Task.user_id == user_id,
                Task.deleted_at.is_(None)
            )
        ).options(selectinload(Task.subtasks))
        
        # Apply filters
        if filters:
            stmt = self._apply_filters(stmt, filters)
        
        # Default ordering: urgency score desc, created_at desc
        stmt = stmt.order_by(Task.created_at.desc())
        
        # Apply pagination
        stmt = stmt.offset(skip).limit(limit)
        
        result = await db.execute(stmt)
        tasks = result.scalars().all()
        
        # Sort by urgency score (computed property)
        tasks_with_scores = [
            (task, task.calculate_urgency_score()) for task in tasks
        ]
        tasks_with_scores.sort(key=lambda x: x[1], reverse=True)
        
        return [task for task, _ in tasks_with_scores]
    
    async def update_task(
        self,
        db: AsyncSession,
        task_id: UUID,
        task_data: TaskUpdate,
        user_id: UUID
    ) -> Task:
        """
        Update an existing task.
        
        Args:
            db: Database session
            task_id: Task ID to update
            task_data: Update data
            user_id: ID of the requesting user
            
        Returns:
            Task: Updated task instance
        """
        task = await self.get_task_by_id(db, task_id, user_id)
        
        # Update fields
        update_data = task_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(task, field, value)
        
        # Handle status changes with gamification
        gamification_results = {}

        if task_data.status == "completed" and task.status != "completed":
            task.mark_completed()

            # Handle gamification for task completion
            try:
                gamification = GamificationIntegration(db)
                gamification_results = await gamification.handle_task_completion(
                    task=task,
                    user_id=user_id,
                    completion_context=getattr(task_data, 'completion_context', {})
                )
                logger.info(f"Gamification results for task {task.id}: {gamification_results}")
            except Exception as e:
                logger.error(f"Gamification error for task {task.id}: {e}")

        elif task_data.status == "in_progress" and task.status == "pending":
            task.start_work()

            # Handle gamification for task start
            try:
                gamification = GamificationIntegration(db)
                start_results = await gamification.handle_task_start(
                    task=task,
                    user_id=user_id,
                    start_context=getattr(task_data, 'start_context', {})
                )
                logger.info(f"Task start gamification for {task.id}: {start_results}")
            except Exception as e:
                logger.error(f"Task start gamification error for task {task.id}: {e}")

        await db.commit()
        await db.refresh(task)

        logger.info(f"Updated task {task.id}: {task.title}")

        # Add gamification results to task object for API response
        if gamification_results:
            task.gamification_results = gamification_results

        return task
    
    async def delete_task(
        self,
        db: AsyncSession,
        task_id: UUID,
        user_id: UUID,
        soft_delete: bool = True
    ) -> bool:
        """
        Delete a task (soft delete by default for ADHD users).
        
        Args:
            db: Database session
            task_id: Task ID to delete
            user_id: ID of the requesting user
            soft_delete: Whether to soft delete (default) or hard delete
            
        Returns:
            bool: True if deletion was successful
        """
        task = await self.get_task_by_id(db, task_id, user_id)
        
        if soft_delete:
            task.soft_delete()
            logger.info(f"Soft deleted task {task.id}: {task.title}")
        else:
            await db.delete(task)
            logger.info(f"Hard deleted task {task.id}: {task.title}")
        
        await db.commit()
        return True
    
    async def chunk_task(
        self,
        db: AsyncSession,
        task_id: UUID,
        user_id: UUID,
        chunk_size: str = "small",
        context: Optional[str] = None,
        user_preferences: Optional[Dict] = None
    ) -> List[Task]:
        """
        Break down a task using AI chunking.
        
        Args:
            db: Database session
            task_id: Task ID to chunk
            user_id: ID of the requesting user
            chunk_size: Size of chunks to create
            context: Additional context for AI
            user_preferences: User-specific preferences
            
        Returns:
            List[Task]: Created subtasks
        """
        # Get the parent task
        parent_task = await self.get_task_by_id(db, task_id, user_id)
        
        # Check if already chunked
        if parent_task.is_chunked:
            logger.warning(f"Task {task_id} is already chunked")
            return parent_task.subtasks
        
        # Generate chunks using AI
        chunks = await self.ai_service.chunk_task(
            title=parent_task.title,
            description=parent_task.description,
            chunk_size=chunk_size,
            context=context,
            user_preferences=user_preferences
        )
        
        # Create subtasks
        subtasks = []
        for chunk_data in chunks:
            subtask = Task(
                title=chunk_data["title"],
                description=chunk_data["description"],
                estimated_duration=chunk_data["estimated_duration"],
                energy_level=chunk_data["energy_level"],
                context_tags=chunk_data["context_tags"],
                parent_task_id=parent_task.id,
                user_id=user_id,
                priority=parent_task.priority,  # Inherit priority
                due_date=parent_task.due_date    # Inherit due date
            )
            db.add(subtask)
            subtasks.append(subtask)
        
        # Mark parent as chunked
        parent_task.is_chunked = True
        parent_task.chunk_size = chunk_size
        parent_task.ai_chunking_metadata = {
            "chunk_count": len(chunks),
            "chunked_at": datetime.utcnow().isoformat(),
            "context": context
        }
        
        await db.commit()
        
        # Refresh all tasks
        await db.refresh(parent_task)
        for subtask in subtasks:
            await db.refresh(subtask)
        
        logger.info(f"Chunked task {task_id} into {len(subtasks)} subtasks")
        return subtasks
    
    def _apply_filters(self, stmt, filters: TaskFilterRequest):
        """Apply filtering criteria to query."""
        conditions = []
        
        # Energy level filter
        if filters.energy_level:
            conditions.append(Task.energy_level == filters.energy_level)
        
        # Duration filter
        if filters.max_duration:
            conditions.append(
                or_(
                    Task.estimated_duration <= filters.max_duration,
                    Task.estimated_duration.is_(None)
                )
            )
        
        # Context tags filter
        if filters.context_tags:
            for tag in filters.context_tags:
                conditions.append(Task.context_tags.contains([tag]))
        
        # Status filter
        if filters.status:
            conditions.append(Task.status.in_(filters.status))
        
        # Priority filter
        if filters.priority:
            conditions.append(Task.priority.in_(filters.priority))
        
        # Overdue filter
        if not filters.include_overdue:
            conditions.append(
                or_(
                    Task.due_date.is_(None),
                    Task.due_date > datetime.utcnow(),
                    Task.status == "completed"
                )
            )
        
        if conditions:
            stmt = stmt.where(and_(*conditions))
        
        return stmt
