"""
Gamification service for Project Chronos.

This module provides ADHD-optimized gamification features including points,
levels, achievements, and streak management.
"""

import logging
import math
from datetime import datetime, timedelta, date
from typing import Dict, List, Optional, Tuple
from uuid import UUID

from sqlalchemy import select, and_, func
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app.models.gamification import (
    UserGamification,
    PointsAward,
    Achievement,
    UserAchievement,
    UserStreak,
)
from app.models.user import User
from app.schemas.gamification import (
    GamificationProfileCreate,
    PointsAwardCreate,
    StreakUpdate,
    LevelUpCelebration,
    GamificationStatsResponse,
)
from app.core.exceptions import GamificationError, UserNotFoundError

logger = logging.getLogger(__name__)


class GamificationService:
    """Service for managing ADHD-optimized gamification features."""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def get_or_create_user_gamification(self, user_id: UUID) -> UserGamification:
        """
        Get or create user gamification profile.
        
        Args:
            user_id: User ID to get/create profile for
            
        Returns:
            UserGamification: User's gamification profile
            
        Raises:
            UserNotFoundError: If user doesn't exist
        """
        # Check if user exists
        user_result = await self.db.execute(
            select(User).where(User.id == user_id)
        )
        user = user_result.scalar_one_or_none()
        if not user:
            raise UserNotFoundError(f"User {user_id} not found")
        
        # Get existing gamification profile
        result = await self.db.execute(
            select(UserGamification).where(UserGamification.user_id == user_id)
        )
        gamification = result.scalar_one_or_none()
        
        if not gamification:
            # Create new gamification profile
            gamification = UserGamification(
                user_id=user_id,
                total_points=0,
                level=1,
                points_to_next_level=100,
                gamification_enabled=True,
                celebration_style="moderate",
                preferred_rewards={}
            )
            self.db.add(gamification)
            await self.db.commit()
            await self.db.refresh(gamification)
            
            logger.info(f"Created gamification profile for user {user_id}")
        
        return gamification
    
    async def award_points(
        self,
        user_id: UUID,
        points: int,
        reason: str,
        task_id: Optional[UUID] = None,
        multiplier: float = 1.0,
        metadata: Optional[Dict] = None
    ) -> Tuple[PointsAward, Optional[LevelUpCelebration]]:
        """
        Award points to user with ADHD-optimized feedback.
        
        Args:
            user_id: User receiving points
            points: Base points to award
            reason: Reason for point award
            task_id: Associated task (if applicable)
            multiplier: Difficulty/energy multiplier
            metadata: Additional metadata
            
        Returns:
            Tuple of (PointsAward, Optional[LevelUpCelebration])
        """
        if metadata is None:
            metadata = {}
        
        final_points = int(points * multiplier)
        
        # Get user gamification profile
        user_gamification = await self.get_or_create_user_gamification(user_id)
        
        # Calculate new totals
        old_total = user_gamification.total_points
        new_total = old_total + final_points
        old_level = user_gamification.level
        new_level = self._calculate_level(new_total)
        
        # Update user gamification record
        user_gamification.total_points = new_total
        user_gamification.level = new_level
        user_gamification.points_to_next_level = self._calculate_points_to_next_level(new_total, new_level)
        
        # Create points award record
        award = PointsAward(
            user_id=user_id,
            user_gamification_id=user_gamification.id,
            task_id=task_id,
            points_awarded=final_points,
            reason=reason,
            multiplier=multiplier,
            total_points_after=new_total,
            award_metadata=metadata
        )
        
        self.db.add(award)
        await self.db.commit()
        await self.db.refresh(award)
        
        # Check for level up celebration
        level_up_celebration = None
        if new_level > old_level:
            level_up_celebration = await self._create_level_up_celebration(
                user_id, old_level, new_level, final_points
            )
            logger.info(f"User {user_id} leveled up from {old_level} to {new_level}")
        
        logger.info(f"Awarded {final_points} points to user {user_id} for: {reason}")
        return award, level_up_celebration
    
    async def calculate_points_multiplier(
        self,
        base_points: int,
        task_difficulty: Optional[str] = None,
        energy_level: Optional[str] = None,
        time_of_day: Optional[str] = None,
        context: Optional[Dict] = None
    ) -> Tuple[float, Dict[str, float]]:
        """
        Calculate ADHD-optimized points multiplier.
        
        Args:
            base_points: Base points for the action
            task_difficulty: Task difficulty (easy/medium/hard)
            energy_level: User's energy level (low/medium/high)
            time_of_day: Time context (morning/afternoon/evening)
            context: Additional context
            
        Returns:
            Tuple of (final_multiplier, breakdown)
        """
        multiplier = 1.0
        breakdown = {"base": 1.0}
        
        # Difficulty multiplier
        if task_difficulty:
            difficulty_multipliers = {
                "easy": 1.0,
                "medium": 1.2,
                "hard": 1.5,
                "overwhelming": 2.0
            }
            diff_mult = difficulty_multipliers.get(task_difficulty, 1.0)
            multiplier *= diff_mult
            breakdown["difficulty"] = diff_mult
        
        # Energy level multiplier (reward low energy efforts more)
        if energy_level:
            energy_multipliers = {
                "low": 1.3,    # Extra reward for pushing through low energy
                "medium": 1.0,
                "high": 0.9    # Slightly less when energy is high
            }
            energy_mult = energy_multipliers.get(energy_level, 1.0)
            multiplier *= energy_mult
            breakdown["energy"] = energy_mult
        
        # Time of day bonus (ADHD users often struggle with certain times)
        if time_of_day:
            time_multipliers = {
                "morning": 1.1,    # Morning productivity bonus
                "afternoon": 1.0,
                "evening": 1.2,    # Evening effort bonus
                "late_night": 1.3  # Extra reward for late night productivity
            }
            time_mult = time_multipliers.get(time_of_day, 1.0)
            multiplier *= time_mult
            breakdown["time_of_day"] = time_mult
        
        # Context-specific bonuses
        if context:
            if context.get("first_task_of_day"):
                multiplier *= 1.2
                breakdown["first_task"] = 1.2
            
            if context.get("after_break"):
                multiplier *= 1.1
                breakdown["after_break"] = 1.1
            
            if context.get("hyperfocus_session"):
                multiplier *= 1.3
                breakdown["hyperfocus"] = 1.3
        
        return multiplier, breakdown
    
    async def update_streak(
        self,
        user_id: UUID,
        streak_type: str,
        action_completed: bool = True
    ) -> Tuple[UserStreak, bool]:
        """
        Update user streak with ADHD-friendly flexibility.
        
        Args:
            user_id: User whose streak to update
            streak_type: Type of streak (daily_tasks, focus_sessions, etc.)
            action_completed: Whether the streak action was completed today
            
        Returns:
            Tuple of (UserStreak, streak_changed)
        """
        # Get user gamification profile
        user_gamification = await self.get_or_create_user_gamification(user_id)
        
        # Get or create streak
        result = await self.db.execute(
            select(UserStreak).where(
                and_(
                    UserStreak.user_id == user_id,
                    UserStreak.streak_type == streak_type
                )
            )
        )
        streak = result.scalar_one_or_none()
        
        if not streak:
            streak = UserStreak(
                user_id=user_id,
                user_gamification_id=user_gamification.id,
                streak_type=streak_type,
                current_streak=0,
                longest_streak=0,
                last_activity_date=None,
                freeze_count=0,
                max_freezes=3
            )
            self.db.add(streak)
        
        today = date.today()
        streak_changed = False
        
        if action_completed:
            if streak.last_activity_date == today:
                # Already completed today, no change
                pass
            elif streak.last_activity_date == today - timedelta(days=1):
                # Continuing streak
                streak.current_streak += 1
                streak.last_activity_date = today
                streak_changed = True
            else:
                # Streak broken, restart
                streak.current_streak = 1
                streak.last_activity_date = today
                streak_changed = True
            
            # Update longest streak if needed
            if streak.current_streak > streak.longest_streak:
                streak.longest_streak = streak.current_streak
        
        await self.db.commit()
        await self.db.refresh(streak)
        
        if streak_changed:
            logger.info(f"Updated {streak_type} streak for user {user_id}: {streak.current_streak}")
        
        return streak, streak_changed
    
    async def get_gamification_stats(self, user_id: UUID) -> GamificationStatsResponse:
        """
        Get comprehensive gamification statistics for user.
        
        Args:
            user_id: User ID to get stats for
            
        Returns:
            GamificationStatsResponse: Complete gamification statistics
        """
        user_gamification = await self.get_or_create_user_gamification(user_id)
        
        # Get achievements count
        achievements_result = await self.db.execute(
            select(func.count(UserAchievement.id)).where(
                and_(
                    UserAchievement.user_id == user_id,
                    UserAchievement.is_unlocked == True
                )
            )
        )
        achievements_unlocked = achievements_result.scalar() or 0
        
        total_achievements_result = await self.db.execute(
            select(func.count(Achievement.id)).where(Achievement.is_active == True)
        )
        total_achievements = total_achievements_result.scalar() or 0
        
        # Get active streaks
        streaks_result = await self.db.execute(
            select(UserStreak).where(
                and_(
                    UserStreak.user_id == user_id,
                    UserStreak.current_streak > 0
                )
            )
        )
        active_streaks = streaks_result.scalars().all()
        
        # Get recent awards
        recent_awards_result = await self.db.execute(
            select(PointsAward)
            .where(PointsAward.user_id == user_id)
            .order_by(PointsAward.created_at.desc())
            .limit(5)
        )
        recent_awards = recent_awards_result.scalars().all()
        
        # Calculate level progress percentage
        current_level_points = self._calculate_points_for_level(user_gamification.level)
        next_level_points = self._calculate_points_for_level(user_gamification.level + 1)
        points_in_current_level = user_gamification.total_points - current_level_points
        points_needed_for_level = next_level_points - current_level_points
        level_progress_percentage = (points_in_current_level / points_needed_for_level) * 100
        
        return GamificationStatsResponse(
            total_points=user_gamification.total_points,
            current_level=user_gamification.level,
            points_to_next_level=user_gamification.points_to_next_level,
            achievements_unlocked=achievements_unlocked,
            total_achievements=total_achievements,
            active_streaks=[],  # Will be populated with proper schema conversion
            recent_awards=[],   # Will be populated with proper schema conversion
            level_progress_percentage=level_progress_percentage
        )
    
    def _calculate_level(self, total_points: int) -> int:
        """Calculate user level based on total points with ADHD-friendly progression."""
        if total_points < 100:
            return 1
        elif total_points < 300:
            return 2
        elif total_points < 600:
            return 3
        else:
            # Logarithmic progression to prevent level stagnation
            return min(50, int(3 + math.log10(total_points - 500)))
    
    def _calculate_points_for_level(self, level: int) -> int:
        """Calculate total points needed to reach a specific level."""
        if level <= 1:
            return 0
        elif level == 2:
            return 100
        elif level == 3:
            return 300
        else:
            # Reverse calculation of logarithmic progression
            return int(500 + (10 ** (level - 3)))
    
    def _calculate_points_to_next_level(self, total_points: int, current_level: int) -> int:
        """Calculate points needed to reach next level."""
        next_level_points = self._calculate_points_for_level(current_level + 1)
        return max(0, next_level_points - total_points)
    
    async def _create_level_up_celebration(
        self,
        user_id: UUID,
        old_level: int,
        new_level: int,
        points_earned: int
    ) -> LevelUpCelebration:
        """Create level up celebration data."""
        celebration_messages = [
            f"🎉 Level Up! You've reached level {new_level}!",
            f"🚀 Amazing! Welcome to level {new_level}!",
            f"⭐ Fantastic progress! Level {new_level} unlocked!",
            f"🎯 You're on fire! Level {new_level} achieved!"
        ]
        
        message = celebration_messages[new_level % len(celebration_messages)]
        
        return LevelUpCelebration(
            old_level=old_level,
            new_level=new_level,
            points_earned=points_earned,
            celebration_message=message,
            rewards_unlocked=[]  # TODO: Implement reward unlocking logic
        )
