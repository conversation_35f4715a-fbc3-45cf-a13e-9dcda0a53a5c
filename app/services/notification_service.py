"""
Notification service for Project Chronos.

This module provides ADHD-optimized notification management including
persistent reminders, context-aware timing, and multi-channel delivery.
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from uuid import UUID

from sqlalchemy import select, and_, func, desc, or_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app.models.notification import (
    Notification,
    NotificationPreference,
    NotificationTemplate,
    NotificationType,
    NotificationPriority,
    NotificationStatus,
)
from app.models.user import User
from app.models.focus_session import FocusSession, FocusSessionStatus
from app.schemas.notification import (
    NotificationCreate,
    NotificationUpdate,
    NotificationAcknowledge,
    NotificationSnooze,
    NotificationStats,
)
from app.core.exceptions import (
    ValidationError,
    UserNotFoundError,
)

logger = logging.getLogger(__name__)


class NotificationService:
    """Service for managing ADHD-optimized notifications and reminders."""
    
    def __init__(self, db: AsyncSession, celery_app=None):
        self.db = db
        self.celery = celery_app
    
    async def create_notification(
        self,
        user_id: UUID,
        notification_data: NotificationCreate
    ) -> Notification:
        """
        Create a new notification with ADHD optimizations.
        
        Args:
            user_id: User to receive the notification
            notification_data: Notification configuration data
            
        Returns:
            Notification: Created notification
            
        Raises:
            UserNotFoundError: If user doesn't exist
            ValidationError: If notification data is invalid
        """
        # Verify user exists
        user_result = await self.db.execute(
            select(User).where(User.id == user_id)
        )
        user = user_result.scalar_one_or_none()
        if not user:
            raise UserNotFoundError(f"User {user_id} not found")
        
        # Get user notification preferences
        prefs = await self._get_user_preferences(user_id)
        
        # Apply user preferences to notification
        delivery_channels = notification_data.delivery_channels
        if prefs:
            # Filter channels based on user preferences
            filtered_channels = []
            for channel in delivery_channels:
                if channel == "push" and prefs.push_enabled:
                    filtered_channels.append(channel)
                elif channel == "email" and prefs.email_enabled:
                    filtered_channels.append(channel)
                elif channel == "sms" and prefs.sms_enabled:
                    filtered_channels.append(channel)
            
            if not filtered_channels:
                # Fallback to push if no channels enabled
                filtered_channels = ["push"]
            
            delivery_channels = filtered_channels
        
        # Check if notification should be batched
        should_batch = (
            prefs and 
            prefs.batch_non_urgent and 
            notification_data.priority in [NotificationPriority.LOW, NotificationPriority.NORMAL]
        )
        
        # Adjust scheduled time if in quiet hours
        scheduled_for = notification_data.scheduled_for
        if prefs and await self._is_in_quiet_hours(scheduled_for, prefs):
            scheduled_for = await self._get_next_available_time(scheduled_for, prefs)
        
        # Create notification
        notification = Notification(
            user_id=user_id,
            task_id=notification_data.task_id,
            time_block_id=notification_data.time_block_id,
            focus_session_id=notification_data.focus_session_id,
            type=notification_data.type,
            title=notification_data.title,
            message=notification_data.message,
            action_text=notification_data.action_text,
            action_url=notification_data.action_url,
            scheduled_for=scheduled_for,
            priority=notification_data.priority,
            delivery_channels=delivery_channels,
            is_persistent=notification_data.is_persistent,
            respect_focus_mode=notification_data.respect_focus_mode,
            max_snooze_count=notification_data.max_snooze_count,
            expires_at=notification_data.expires_at,
            status=NotificationStatus.SCHEDULED,
            notification_data={
                "created_with_adhd_accommodations": True,
                "original_channels": notification_data.delivery_channels,
                "batched": should_batch,
                "quiet_hours_adjusted": scheduled_for != notification_data.scheduled_for
            }
        )
        
        self.db.add(notification)
        await self.db.commit()
        await self.db.refresh(notification)
        
        # Schedule delivery
        if should_batch:
            await self._add_to_batch(notification)
        else:
            await self._schedule_delivery(notification, notification_data.reminder_stages)
        
        logger.info(f"Created notification {notification.id} for user {user_id}")
        return notification
    
    async def acknowledge_notification(
        self,
        user_id: UUID,
        notification_id: UUID,
        acknowledge_data: NotificationAcknowledge
    ) -> Notification:
        """
        Acknowledge a notification to stop persistence.
        
        Args:
            user_id: User acknowledging the notification
            notification_id: Notification to acknowledge
            acknowledge_data: Acknowledgment details
            
        Returns:
            Notification: Updated notification
            
        Raises:
            ValidationError: If notification cannot be acknowledged
        """
        notification = await self._get_user_notification(notification_id, user_id)
        if not notification:
            raise ValidationError("Notification not found")
        
        if notification.acknowledged_at:
            raise ValidationError("Notification already acknowledged")
        
        # Update notification
        notification.status = NotificationStatus.ACKNOWLEDGED
        notification.acknowledged_at = datetime.utcnow()
        notification.acknowledgment_action = acknowledge_data.action
        
        if acknowledge_data.notes:
            notification.notification_data["acknowledgment_notes"] = acknowledge_data.notes
        
        await self.db.commit()
        
        # Cancel any pending escalation tasks
        if self.celery and notification.celery_task_id:
            self.celery.control.revoke(notification.celery_task_id)
        
        logger.info(f"Acknowledged notification {notification_id} for user {user_id}")
        return notification
    
    async def snooze_notification(
        self,
        user_id: UUID,
        notification_id: UUID,
        snooze_data: NotificationSnooze
    ) -> Notification:
        """
        Snooze a notification with ADHD-friendly options.
        
        Args:
            user_id: User snoozing the notification
            notification_id: Notification to snooze
            snooze_data: Snooze configuration
            
        Returns:
            Notification: Updated notification
            
        Raises:
            ValidationError: If notification cannot be snoozed
        """
        notification = await self._get_user_notification(notification_id, user_id)
        if not notification:
            raise ValidationError("Notification not found")
        
        if not notification.can_be_snoozed():
            raise ValidationError("Notification cannot be snoozed")
        
        # Calculate snooze time
        snooze_until = datetime.utcnow() + timedelta(minutes=snooze_data.snooze_duration)
        
        # Update notification
        notification.status = NotificationStatus.SNOOZED
        notification.snoozed_until = snooze_until
        notification.snooze_count += 1
        
        if snooze_data.snooze_reason:
            notification.notification_data["snooze_reasons"] = notification.notification_data.get("snooze_reasons", [])
            notification.notification_data["snooze_reasons"].append({
                "reason": snooze_data.snooze_reason,
                "snoozed_at": datetime.utcnow().isoformat(),
                "duration": snooze_data.snooze_duration
            })
        
        await self.db.commit()
        
        # Schedule redelivery
        if self.celery:
            self.celery.send_task(
                'deliver_notification',
                args=[str(notification.id), "snoozed_redelivery"],
                eta=snooze_until
            )
        
        logger.info(f"Snoozed notification {notification_id} for {snooze_data.snooze_duration} minutes")
        return notification
    
    async def deliver_notification(
        self,
        notification_id: UUID,
        delivery_stage: str = "primary"
    ) -> bool:
        """
        Deliver notification through configured channels.
        
        Args:
            notification_id: Notification to deliver
            delivery_stage: Stage of delivery (primary, reminder, escalation)
            
        Returns:
            bool: Whether delivery was successful
        """
        notification = await self._get_notification(notification_id)
        if not notification:
            logger.error(f"Notification {notification_id} not found for delivery")
            return False
        
        # Check if user is in focus mode
        if notification.respect_focus_mode and notification.priority != NotificationPriority.URGENT:
            if await self._user_in_focus_mode(notification.user_id):
                # Defer non-urgent notifications
                await self._defer_notification(notification, minutes=30)
                return True
        
        # Check if notification is expired
        if notification.is_expired():
            notification.status = NotificationStatus.EXPIRED
            await self.db.commit()
            return False
        
        # Deliver through each channel
        delivery_success = False
        for channel in notification.delivery_channels:
            try:
                if channel == "push":
                    success = await self._send_push_notification(notification)
                elif channel == "email":
                    success = await self._send_email_notification(notification)
                elif channel == "sms":
                    success = await self._send_sms_notification(notification)
                else:
                    success = False
                
                if success:
                    delivery_success = True
                    
            except Exception as e:
                logger.error(f"Failed to deliver notification {notification_id} via {channel}: {e}")
                notification.delivery_errors.append(f"{channel}: {str(e)}")
        
        # Update delivery status
        notification.delivery_attempts += 1
        notification.last_delivery_attempt = datetime.utcnow()
        
        if delivery_success:
            notification.status = NotificationStatus.SENT
            notification.sent_at = datetime.utcnow()
            
            # Schedule persistence check if needed
            if notification.is_persistent:
                escalation_time = notification.get_next_escalation_time()
                if escalation_time and self.celery:
                    task = self.celery.send_task(
                        'check_notification_acknowledgment',
                        args=[str(notification.id)],
                        eta=escalation_time
                    )
                    notification.celery_task_id = task.id
        
        await self.db.commit()
        
        logger.info(f"Delivered notification {notification_id} via {notification.delivery_channels}")
        return delivery_success
    
    async def get_user_notifications(
        self,
        user_id: UUID,
        unread_only: bool = False,
        limit: int = 50,
        offset: int = 0
    ) -> List[Notification]:
        """
        Get notifications for a user with filtering.
        
        Args:
            user_id: User to get notifications for
            unread_only: Whether to return only unread notifications
            limit: Maximum number of notifications to return
            offset: Number of notifications to skip
            
        Returns:
            List[Notification]: User's notifications
        """
        query = select(Notification).where(Notification.user_id == user_id)
        
        if unread_only:
            query = query.where(Notification.acknowledged_at.is_(None))
        
        query = query.order_by(desc(Notification.created_at)).limit(limit).offset(offset)
        
        result = await self.db.execute(query)
        return list(result.scalars().all())
    
    async def get_notification_stats(self, user_id: UUID) -> NotificationStats:
        """
        Get notification statistics for a user.
        
        Args:
            user_id: User to get stats for
            
        Returns:
            NotificationStats: User's notification statistics
        """
        # Get counts by status
        status_counts = {}
        for status in NotificationStatus:
            count_result = await self.db.execute(
                select(func.count(Notification.id)).where(
                    and_(
                        Notification.user_id == user_id,
                        Notification.status == status
                    )
                )
            )
            status_counts[status.value] = count_result.scalar() or 0
        
        # Calculate rates
        total_sent = status_counts.get("sent", 0) + status_counts.get("delivered", 0) + status_counts.get("acknowledged", 0)
        acknowledgment_rate = (
            status_counts.get("acknowledged", 0) / total_sent 
            if total_sent > 0 else 0.0
        )
        
        return NotificationStats(
            total_sent=total_sent,
            total_delivered=status_counts.get("delivered", 0),
            total_acknowledged=status_counts.get("acknowledged", 0),
            total_snoozed=status_counts.get("snoozed", 0),
            total_expired=status_counts.get("expired", 0),
            acknowledgment_rate=acknowledgment_rate,
            average_acknowledgment_time=None,  # Would need time analysis
            most_effective_channels=[],  # Would need channel analysis
            peak_notification_times=[],  # Would need time analysis
            escalation_frequency=0.0  # Would need escalation analysis
        )
    
    async def _get_user_notification(self, notification_id: UUID, user_id: UUID) -> Optional[Notification]:
        """Get notification owned by user."""
        result = await self.db.execute(
            select(Notification).where(
                and_(
                    Notification.id == notification_id,
                    Notification.user_id == user_id
                )
            )
        )
        return result.scalar_one_or_none()
    
    async def _get_notification(self, notification_id: UUID) -> Optional[Notification]:
        """Get notification by ID."""
        result = await self.db.execute(
            select(Notification).where(Notification.id == notification_id)
        )
        return result.scalar_one_or_none()
    
    async def _get_user_preferences(self, user_id: UUID) -> Optional[NotificationPreference]:
        """Get user's notification preferences."""
        result = await self.db.execute(
            select(NotificationPreference).where(NotificationPreference.user_id == user_id)
        )
        return result.scalar_one_or_none()
    
    async def _user_in_focus_mode(self, user_id: UUID) -> bool:
        """Check if user is currently in a focus session."""
        result = await self.db.execute(
            select(FocusSession).where(
                and_(
                    FocusSession.user_id == user_id,
                    FocusSession.status == FocusSessionStatus.ACTIVE
                )
            )
        )
        return result.scalar_one_or_none() is not None
    
    async def _is_in_quiet_hours(self, scheduled_time: datetime, prefs: NotificationPreference) -> bool:
        """Check if scheduled time falls within quiet hours."""
        if not prefs.quiet_hours_start or not prefs.quiet_hours_end:
            return False
        
        # Implementation would check if time falls within quiet hours
        # This is a simplified version
        return False
    
    async def _get_next_available_time(self, scheduled_time: datetime, prefs: NotificationPreference) -> datetime:
        """Get next available time outside quiet hours."""
        # Implementation would calculate next available time
        # This is a simplified version
        return scheduled_time
    
    async def _schedule_delivery(self, notification: Notification, reminder_stages: Optional[List[int]] = None):
        """Schedule notification delivery with optional staggered reminders."""
        if not self.celery:
            return
        
        if reminder_stages:
            # Schedule multiple reminders
            for minutes_before in reminder_stages:
                delivery_time = notification.scheduled_for - timedelta(minutes=minutes_before)
                
                if delivery_time > datetime.utcnow():
                    self.celery.send_task(
                        'deliver_notification',
                        args=[str(notification.id), f"reminder_{minutes_before}min"],
                        eta=delivery_time
                    )
        else:
            # Schedule single notification
            task = self.celery.send_task(
                'deliver_notification',
                args=[str(notification.id), "primary"],
                eta=notification.scheduled_for
            )
            notification.celery_task_id = task.id
    
    async def _add_to_batch(self, notification: Notification):
        """Add notification to batch for later delivery."""
        # Implementation would add to batch queue
        # This is a placeholder
        pass
    
    async def _defer_notification(self, notification: Notification, minutes: int):
        """Defer notification delivery."""
        new_time = datetime.utcnow() + timedelta(minutes=minutes)
        notification.scheduled_for = new_time
        await self.db.commit()
        
        if self.celery:
            self.celery.send_task(
                'deliver_notification',
                args=[str(notification.id), "deferred"],
                eta=new_time
            )
    
    async def _send_push_notification(self, notification: Notification) -> bool:
        """Send push notification."""
        # Implementation would send push notification
        # This is a placeholder
        logger.info(f"Sending push notification: {notification.title}")
        return True
    
    async def _send_email_notification(self, notification: Notification) -> bool:
        """Send email notification."""
        # Implementation would send email
        # This is a placeholder
        logger.info(f"Sending email notification: {notification.title}")
        return True
    
    async def _send_sms_notification(self, notification: Notification) -> bool:
        """Send SMS notification."""
        # Implementation would send SMS
        # This is a placeholder
        logger.info(f"Sending SMS notification: {notification.title}")
        return True
