"""
Time blocking service for Project Chronos.

This service handles time block management, calendar integration, and
ADHD-friendly scheduling features.
"""

import logging
from datetime import datetime, timezone, timedelta
from typing import List, Optional, Dict, Any
from uuid import UUID

from sqlalchemy import select, and_, or_, func
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app.models.time_blocking import TimeBlock, CalendarIntegration
from app.models.user import User
from app.models.task import Task
from app.models.focus import FocusSession
from app.models.body_doubling import BodyDoublingSession
from app.schemas.time_blocking import (
    TimeBlockCreate,
    TimeBlockUpdate,
    TimeBlockCommand,
    CalendarIntegrationCreate,
    CalendarIntegrationUpdate,
    WeeklySchedule,
    SchedulingConflict
)

logger = logging.getLogger(__name__)


class TimeBlockingService:
    """Service for managing time blocks and calendar integration."""
    
    async def create_time_block(
        self,
        db: AsyncSession,
        block_data: TimeBlockCreate,
        user: User
    ) -> TimeBlock:
        """
        Create a new time block.
        
        Args:
            db: Database session
            block_data: Time block creation data
            user: User creating the block
            
        Returns:
            Created time block
        """
        # Calculate duration
        duration_delta = block_data.end_time - block_data.start_time
        duration_minutes = int(duration_delta.total_seconds() / 60)
        
        # Validate associations
        if block_data.task_id:
            task_query = select(Task).where(
                and_(
                    Task.id == block_data.task_id,
                    Task.user_id == user.id
                )
            )
            task_result = await db.execute(task_query)
            task = task_result.scalar_one_or_none()
            if not task:
                raise ValueError("Task not found or not owned by user")
        
        if block_data.focus_session_id:
            focus_query = select(FocusSession).where(
                and_(
                    FocusSession.id == block_data.focus_session_id,
                    FocusSession.user_id == user.id
                )
            )
            focus_result = await db.execute(focus_query)
            focus_session = focus_result.scalar_one_or_none()
            if not focus_session:
                raise ValueError("Focus session not found or not owned by user")
        
        # Check for conflicts
        conflicts = await self.check_scheduling_conflicts(
            db, user, block_data.start_time, block_data.end_time
        )
        
        if conflicts:
            logger.warning(f"Creating time block with {len(conflicts)} conflicts for user {user.id}")
        
        # Create time block
        time_block = TimeBlock(
            user_id=user.id,
            task_id=block_data.task_id,
            focus_session_id=block_data.focus_session_id,
            body_doubling_session_id=block_data.body_doubling_session_id,
            title=block_data.title,
            description=block_data.description,
            block_type=block_data.block_type,
            start_time=block_data.start_time,
            end_time=block_data.end_time,
            duration_minutes=duration_minutes,
            buffer_before=block_data.buffer_before,
            buffer_after=block_data.buffer_after,
            energy_level_required=block_data.energy_level_required,
            flexibility_level=block_data.flexibility_level,
            is_recurring=block_data.is_recurring,
            recurrence_pattern=block_data.recurrence_pattern,
            block_settings=block_data.block_settings,
            reminder_settings=block_data.reminder_settings,
            preparation_notes=block_data.preparation_notes
        )
        
        db.add(time_block)
        await db.commit()
        await db.refresh(time_block)
        
        # Handle recurring blocks
        if block_data.is_recurring and block_data.recurrence_pattern:
            await self.create_recurring_blocks(db, time_block, block_data.recurrence_pattern)
        
        logger.info(f"Created time block {time_block.id} for user {user.id}")
        return time_block
    
    async def get_time_block(
        self,
        db: AsyncSession,
        block_id: UUID,
        user: User
    ) -> Optional[TimeBlock]:
        """
        Get a time block by ID.
        
        Args:
            db: Database session
            block_id: Time block ID
            user: Current user
            
        Returns:
            Time block if found and accessible
        """
        query = select(TimeBlock).where(
            and_(
                TimeBlock.id == block_id,
                TimeBlock.user_id == user.id
            )
        ).options(
            selectinload(TimeBlock.task),
            selectinload(TimeBlock.focus_session),
            selectinload(TimeBlock.body_doubling_session)
        )
        
        result = await db.execute(query)
        return result.scalar_one_or_none()
    
    async def get_user_time_blocks(
        self,
        db: AsyncSession,
        user: User,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        block_type: Optional[str] = None,
        status: Optional[str] = None,
        limit: int = 100,
        offset: int = 0
    ) -> List[TimeBlock]:
        """
        Get user's time blocks with optional filtering.
        
        Args:
            db: Database session
            user: User to get blocks for
            start_date: Optional start date filter
            end_date: Optional end date filter
            block_type: Optional block type filter
            status: Optional status filter
            limit: Maximum number of blocks to return
            offset: Number of blocks to skip
            
        Returns:
            List of time blocks
        """
        query = select(TimeBlock).where(
            TimeBlock.user_id == user.id
        ).options(
            selectinload(TimeBlock.task),
            selectinload(TimeBlock.focus_session),
            selectinload(TimeBlock.body_doubling_session)
        ).order_by(TimeBlock.start_time)
        
        if start_date:
            query = query.where(TimeBlock.start_time >= start_date)
        
        if end_date:
            query = query.where(TimeBlock.end_time <= end_date)
        
        if block_type:
            query = query.where(TimeBlock.block_type == block_type)
        
        if status:
            query = query.where(TimeBlock.status == status)
        
        query = query.limit(limit).offset(offset)
        
        result = await db.execute(query)
        return list(result.scalars().all())
    
    async def update_time_block(
        self,
        db: AsyncSession,
        block_id: UUID,
        block_data: TimeBlockUpdate,
        user: User
    ) -> Optional[TimeBlock]:
        """
        Update a time block.
        
        Args:
            db: Database session
            block_id: Time block ID
            block_data: Update data
            user: Current user
            
        Returns:
            Updated time block
        """
        time_block = await self.get_time_block(db, block_id, user)
        if not time_block:
            return None
        
        # Update fields
        update_data = block_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(time_block, field, value)
        
        # Recalculate duration if times changed
        if 'start_time' in update_data or 'end_time' in update_data:
            duration_delta = time_block.end_time - time_block.start_time
            time_block.duration_minutes = int(duration_delta.total_seconds() / 60)
        
        await db.commit()
        await db.refresh(time_block)
        
        logger.info(f"Updated time block {block_id} for user {user.id}")
        return time_block
    
    async def execute_block_command(
        self,
        db: AsyncSession,
        block_id: UUID,
        command_data: TimeBlockCommand,
        user: User
    ) -> Optional[TimeBlock]:
        """
        Execute a time block command (start, complete, cancel, reschedule).
        
        Args:
            db: Database session
            block_id: Time block ID
            command_data: Command to execute
            user: Current user
            
        Returns:
            Updated time block
        """
        time_block = await self.get_time_block(db, block_id, user)
        if not time_block:
            return None
        
        now = datetime.now(timezone.utc)
        command = command_data.command
        
        if command == "start":
            if time_block.status == "scheduled":
                time_block.status = "active"
                time_block.actual_start_time = now
        
        elif command == "complete":
            if time_block.status in ["scheduled", "active"]:
                time_block.status = "completed"
                time_block.completion_percentage = 100
                time_block.actual_end_time = now
                if not time_block.actual_start_time:
                    time_block.actual_start_time = time_block.start_time
        
        elif command == "cancel":
            time_block.status = "cancelled"
        
        elif command == "reschedule":
            if command_data.new_start_time and command_data.new_end_time:
                time_block.start_time = command_data.new_start_time
                time_block.end_time = command_data.new_end_time
                duration_delta = time_block.end_time - time_block.start_time
                time_block.duration_minutes = int(duration_delta.total_seconds() / 60)
                time_block.status = "rescheduled"
        
        if command_data.notes:
            time_block.completion_notes = command_data.notes
        
        await db.commit()
        await db.refresh(time_block)
        
        logger.info(f"Executed command '{command}' on time block {block_id}")
        return time_block
    
    async def check_scheduling_conflicts(
        self,
        db: AsyncSession,
        user: User,
        start_time: datetime,
        end_time: datetime,
        exclude_block_id: Optional[UUID] = None
    ) -> List[SchedulingConflict]:
        """
        Check for scheduling conflicts with existing time blocks.
        
        Args:
            db: Database session
            user: User to check conflicts for
            start_time: Proposed start time
            end_time: Proposed end time
            exclude_block_id: Block ID to exclude from conflict check
            
        Returns:
            List of scheduling conflicts
        """
        # Query for overlapping blocks
        query = select(TimeBlock).where(
            and_(
                TimeBlock.user_id == user.id,
                TimeBlock.status.in_(["scheduled", "active"]),
                or_(
                    # Block starts during proposed time
                    and_(
                        TimeBlock.start_time >= start_time,
                        TimeBlock.start_time < end_time
                    ),
                    # Block ends during proposed time
                    and_(
                        TimeBlock.end_time > start_time,
                        TimeBlock.end_time <= end_time
                    ),
                    # Block completely contains proposed time
                    and_(
                        TimeBlock.start_time <= start_time,
                        TimeBlock.end_time >= end_time
                    )
                )
            )
        )
        
        if exclude_block_id:
            query = query.where(TimeBlock.id != exclude_block_id)
        
        result = await db.execute(query)
        conflicting_blocks = list(result.scalars().all())
        
        conflicts = []
        for block in conflicting_blocks:
            # Determine conflict severity based on flexibility
            if block.flexibility_level == "rigid":
                severity = "high"
            elif block.flexibility_level == "medium":
                severity = "medium"
            else:
                severity = "low"
            
            conflicts.append(SchedulingConflict(
                conflict_type="time_overlap",
                description=f"Overlaps with '{block.title}' ({block.start_time} - {block.end_time})",
                affected_blocks=[block.id],
                severity=severity,
                suggested_resolution=f"Consider rescheduling or adjusting the time"
            ))
        
        return conflicts
    
    async def get_weekly_schedule(
        self,
        db: AsyncSession,
        user: User,
        week_start: datetime
    ) -> WeeklySchedule:
        """
        Get weekly schedule view with analytics.
        
        Args:
            db: Database session
            user: User to get schedule for
            week_start: Start of the week
            
        Returns:
            Weekly schedule with analytics
        """
        week_end = week_start + timedelta(days=7)
        
        # Get time blocks for the week
        time_blocks = await self.get_user_time_blocks(
            db, user, week_start, week_end
        )
        
        # Calculate analytics
        total_minutes = sum(block.duration_minutes for block in time_blocks)
        total_hours = total_minutes / 60
        
        # Energy distribution
        energy_distribution = {"low": 0, "medium": 0, "high": 0}
        for block in time_blocks:
            energy_distribution[block.energy_level_required] += 1
        
        # Block type distribution
        block_type_distribution = {}
        for block in time_blocks:
            block_type_distribution[block.block_type] = block_type_distribution.get(block.block_type, 0) + 1
        
        # Check for conflicts
        conflicts = []
        for i, block in enumerate(time_blocks):
            block_conflicts = await self.check_scheduling_conflicts(
                db, user, block.start_time, block.end_time, block.id
            )
            conflicts.extend(block_conflicts)
        
        # Generate suggestions
        suggestions = []
        if total_hours > 50:
            suggestions.append("Consider reducing scheduled time to avoid burnout")
        if energy_distribution["high"] > energy_distribution["low"] * 2:
            suggestions.append("Balance high-energy tasks with more low-energy activities")
        if len(conflicts) > 0:
            suggestions.append(f"Resolve {len(conflicts)} scheduling conflicts")
        
        return WeeklySchedule(
            week_start=week_start,
            week_end=week_end,
            time_blocks=[],  # Will be populated with response objects
            total_scheduled_hours=total_hours,
            energy_distribution=energy_distribution,
            block_type_distribution=block_type_distribution,
            conflicts=[conflict.dict() for conflict in conflicts],
            suggestions=suggestions
        )

    async def create_recurring_blocks(
        self,
        db: AsyncSession,
        parent_block: TimeBlock,
        recurrence_pattern: Dict[str, Any]
    ) -> List[TimeBlock]:
        """
        Create recurring time blocks based on pattern.

        Args:
            db: Database session
            parent_block: Parent time block
            recurrence_pattern: Recurrence configuration

        Returns:
            List of created recurring blocks
        """
        recurring_blocks = []

        frequency = recurrence_pattern.get("frequency", "weekly")
        count = recurrence_pattern.get("count", 4)  # Default 4 occurrences
        interval = recurrence_pattern.get("interval", 1)  # Every N periods

        current_start = parent_block.start_time
        current_end = parent_block.end_time

        for i in range(count):
            if frequency == "daily":
                current_start += timedelta(days=interval)
                current_end += timedelta(days=interval)
            elif frequency == "weekly":
                current_start += timedelta(weeks=interval)
                current_end += timedelta(weeks=interval)
            elif frequency == "monthly":
                # Approximate monthly recurrence
                current_start += timedelta(days=30 * interval)
                current_end += timedelta(days=30 * interval)

            # Create recurring block
            recurring_block = TimeBlock(
                user_id=parent_block.user_id,
                task_id=parent_block.task_id,
                focus_session_id=parent_block.focus_session_id,
                body_doubling_session_id=parent_block.body_doubling_session_id,
                title=parent_block.title,
                description=parent_block.description,
                block_type=parent_block.block_type,
                start_time=current_start,
                end_time=current_end,
                duration_minutes=parent_block.duration_minutes,
                buffer_before=parent_block.buffer_before,
                buffer_after=parent_block.buffer_after,
                energy_level_required=parent_block.energy_level_required,
                flexibility_level=parent_block.flexibility_level,
                is_recurring=False,  # Child blocks are not recurring themselves
                parent_block_id=parent_block.id,
                block_settings=parent_block.block_settings,
                reminder_settings=parent_block.reminder_settings,
                preparation_notes=parent_block.preparation_notes
            )

            db.add(recurring_block)
            recurring_blocks.append(recurring_block)

        await db.commit()

        logger.info(f"Created {len(recurring_blocks)} recurring blocks for parent {parent_block.id}")
        return recurring_blocks

    async def delete_time_block(
        self,
        db: AsyncSession,
        block_id: UUID,
        user: User,
        delete_series: bool = False
    ) -> bool:
        """
        Delete a time block.

        Args:
            db: Database session
            block_id: Time block ID
            user: Current user
            delete_series: Whether to delete entire recurring series

        Returns:
            True if deleted successfully
        """
        time_block = await self.get_time_block(db, block_id, user)
        if not time_block:
            return False

        if delete_series and time_block.parent_block_id:
            # Delete entire series
            parent_query = select(TimeBlock).where(
                TimeBlock.id == time_block.parent_block_id
            )
            parent_result = await db.execute(parent_query)
            parent_block = parent_result.scalar_one_or_none()

            if parent_block:
                await db.delete(parent_block)  # Cascade will delete children
        else:
            # Delete single block
            await db.delete(time_block)

        await db.commit()

        logger.info(f"Deleted time block {block_id} (series: {delete_series})")
        return True

    # Calendar Integration Methods

    async def create_calendar_integration(
        self,
        db: AsyncSession,
        integration_data: CalendarIntegrationCreate,
        user: User
    ) -> CalendarIntegration:
        """
        Create a new calendar integration.

        Args:
            db: Database session
            integration_data: Integration creation data
            user: User creating the integration

        Returns:
            Created calendar integration
        """
        # TODO: Encrypt tokens before storing
        integration = CalendarIntegration(
            user_id=user.id,
            provider=integration_data.provider,
            provider_calendar_id=integration_data.provider_calendar_id,
            calendar_name=integration_data.calendar_name,
            sync_direction=integration_data.sync_direction,
            sync_settings=integration_data.sync_settings,
            access_token=integration_data.access_token,  # Should be encrypted
            refresh_token=integration_data.refresh_token  # Should be encrypted
        )

        db.add(integration)
        await db.commit()
        await db.refresh(integration)

        logger.info(f"Created calendar integration {integration.id} for user {user.id}")
        return integration

    async def get_user_calendar_integrations(
        self,
        db: AsyncSession,
        user: User
    ) -> List[CalendarIntegration]:
        """
        Get user's calendar integrations.

        Args:
            db: Database session
            user: User to get integrations for

        Returns:
            List of calendar integrations
        """
        query = select(CalendarIntegration).where(
            CalendarIntegration.user_id == user.id
        ).order_by(CalendarIntegration.created_at)

        result = await db.execute(query)
        return list(result.scalars().all())

    async def sync_calendar_integration(
        self,
        db: AsyncSession,
        integration_id: UUID,
        user: User
    ) -> bool:
        """
        Sync a calendar integration.

        Args:
            db: Database session
            integration_id: Integration ID
            user: Current user

        Returns:
            True if sync was successful
        """
        query = select(CalendarIntegration).where(
            and_(
                CalendarIntegration.id == integration_id,
                CalendarIntegration.user_id == user.id
            )
        )

        result = await db.execute(query)
        integration = result.scalar_one_or_none()

        if not integration:
            return False

        try:
            # TODO: Implement actual calendar sync logic
            # This would involve calling external calendar APIs

            integration.last_sync_at = datetime.now(timezone.utc)
            integration.sync_status = "active"
            integration.error_count = 0
            integration.last_error = None

            await db.commit()

            logger.info(f"Synced calendar integration {integration_id}")
            return True

        except Exception as e:
            integration.sync_status = "error"
            integration.error_count += 1
            integration.last_error = str(e)

            await db.commit()

            logger.error(f"Failed to sync calendar integration {integration_id}: {e}")
            return False
