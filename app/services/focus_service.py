"""
Focus session service for Project Chronos.

This service handles focus session management, including Pomodoro technique,
deep work sessions, and ADHD-specific focus patterns with group synchronization.
"""

import logging
from datetime import datetime, timezone
from typing import List, Optional
from uuid import UUID

from sqlalchemy import select, and_, func
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app.models.focus import FocusSession, FocusSessionParticipant
from app.models.user import User
from app.models.task import Task
from app.models.body_doubling import BodyDoublingSession
from app.schemas.focus import (
    FocusSessionCreate,
    FocusSessionUpdate,
    FocusSessionParticipantCreate,
    FocusSessionParticipantUpdate,
    GroupFocusSessionCreate,
    FocusSessionCommand,
    FocusSessionStats
)

logger = logging.getLogger(__name__)


class FocusSessionService:
    """Service for managing focus sessions and group synchronization."""
    
    async def create_session(
        self,
        db: AsyncSession,
        session_data: FocusSessionCreate,
        user: User
    ) -> FocusSession:
        """
        Create a new focus session.
        
        Args:
            db: Database session
            session_data: Focus session creation data
            user: User creating the session
            
        Returns:
            Created focus session
        """
        # Validate task ownership if task_id provided
        if session_data.task_id:
            task_query = select(Task).where(
                and_(
                    Task.id == session_data.task_id,
                    Task.user_id == user.id
                )
            )
            task_result = await db.execute(task_query)
            task = task_result.scalar_one_or_none()
            if not task:
                raise ValueError("Task not found or not owned by user")
        
        # Validate body doubling session if provided
        if session_data.body_doubling_session_id:
            bd_query = select(BodyDoublingSession).where(
                BodyDoublingSession.id == session_data.body_doubling_session_id
            )
            bd_result = await db.execute(bd_query)
            bd_session = bd_result.scalar_one_or_none()
            if not bd_session:
                raise ValueError("Body doubling session not found")
        
        # Create focus session
        focus_session = FocusSession(
            user_id=user.id,
            task_id=session_data.task_id,
            body_doubling_session_id=session_data.body_doubling_session_id,
            session_type=session_data.session_type,
            title=session_data.title,
            planned_duration=session_data.planned_duration,
            break_duration=session_data.break_duration,
            focus_mode_settings=session_data.focus_mode_settings,
            is_group_session=session_data.is_group_session,
            group_session_leader=user.id if session_data.is_group_session else None
        )
        
        db.add(focus_session)
        await db.flush()
        
        # If it's a group session, add the creator as first participant
        if session_data.is_group_session:
            participant = FocusSessionParticipant(
                focus_session_id=focus_session.id,
                user_id=user.id
            )
            db.add(participant)
        
        await db.commit()
        await db.refresh(focus_session)
        
        logger.info(f"Created focus session {focus_session.id} by user {user.id}")
        return focus_session
    
    async def get_session(
        self,
        db: AsyncSession,
        session_id: UUID,
        user: User
    ) -> Optional[FocusSession]:
        """
        Get a focus session by ID.
        
        Args:
            db: Database session
            session_id: Focus session ID
            user: Current user
            
        Returns:
            Focus session if found and accessible
        """
        query = select(FocusSession).where(
            and_(
                FocusSession.id == session_id,
                FocusSession.user_id == user.id
            )
        ).options(
            selectinload(FocusSession.task),
            selectinload(FocusSession.body_doubling_session)
        )
        
        result = await db.execute(query)
        return result.scalar_one_or_none()
    
    async def get_user_sessions(
        self,
        db: AsyncSession,
        user: User,
        limit: int = 50,
        offset: int = 0,
        status: Optional[str] = None
    ) -> List[FocusSession]:
        """
        Get user's focus sessions.
        
        Args:
            db: Database session
            user: User to get sessions for
            limit: Maximum number of sessions to return
            offset: Number of sessions to skip
            status: Optional status filter
            
        Returns:
            List of focus sessions
        """
        query = select(FocusSession).where(
            FocusSession.user_id == user.id
        ).options(
            selectinload(FocusSession.task),
            selectinload(FocusSession.body_doubling_session)
        ).order_by(FocusSession.created_at.desc())
        
        if status:
            query = query.where(FocusSession.status == status)
        
        query = query.limit(limit).offset(offset)
        
        result = await db.execute(query)
        return list(result.scalars().all())
    
    async def update_session(
        self,
        db: AsyncSession,
        session_id: UUID,
        session_data: FocusSessionUpdate,
        user: User
    ) -> Optional[FocusSession]:
        """
        Update a focus session.
        
        Args:
            db: Database session
            session_id: Focus session ID
            session_data: Update data
            user: Current user
            
        Returns:
            Updated focus session
        """
        session = await self.get_session(db, session_id, user)
        if not session:
            return None
        
        # Update fields
        update_data = session_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(session, field, value)
        
        await db.commit()
        await db.refresh(session)
        
        logger.info(f"Updated focus session {session_id} by user {user.id}")
        return session
    
    async def execute_command(
        self,
        db: AsyncSession,
        session_id: UUID,
        command_data: FocusSessionCommand,
        user: User
    ) -> Optional[FocusSession]:
        """
        Execute a focus session command (start, pause, resume, complete, cancel).
        
        Args:
            db: Database session
            session_id: Focus session ID
            command_data: Command to execute
            user: Current user
            
        Returns:
            Updated focus session
        """
        session = await self.get_session(db, session_id, user)
        if not session:
            return None
        
        now = datetime.now(timezone.utc)
        command = command_data.command
        
        if command == "start":
            if session.status == "planned":
                session.status = "active"
                session.started_at = now
        
        elif command == "pause":
            if session.status == "active":
                session.status = "paused"
                session.paused_at = now
        
        elif command == "resume":
            if session.status == "paused":
                session.status = "active"
                session.paused_at = None
        
        elif command == "complete":
            if session.status in ["active", "paused"]:
                session.status = "completed"
                session.completed_at = now
                if session.started_at:
                    elapsed = (now - session.started_at).total_seconds() / 60
                    session.actual_duration = int(elapsed)
        
        elif command == "cancel":
            session.status = "cancelled"
        
        if command_data.notes:
            session.session_notes = command_data.notes
        
        await db.commit()
        await db.refresh(session)
        
        logger.info(f"Executed command '{command}' on focus session {session_id}")
        return session
    
    async def get_session_stats(
        self,
        db: AsyncSession,
        user: User,
        days: int = 30
    ) -> FocusSessionStats:
        """
        Get focus session statistics for a user.
        
        Args:
            db: Database session
            user: User to get stats for
            days: Number of days to look back
            
        Returns:
            Focus session statistics
        """
        # Calculate date range
        end_date = datetime.now(timezone.utc)
        start_date = end_date.replace(hour=0, minute=0, second=0, microsecond=0)
        from datetime import timedelta
        start_date = start_date - timedelta(days=days)
        
        # Base query for user's sessions in date range
        base_query = select(FocusSession).where(
            and_(
                FocusSession.user_id == user.id,
                FocusSession.created_at >= start_date
            )
        )
        
        # Total sessions
        total_result = await db.execute(
            select(func.count()).select_from(base_query.subquery())
        )
        total_sessions = total_result.scalar() or 0
        
        # Completed sessions
        completed_result = await db.execute(
            select(func.count()).select_from(
                base_query.where(FocusSession.status == "completed").subquery()
            )
        )
        completed_sessions = completed_result.scalar() or 0
        
        # Total focus time (sum of actual durations)
        focus_time_result = await db.execute(
            select(func.sum(FocusSession.actual_duration)).select_from(
                base_query.where(
                    and_(
                        FocusSession.status == "completed",
                        FocusSession.actual_duration.isnot(None)
                    )
                ).subquery()
            )
        )
        total_focus_time = focus_time_result.scalar() or 0
        
        # Calculate derived stats
        completion_rate = (completed_sessions / total_sessions * 100) if total_sessions > 0 else 0
        average_duration = (total_focus_time / completed_sessions) if completed_sessions > 0 else 0
        
        # Hyperfocus sessions
        hyperfocus_result = await db.execute(
            select(func.count()).select_from(
                base_query.where(FocusSession.hyperfocus_detected == True).subquery()
            )
        )
        hyperfocus_sessions = hyperfocus_result.scalar() or 0
        
        # Group sessions
        group_result = await db.execute(
            select(func.count()).select_from(
                base_query.where(FocusSession.is_group_session == True).subquery()
            )
        )
        group_sessions = group_result.scalar() or 0
        
        return FocusSessionStats(
            total_sessions=total_sessions,
            completed_sessions=completed_sessions,
            total_focus_time=total_focus_time,
            average_session_duration=average_duration,
            hyperfocus_sessions=hyperfocus_sessions,
            group_sessions=group_sessions,
            completion_rate=completion_rate,
            most_productive_time=None,  # TODO: Implement time analysis
            favorite_session_type=None  # TODO: Implement type analysis
        )

    async def create_group_session(
        self,
        db: AsyncSession,
        session_data: GroupFocusSessionCreate,
        user: User
    ) -> FocusSession:
        """
        Create a group focus session within a body doubling session.

        Args:
            db: Database session
            session_data: Group focus session creation data
            user: User creating the session

        Returns:
            Created group focus session
        """
        # Validate body doubling session and user participation
        bd_query = select(BodyDoublingSession).where(
            BodyDoublingSession.id == session_data.body_doubling_session_id
        )
        bd_result = await db.execute(bd_query)
        bd_session = bd_result.scalar_one_or_none()

        if not bd_session:
            raise ValueError("Body doubling session not found")

        # Check if user is host or participant
        from app.models.body_doubling import SessionParticipant
        participant_query = select(SessionParticipant).where(
            and_(
                SessionParticipant.session_id == session_data.body_doubling_session_id,
                SessionParticipant.user_id == user.id
            )
        )
        participant_result = await db.execute(participant_query)
        participant = participant_result.scalar_one_or_none()

        if not participant and bd_session.host_user_id != user.id:
            raise ValueError("User is not a participant in this body doubling session")

        # Create the group focus session
        focus_session_create = FocusSessionCreate(
            title=session_data.title,
            session_type=session_data.session_type,
            planned_duration=session_data.planned_duration,
            break_duration=session_data.break_duration,
            focus_mode_settings=session_data.focus_mode_settings,
            is_group_session=True,
            body_doubling_session_id=session_data.body_doubling_session_id
        )

        focus_session = await self.create_session(db, focus_session_create, user)

        # Auto-start if requested
        if session_data.auto_start:
            command = FocusSessionCommand(command="start")
            await self.execute_command(db, focus_session.id, command, user)

        logger.info(f"Created group focus session {focus_session.id} in body doubling session {session_data.body_doubling_session_id}")
        return focus_session

    async def join_group_session(
        self,
        db: AsyncSession,
        session_id: UUID,
        participant_data: FocusSessionParticipantCreate,
        user: User
    ) -> Optional[FocusSessionParticipant]:
        """
        Join a group focus session.

        Args:
            db: Database session
            session_id: Focus session ID to join
            participant_data: Participant data
            user: User joining the session

        Returns:
            Created participant record
        """
        # Get the focus session
        session_query = select(FocusSession).where(
            and_(
                FocusSession.id == session_id,
                FocusSession.is_group_session == True
            )
        )
        session_result = await db.execute(session_query)
        session = session_result.scalar_one_or_none()

        if not session:
            raise ValueError("Group focus session not found")

        # Check if user is already a participant
        existing_query = select(FocusSessionParticipant).where(
            and_(
                FocusSessionParticipant.focus_session_id == session_id,
                FocusSessionParticipant.user_id == user.id,
                FocusSessionParticipant.left_at.is_(None)
            )
        )
        existing_result = await db.execute(existing_query)
        existing = existing_result.scalar_one_or_none()

        if existing:
            raise ValueError("User is already participating in this session")

        # Validate personal task if provided
        if participant_data.personal_task_id:
            task_query = select(Task).where(
                and_(
                    Task.id == participant_data.personal_task_id,
                    Task.user_id == user.id
                )
            )
            task_result = await db.execute(task_query)
            task = task_result.scalar_one_or_none()
            if not task:
                raise ValueError("Personal task not found or not owned by user")

        # Create participant record
        participant = FocusSessionParticipant(
            focus_session_id=session_id,
            user_id=user.id,
            personal_task_id=participant_data.personal_task_id,
            progress_notes=participant_data.progress_notes
        )

        db.add(participant)
        await db.commit()
        await db.refresh(participant)

        logger.info(f"User {user.id} joined group focus session {session_id}")
        return participant
