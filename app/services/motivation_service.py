"""
Motivation service for Project Chronos.

This module provides dopamine menu functionality, motivation insights,
and personalized activity recommendations for ADHD users.
"""

import logging
import random
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from uuid import UUID

from sqlalchemy import select, and_, func, desc
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app.models.motivation import (
    DopamineActivity,
    UserDopaminePreference,
    DopamineActivityCompletion,
    MotivationInsight,
)
from app.schemas.motivation import (
    DopamineMenuRequest,
    DopamineMenuResponse,
    DopamineActivityCompletionCreate,
    CustomActivityRequest,
    MotivationAnalyticsResponse,
)
from app.core.exceptions import MotivationError

logger = logging.getLogger(__name__)


class MotivationService:
    """Service for managing ADHD-optimized motivation features."""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def initialize_default_activities(self) -> None:
        """Initialize default dopamine activities in the database."""
        default_activities = self._get_default_activities()
        
        for activity_data in default_activities:
            # Check if activity already exists
            result = await self.db.execute(
                select(DopamineActivity).where(DopamineActivity.name == activity_data["name"])
            )
            existing = result.scalar_one_or_none()
            
            if not existing:
                activity = DopamineActivity(**activity_data)
                self.db.add(activity)
        
        await self.db.commit()
        logger.info(f"Initialized {len(default_activities)} default dopamine activities")
    
    async def get_dopamine_menu(
        self,
        user_id: UUID,
        request: DopamineMenuRequest
    ) -> DopamineMenuResponse:
        """
        Get personalized dopamine menu activities.
        
        Args:
            user_id: User requesting menu
            request: Menu request parameters
            
        Returns:
            Personalized dopamine menu response
        """
        # Get user preferences
        user_preferences = await self._get_user_preferences(user_id)
        
        # Get suitable activities based on criteria
        suitable_activities = await self._filter_activities(
            energy_level=request.energy_level,
            available_time=request.available_time,
            exclude_categories=request.exclude_categories,
            preferred_categories=request.preferred_categories
        )
        
        # Personalize based on user history
        personalized_activities = await self._personalize_activities(
            user_id, suitable_activities, user_preferences
        )
        
        # Calculate personalization score
        personalization_score = self._calculate_personalization_score(
            user_preferences, len(personalized_activities)
        )
        
        return DopamineMenuResponse(
            activities=personalized_activities[:5],  # Return top 5
            personalization_score=personalization_score,
            context=request.context,
            energy_level=request.energy_level,
            available_time=request.available_time
        )
    
    async def complete_dopamine_activity(
        self,
        completion_data: DopamineActivityCompletionCreate
    ) -> DopamineActivityCompletion:
        """
        Record completion of a dopamine activity.
        
        Args:
            completion_data: Activity completion data
            
        Returns:
            Created completion record
        """
        completion = DopamineActivityCompletion(
            user_id=completion_data.user_id,
            activity_id=completion_data.activity_id,
            task_id=completion_data.task_id,
            actual_duration=completion_data.actual_duration,
            energy_before=completion_data.energy_before,
            energy_after=completion_data.energy_after,
            mood_before=completion_data.mood_before,
            mood_after=completion_data.mood_after,
            helped_with_task=completion_data.helped_with_task,
            satisfaction_rating=completion_data.satisfaction_rating,
            would_do_again=completion_data.would_do_again,
            context=completion_data.context,
            notes=completion_data.notes
        )
        
        self.db.add(completion)
        await self.db.commit()
        await self.db.refresh(completion)
        
        # Update user preferences based on completion
        await self._update_user_preferences(completion)
        
        # Generate insights if needed
        await self._generate_insights(completion_data.user_id)
        
        logger.info(f"Recorded dopamine activity completion for user {completion_data.user_id}")
        return completion
    
    async def create_custom_activity(
        self,
        user_id: UUID,
        activity_request: CustomActivityRequest
    ) -> UserDopaminePreference:
        """
        Create a custom dopamine activity for a user.
        
        Args:
            user_id: User creating the activity
            activity_request: Custom activity data
            
        Returns:
            Created user preference record
        """
        preference = UserDopaminePreference(
            user_id=user_id,
            activity_id=None,  # Custom activity
            preference_type="custom",
            custom_name=activity_request.name,
            custom_description=activity_request.description,
            custom_duration=activity_request.duration,
            custom_category=activity_request.category,
            rating=5,  # Default high rating for custom activities
            times_suggested=0,
            times_completed=0
        )
        
        self.db.add(preference)
        await self.db.commit()
        await self.db.refresh(preference)
        
        logger.info(f"Created custom dopamine activity for user {user_id}: {activity_request.name}")
        return preference
    
    async def get_motivation_analytics(self, user_id: UUID) -> MotivationAnalyticsResponse:
        """
        Get motivation analytics for a user.
        
        Args:
            user_id: User ID
            
        Returns:
            Motivation analytics data
        """
        # Get total completions
        total_completions_result = await self.db.execute(
            select(func.count(DopamineActivityCompletion.id))
            .where(DopamineActivityCompletion.user_id == user_id)
        )
        total_completions = total_completions_result.scalar() or 0
        
        # Get favorite categories
        favorite_categories = await self._get_favorite_categories(user_id)
        
        # Get most effective activities
        effective_activities = await self._get_most_effective_activities(user_id)
        
        # Get energy patterns
        energy_patterns = await self._analyze_energy_patterns(user_id)
        
        # Get mood improvements
        mood_improvements = await self._analyze_mood_improvements(user_id)
        
        # Get completion rates by context
        completion_rates = await self._get_completion_rates_by_context(user_id)
        
        # Get recent insights
        insights_result = await self.db.execute(
            select(MotivationInsight)
            .where(
                and_(
                    MotivationInsight.user_id == user_id,
                    MotivationInsight.is_active == True
                )
            )
            .order_by(desc(MotivationInsight.created_at))
            .limit(5)
        )
        insights = insights_result.scalars().all()
        
        # Generate recommendations
        recommendations = await self._generate_recommendations(user_id)
        
        return MotivationAnalyticsResponse(
            total_activities_completed=total_completions,
            favorite_categories=favorite_categories,
            most_effective_activities=effective_activities,
            energy_patterns=energy_patterns,
            mood_improvements=mood_improvements,
            completion_rate_by_context=completion_rates,
            insights=[],  # Will be converted to proper schema
            recommendations=recommendations
        )
    
    async def _get_user_preferences(self, user_id: UUID) -> List[UserDopaminePreference]:
        """Get user's dopamine activity preferences."""
        result = await self.db.execute(
            select(UserDopaminePreference)
            .options(selectinload(UserDopaminePreference.activity))
            .where(UserDopaminePreference.user_id == user_id)
        )
        return result.scalars().all()
    
    async def _filter_activities(
        self,
        energy_level: str,
        available_time: int,
        exclude_categories: List[str],
        preferred_categories: List[str]
    ) -> List[DopamineActivity]:
        """Filter activities based on criteria."""
        query = select(DopamineActivity).where(
            and_(
                DopamineActivity.is_active == True,
                DopamineActivity.duration_min <= available_time,
                DopamineActivity.energy_requirement <= energy_level
            )
        )
        
        if exclude_categories:
            query = query.where(~DopamineActivity.category.in_(exclude_categories))
        
        if preferred_categories:
            query = query.where(DopamineActivity.category.in_(preferred_categories))
        
        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def _personalize_activities(
        self,
        user_id: UUID,
        activities: List[DopamineActivity],
        preferences: List[UserDopaminePreference]
    ) -> List[DopamineActivity]:
        """Personalize activity list based on user preferences."""
        # Create preference lookup
        pref_lookup = {pref.activity_id: pref for pref in preferences if pref.activity_id}
        
        # Score activities
        scored_activities = []
        for activity in activities:
            score = 1.0  # Base score
            
            if activity.id in pref_lookup:
                pref = pref_lookup[activity.id]
                if pref.preference_type == "liked":
                    score += 0.5
                elif pref.preference_type == "favorite":
                    score += 1.0
                elif pref.preference_type == "disliked":
                    score -= 0.5
                
                # Boost score based on rating
                if pref.rating:
                    score += (pref.rating - 3) * 0.2  # -0.4 to +0.4
                
                # Reduce score if suggested too recently
                if pref.last_suggested and pref.last_suggested > datetime.utcnow() - timedelta(hours=24):
                    score -= 0.3
            
            scored_activities.append((activity, score))
        
        # Sort by score and add some randomness
        scored_activities.sort(key=lambda x: x[1] + random.random() * 0.2, reverse=True)
        
        return [activity for activity, score in scored_activities]
    
    def _calculate_personalization_score(
        self,
        preferences: List[UserDopaminePreference],
        activity_count: int
    ) -> float:
        """Calculate how personalized the suggestions are."""
        if not preferences:
            return 0.0
        
        # Base score on number of preferences and activity variety
        preference_score = min(len(preferences) / 10, 1.0)  # Max at 10 preferences
        activity_score = min(activity_count / 5, 1.0)  # Max at 5 activities
        
        return (preference_score + activity_score) / 2
    
    async def _update_user_preferences(self, completion: DopamineActivityCompletion) -> None:
        """Update user preferences based on activity completion."""
        if not completion.activity_id:
            return
        
        # Get or create preference
        result = await self.db.execute(
            select(UserDopaminePreference).where(
                and_(
                    UserDopaminePreference.user_id == completion.user_id,
                    UserDopaminePreference.activity_id == completion.activity_id
                )
            )
        )
        preference = result.scalar_one_or_none()
        
        if not preference:
            preference = UserDopaminePreference(
                user_id=completion.user_id,
                activity_id=completion.activity_id,
                preference_type="neutral",
                times_suggested=0,
                times_completed=0
            )
            self.db.add(preference)
        
        # Update completion tracking
        preference.times_completed += 1
        preference.last_completed = completion.created_at
        
        # Update preference based on satisfaction
        if completion.satisfaction_rating:
            if completion.satisfaction_rating >= 4:
                preference.preference_type = "liked"
                preference.rating = completion.satisfaction_rating
            elif completion.satisfaction_rating <= 2:
                preference.preference_type = "disliked"
                preference.rating = completion.satisfaction_rating
        
        await self.db.commit()
    
    async def _generate_insights(self, user_id: UUID) -> None:
        """Generate motivation insights for a user."""
        # This is a placeholder for insight generation logic
        # In a full implementation, this would analyze patterns and generate insights
        pass
    
    async def _get_favorite_categories(self, user_id: UUID) -> List[str]:
        """Get user's favorite activity categories."""
        # Placeholder implementation
        return ["movement", "creative", "social"]
    
    async def _get_most_effective_activities(self, user_id: UUID) -> List[DopamineActivity]:
        """Get most effective activities for the user."""
        # Placeholder implementation
        return []
    
    async def _analyze_energy_patterns(self, user_id: UUID) -> Dict[str, any]:
        """Analyze user's energy patterns."""
        # Placeholder implementation
        return {"morning": "high", "afternoon": "medium", "evening": "low"}
    
    async def _analyze_mood_improvements(self, user_id: UUID) -> Dict[str, float]:
        """Analyze mood improvements from activities."""
        # Placeholder implementation
        return {"average_improvement": 2.5, "best_category": "movement"}
    
    async def _get_completion_rates_by_context(self, user_id: UUID) -> Dict[str, float]:
        """Get completion rates by context."""
        # Placeholder implementation
        return {"pre_task": 0.8, "break": 0.9, "reward": 0.95}
    
    async def _generate_recommendations(self, user_id: UUID) -> List[str]:
        """Generate personalized recommendations."""
        return [
            "Try movement activities when energy is low",
            "Creative activities work well for you in the evening",
            "Consider shorter activities during busy periods"
        ]
    
    def _get_default_activities(self) -> List[Dict]:
        """Get default dopamine activities to initialize."""
        return [
            {
                "name": "5-Minute Walk",
                "description": "Take a quick walk around the block or office",
                "category": "movement",
                "duration_min": 3,
                "duration_max": 10,
                "energy_requirement": "low",
                "energy_boost": "medium",
                "requires_equipment": False,
                "requires_space": False,
                "can_do_anywhere": True,
                "tags": ["outdoor", "exercise", "fresh_air"],
                "instructions": "Step outside and walk at a comfortable pace for 5 minutes"
            },
            {
                "name": "Deep Breathing",
                "description": "Practice deep breathing exercises",
                "category": "mental",
                "duration_min": 2,
                "duration_max": 10,
                "energy_requirement": "low",
                "energy_boost": "low",
                "requires_equipment": False,
                "requires_space": False,
                "can_do_anywhere": True,
                "tags": ["relaxation", "mindfulness", "stress_relief"],
                "instructions": "Breathe in for 4 counts, hold for 4, exhale for 6"
            },
            {
                "name": "Quick Stretch",
                "description": "Do some simple stretches",
                "category": "movement",
                "duration_min": 3,
                "duration_max": 15,
                "energy_requirement": "low",
                "energy_boost": "medium",
                "requires_equipment": False,
                "requires_space": True,
                "can_do_anywhere": False,
                "tags": ["flexibility", "movement", "tension_relief"],
                "instructions": "Focus on neck, shoulders, and back stretches"
            },
            {
                "name": "Listen to Favorite Song",
                "description": "Play an upbeat song you love",
                "category": "sensory",
                "duration_min": 3,
                "duration_max": 5,
                "energy_requirement": "low",
                "energy_boost": "high",
                "requires_equipment": True,
                "requires_space": False,
                "can_do_anywhere": True,
                "tags": ["music", "mood_boost", "quick"],
                "instructions": "Choose a song that always makes you feel good"
            },
            {
                "name": "Doodle or Sketch",
                "description": "Draw something simple and fun",
                "category": "creative",
                "duration_min": 5,
                "duration_max": 20,
                "energy_requirement": "medium",
                "energy_boost": "medium",
                "requires_equipment": True,
                "requires_space": False,
                "can_do_anywhere": True,
                "tags": ["art", "creativity", "relaxing"],
                "instructions": "Don't worry about perfection, just enjoy the process"
            }
        ]
