"""Security utilities for authentication and authorization.

This module provides JWT token management, password hashing, and other security
utilities specifically designed for ADHD users who need reliable but not overly
complex authentication systems.
"""

from datetime import datetime, timedelta, timezone
from typing import Any, Dict, Optional, Union

from jose import JWTError, jwt
from passlib.context import Crypt<PERSON>ontext
from pydantic import ValidationError

from app.core.config import settings


class SecurityManager:
    """Centralized security operations for Project Chronos.
    
    Handles JWT token creation/verification, password hashing, and other
    security operations with ADHD-friendly considerations like reasonable
    token expiry times and clear error handling.
    """
    
    def __init__(self) -> None:
        """Initialize security manager with cryptographic contexts."""
        self.pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
        self.secret_key = settings.secret_key
        self.algorithm = settings.algorithm
        self.access_token_expire_minutes = settings.access_token_expire_minutes
        self.refresh_token_expire_days = settings.refresh_token_expire_days
    
    def create_access_token(
        self, 
        data: Dict[str, Any], 
        expires_delta: Optional[timedelta] = None
    ) -> str:
        """Create JWT access token with short expiry for security.
        
        Args:
            data: Token payload data (typically user ID and permissions)
            expires_delta: Custom expiration time, defaults to configured value
            
        Returns:
            Encoded JWT token string
            
        Note:
            Access tokens have short expiry (15 minutes) to balance security
            with ADHD user needs for not being logged out too frequently.
        """
        to_encode = data.copy()
        
        if expires_delta:
            expire = datetime.now(timezone.utc) + expires_delta
        else:
            expire = datetime.now(timezone.utc) + timedelta(
                minutes=self.access_token_expire_minutes
            )

        to_encode.update({
            "exp": expire,
            "type": "access",
            "iat": datetime.now(timezone.utc),
        })
        
        return jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
    
    def create_refresh_token(
        self, 
        data: Dict[str, Any], 
        expires_delta: Optional[timedelta] = None
    ) -> str:
        """Create JWT refresh token with longer expiry.
        
        Args:
            data: Token payload data (typically user ID)
            expires_delta: Custom expiration time, defaults to configured value
            
        Returns:
            Encoded JWT refresh token string
            
        Note:
            Refresh tokens have longer expiry (7 days) to reduce the frequency
            of re-authentication for ADHD users who may forget passwords.
        """
        to_encode = data.copy()
        
        if expires_delta:
            expire = datetime.now(timezone.utc) + expires_delta
        else:
            expire = datetime.now(timezone.utc) + timedelta(
                days=self.refresh_token_expire_days
            )

        to_encode.update({
            "exp": expire,
            "type": "refresh",
            "iat": datetime.now(timezone.utc),
        })
        
        return jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
    
    def verify_token(self, token: str, token_type: str = "access") -> Optional[Dict[str, Any]]:
        """Verify and decode JWT token.
        
        Args:
            token: JWT token string to verify
            token_type: Expected token type ("access" or "refresh")
            
        Returns:
            Decoded token payload if valid, None if invalid
            
        Raises:
            JWTError: If token is malformed or signature is invalid
            ValidationError: If token payload is invalid
        """
        try:
            payload = jwt.decode(
                token, 
                self.secret_key, 
                algorithms=[self.algorithm]
            )
            
            # Verify token type
            if payload.get("type") != token_type:
                return None
                
            # Check expiration
            exp = payload.get("exp")
            if exp is None or datetime.fromtimestamp(exp, timezone.utc) < datetime.now(timezone.utc):
                return None
                
            return payload
            
        except (JWTError, ValidationError):
            return None
    
    def hash_password(self, password: str) -> str:
        """Hash password using bcrypt with salt.
        
        Args:
            password: Plain text password to hash
            
        Returns:
            Hashed password string
            
        Note:
            Uses bcrypt which is specifically designed to be slow to
            prevent brute force attacks while remaining user-friendly.
        """
        return self.pwd_context.hash(password)
    
    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """Verify password against hash.
        
        Args:
            plain_password: Plain text password to verify
            hashed_password: Stored hashed password
            
        Returns:
            True if password matches, False otherwise
        """
        return self.pwd_context.verify(plain_password, hashed_password)
    
    def generate_password_reset_token(self, email: str) -> str:
        """Generate password reset token for email recovery.
        
        Args:
            email: User email address
            
        Returns:
            JWT token for password reset (1 hour expiry)
            
        Note:
            Reset tokens have short expiry (1 hour) for security but
            long enough for ADHD users to complete the reset process.
        """
        delta = timedelta(hours=1)
        now = datetime.now(timezone.utc)
        expires = now + delta

        data = {
            "exp": expires,
            "iat": now,
            "email": email,
            "type": "password_reset"
        }
        
        return jwt.encode(data, self.secret_key, algorithm=self.algorithm)
    
    def verify_password_reset_token(self, token: str) -> Optional[str]:
        """Verify password reset token and extract email.
        
        Args:
            token: Password reset token to verify
            
        Returns:
            Email address if token is valid, None otherwise
        """
        try:
            payload = jwt.decode(
                token, 
                self.secret_key, 
                algorithms=[self.algorithm]
            )
            
            if payload.get("type") != "password_reset":
                return None
                
            email = payload.get("email")
            if not email:
                return None
                
            return email
            
        except (JWTError, ValidationError):
            return None


# Global security manager instance
security = SecurityManager()
