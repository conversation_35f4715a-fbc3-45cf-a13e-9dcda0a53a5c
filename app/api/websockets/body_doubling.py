"""
WebSocket endpoints for body doubling sessions.

This module provides WebSocket connections for real-time body doubling
sessions, participant coordination, and live progress sharing.
"""

import asyncio
import json
import logging
from typing import Dict, Optional
from uuid import UUID

from fastapi import WebSocket, WebSocketDisconnect, Depends
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.websocket_manager import connection_manager
from app.middleware.websocket_auth import websocket_auth
from app.services.body_doubling_service import BodyDoublingService
from app.schemas.body_doubling import (
    WebSocketMessage,
    ParticipantStatusUpdate,
    TaskProgressUpdate,
    EncouragementMessage
)
from app.models.user import User

logger = logging.getLogger(__name__)


class BodyDoublingWebSocketManager:
    """
    WebSocket manager for body doubling sessions.
    
    Handles real-time communication, participant coordination,
    and progress sharing for virtual body doubling sessions.
    """
    
    def __init__(self):
        self.service = BodyDoublingService()
    
    async def handle_connection(
        self,
        websocket: WebSocket,
        session_id: UUID,
        token: Optional[str] = None
    ):
        """
        Handle a new WebSocket connection for body doubling.
        
        Args:
            websocket: WebSocket connection
            session_id: Body doubling session ID
            token: JWT authentication token
        """
        # Authenticate user
        user = await websocket_auth.authenticate(websocket, token)
        if not user:
            return
        
        # Validate session access
        async for db in get_db():
            try:
                has_access = await websocket_auth.validate_session_access(
                    user, session_id, db
                )
                if not has_access:
                    await websocket.close(code=4003, reason="Access denied to session")
                    return
                
                # Connect to WebSocket manager
                await connection_manager.connect(websocket, user.id, session_id)
                
                # Send initial session state
                await self.send_session_state(user.id, session_id, db)
                
                # Handle messages
                await self.handle_messages(websocket, user, session_id, db)
                
            except Exception as e:
                logger.error(f"Error in body doubling WebSocket connection: {e}")
                await websocket.close(code=4000, reason="Internal server error")
            finally:
                await connection_manager.disconnect(user.id)
                await db.close()
    
    async def handle_messages(
        self,
        websocket: WebSocket,
        user: User,
        session_id: UUID,
        db: AsyncSession
    ):
        """
        Handle incoming WebSocket messages.
        
        Args:
            websocket: WebSocket connection
            user: Authenticated user
            session_id: Session ID
            db: Database session
        """
        try:
            while True:
                # Receive message
                data = await websocket.receive_text()
                
                try:
                    message_data = json.loads(data)
                    message_type = message_data.get("type")
                    
                    # Route message based on type
                    if message_type == "heartbeat":
                        await self.handle_heartbeat(user.id)
                    elif message_type == "status_update":
                        await self.handle_status_update(user, session_id, message_data, db)
                    elif message_type == "task_progress":
                        await self.handle_task_progress(user, session_id, message_data, db)
                    elif message_type == "encouragement":
                        await self.handle_encouragement(user, session_id, message_data, db)
                    elif message_type == "chat_message":
                        await self.handle_chat_message(user, session_id, message_data, db)
                    else:
                        logger.warning(f"Unknown message type: {message_type}")
                
                except json.JSONDecodeError:
                    logger.warning(f"Invalid JSON received from user {user.id}")
                except Exception as e:
                    logger.error(f"Error handling message from user {user.id}: {e}")
        
        except WebSocketDisconnect:
            logger.info(f"User {user.id} disconnected from session {session_id}")
        except Exception as e:
            logger.error(f"WebSocket error for user {user.id}: {e}")
    
    async def send_session_state(
        self,
        user_id: UUID,
        session_id: UUID,
        db: AsyncSession
    ):
        """
        Send current session state to a user.
        
        Args:
            user_id: User ID
            session_id: Session ID
            db: Database session
        """
        try:
            # Get session with participants
            session = await self.service.get_session_by_id(
                db, session_id, include_participants=True
            )
            
            if not session:
                return
            
            # Prepare session state data
            participants_data = []
            for participant in session.participants:
                if participant.is_active():
                    participants_data.append({
                        "user_id": str(participant.user_id),
                        "status": participant.status,
                        "anonymous_mode": participant.anonymous_mode,
                        "joined_at": participant.joined_at.isoformat()
                    })
            
            # Send session state
            message = WebSocketMessage(
                type="session_state",
                data={
                    "session_id": str(session_id),
                    "status": session.status,
                    "participants": participants_data,
                    "current_participants": session.current_participants,
                    "max_participants": session.max_participants
                }
            )
            
            await connection_manager.send_personal_message(user_id, message)
            
        except Exception as e:
            logger.error(f"Error sending session state to user {user_id}: {e}")
    
    async def handle_heartbeat(self, user_id: UUID):
        """
        Handle heartbeat message.
        
        Args:
            user_id: User ID
        """
        await connection_manager.handle_heartbeat(user_id)
        
        # Send heartbeat response
        message = WebSocketMessage(
            type="heartbeat_ack",
            data={"status": "ok"}
        )
        await connection_manager.send_personal_message(user_id, message)
    
    async def handle_status_update(
        self,
        user: User,
        session_id: UUID,
        message_data: Dict,
        db: AsyncSession
    ):
        """
        Handle participant status update.
        
        Args:
            user: User updating status
            session_id: Session ID
            message_data: Message data
            db: Database session
        """
        try:
            # Validate status update
            status_update = ParticipantStatusUpdate(**message_data.get("data", {}))
            
            # Update participant status in database
            # (Implementation would update the participant record)
            
            # Broadcast status change to session
            message = WebSocketMessage(
                type="participant_status_changed",
                data={
                    "user_id": str(user.id),
                    "status": status_update.status,
                    "timestamp": status_update.timestamp.isoformat()
                }
            )
            
            await connection_manager.broadcast_to_session(
                session_id, message, exclude_user=user.id
            )
            
        except Exception as e:
            logger.error(f"Error handling status update: {e}")
    
    async def handle_task_progress(
        self,
        user: User,
        session_id: UUID,
        message_data: Dict,
        db: AsyncSession
    ):
        """
        Handle task progress update.
        
        Args:
            user: User sharing progress
            session_id: Session ID
            message_data: Message data
            db: Database session
        """
        try:
            # Validate progress update
            progress_update = TaskProgressUpdate(**message_data.get("data", {}))
            
            # Check if user allows progress sharing
            # (Implementation would check participant settings)
            
            # Broadcast progress to session
            message = WebSocketMessage(
                type="task_progress",
                data={
                    "user_id": str(user.id),
                    "task_id": str(progress_update.task_id),
                    "progress_type": progress_update.progress_type,
                    "progress_data": progress_update.progress_data,
                    "timestamp": progress_update.timestamp.isoformat()
                }
            )
            
            await connection_manager.broadcast_to_session(
                session_id, message, exclude_user=user.id
            )
            
        except Exception as e:
            logger.error(f"Error handling task progress: {e}")
    
    async def handle_encouragement(
        self,
        user: User,
        session_id: UUID,
        message_data: Dict,
        db: AsyncSession
    ):
        """
        Handle encouragement message.
        
        Args:
            user: User sending encouragement
            session_id: Session ID
            message_data: Message data
            db: Database session
        """
        try:
            # Validate encouragement
            encouragement = EncouragementMessage(**message_data.get("data", {}))
            
            # Prepare encouragement message
            message = WebSocketMessage(
                type="encouragement",
                data={
                    "from_user_id": str(user.id),
                    "target_user_id": str(encouragement.target_participant_id) if encouragement.target_participant_id else None,
                    "encouragement_type": encouragement.encouragement_type,
                    "message": encouragement.message,
                    "emoji": encouragement.emoji
                }
            )
            
            # Send to specific user or broadcast to session
            if encouragement.target_participant_id:
                await connection_manager.send_personal_message(
                    encouragement.target_participant_id, message
                )
            else:
                await connection_manager.broadcast_to_session(
                    session_id, message, exclude_user=user.id
                )
            
        except Exception as e:
            logger.error(f"Error handling encouragement: {e}")
    
    async def handle_chat_message(
        self,
        user: User,
        session_id: UUID,
        message_data: Dict,
        db: AsyncSession
    ):
        """
        Handle chat message.
        
        Args:
            user: User sending message
            session_id: Session ID
            message_data: Message data
            db: Database session
        """
        try:
            content = message_data.get("data", {}).get("content", "")
            if not content or len(content) > 1000:
                return
            
            # Store message in database
            # (Implementation would save to SessionMessage table)
            
            # Broadcast to session
            message = WebSocketMessage(
                type="chat_message",
                data={
                    "from_user_id": str(user.id),
                    "content": content,
                    "timestamp": message_data.get("timestamp")
                }
            )
            
            await connection_manager.broadcast_to_session(
                session_id, message, exclude_user=user.id
            )
            
        except Exception as e:
            logger.error(f"Error handling chat message: {e}")


# Global WebSocket manager instance
body_doubling_manager = BodyDoublingWebSocketManager()


async def websocket_endpoint(websocket: WebSocket, session_id: str, token: str = None):
    """
    WebSocket endpoint for body doubling sessions.
    
    Args:
        websocket: WebSocket connection
        session_id: Body doubling session ID
        token: JWT authentication token
    """
    try:
        session_uuid = UUID(session_id)
        await body_doubling_manager.handle_connection(websocket, session_uuid, token)
    except ValueError:
        await websocket.close(code=4000, reason="Invalid session ID")
    except Exception as e:
        logger.error(f"WebSocket endpoint error: {e}")
        await websocket.close(code=4000, reason="Internal server error")
