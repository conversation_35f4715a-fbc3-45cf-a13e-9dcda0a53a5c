"""
Time blocking API endpoints for Project Chronos.

This module provides ADHD-optimized time blocking endpoints including
visual time management, scheduling, and calendar integration.
"""

import logging
from datetime import datetime, timedelta
from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.auth import get_current_user
from app.models.user import User
from app.schemas.time_block import (
    TimeBlockCreate,
    TimeBlockUpdate,
    TimeBlockResponse,
    TimeBlockMove,
    DailyScheduleRequest,
    DailyScheduleResponse,
    ScheduleValidationResponse,
    TimeSlotSuggestion,
    CircularCalendarView,
    TimelineView,
    SchedulingPreferencesCreate,
    SchedulingPreferencesUpdate,
    SchedulingPreferencesResponse,
)
from app.services.time_service import TimeBlockingService, TimeVisualizationService
from app.core.exceptions import (
    ValidationError,
    UserNotFoundError,
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/time-blocks", tags=["time-blocking"])


@router.post("/", response_model=TimeBlockResponse)
async def create_time_block(
    block_data: TimeBlockCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Create a new ADHD-optimized time block.
    
    Creates a time block with automatic buffer time insertion,
    conflict detection, and ADHD accommodations.
    """
    try:
        time_service = TimeBlockingService(db)
        time_block = await time_service.create_time_block(current_user.id, block_data)
        
        # Convert to response format with calculated fields
        response_data = TimeBlockResponse.model_validate(time_block)
        response_data.total_duration_with_buffers = time_block.get_total_duration_with_buffers()
        response_data.is_in_past = time_block.is_in_past()
        response_data.is_active_now = time_block.is_active_now()
        
        return response_data
        
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error creating time block: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create time block"
        )


@router.get("/daily/{date}", response_model=DailyScheduleResponse)
async def get_daily_schedule(
    date: datetime,
    view_type: str = Query("timeline", description="View type (timeline, circular)"),
    include_buffers: bool = Query(True, description="Include buffer time blocks"),
    include_completed: bool = Query(True, description="Include completed blocks"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get daily schedule with ADHD-friendly visual representations.
    
    Returns time blocks for the specified date with visual positioning
    data for timeline or circular clock views.
    """
    try:
        time_service = TimeBlockingService(db)
        viz_service = TimeVisualizationService()
        
        # Get time blocks for the day
        time_blocks = await time_service.get_daily_schedule(
            current_user.id, date, include_buffers, include_completed
        )
        
        # Convert to response format
        block_responses = []
        for block in time_blocks:
            response = TimeBlockResponse.model_validate(block)
            response.total_duration_with_buffers = block.get_total_duration_with_buffers()
            response.is_in_past = block.is_in_past()
            response.is_active_now = block.is_active_now()
            
            # Add visual positioning for timeline view
            if view_type == "timeline":
                day_start = date.replace(hour=6, minute=0, second=0, microsecond=0)
                response.visual_position = block.get_visual_position(day_start, 1.0)
            
            block_responses.append(response)
        
        # Calculate schedule metrics
        total_scheduled = sum(block.duration_minutes for block in time_blocks)
        total_available = 16 * 60  # 16 hours available by default
        utilization = (total_scheduled / total_available) * 100 if total_available > 0 else 0
        
        # Generate view-specific data
        view_data = {}
        if view_type == "circular":
            circular_view = viz_service.generate_circular_view(time_blocks, date)
            view_data = circular_view.model_dump()
        elif view_type == "timeline":
            timeline_view = viz_service.generate_timeline_view(time_blocks, date)
            view_data = timeline_view.model_dump()
        
        # Validate schedule and get suggestions
        validation = await time_service.validate_schedule(current_user.id, date)
        
        return DailyScheduleResponse(
            date=date,
            time_blocks=block_responses,
            total_scheduled_time=total_scheduled,
            total_available_time=total_available,
            utilization_percentage=utilization,
            conflicts=[conflict.model_dump() for conflict in validation.conflicts],
            suggestions=validation.suggestions,
            view_data=view_data
        )
        
    except Exception as e:
        logger.error(f"Error getting daily schedule: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get daily schedule"
        )


@router.post("/validate/{date}", response_model=ScheduleValidationResponse)
async def validate_daily_schedule(
    date: datetime,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Validate daily schedule for ADHD-specific issues.
    
    Checks for over-scheduling, conflicts, missing breaks,
    and provides ADHD-friendly suggestions for improvement.
    """
    try:
        time_service = TimeBlockingService(db)
        validation = await time_service.validate_schedule(current_user.id, date)
        
        return validation
        
    except Exception as e:
        logger.error(f"Error validating schedule: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to validate schedule"
        )


@router.put("/{block_id}/move", response_model=TimeBlockResponse)
async def move_time_block(
    block_id: UUID,
    move_data: TimeBlockMove,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Move a time block to a new time slot.
    
    ADHD-friendly block movement with automatic conflict detection
    and flexible duration adjustment.
    """
    try:
        time_service = TimeBlockingService(db)
        time_block = await time_service.move_time_block(
            block_id, current_user.id, move_data
        )
        
        response_data = TimeBlockResponse.model_validate(time_block)
        response_data.total_duration_with_buffers = time_block.get_total_duration_with_buffers()
        response_data.is_in_past = time_block.is_in_past()
        response_data.is_active_now = time_block.is_active_now()
        
        return response_data
        
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error moving time block: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to move time block"
        )


@router.get("/{block_id}", response_model=TimeBlockResponse)
async def get_time_block(
    block_id: UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get detailed information about a specific time block.
    
    Returns time block details with ADHD-relevant metrics
    and visual positioning information.
    """
    try:
        time_service = TimeBlockingService(db)
        
        # This would be implemented in the service
        # For now, return a basic response
        raise HTTPException(
            status_code=status.HTTP_501_NOT_IMPLEMENTED,
            detail="Get single time block not yet implemented"
        )
        
    except Exception as e:
        logger.error(f"Error getting time block: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get time block"
        )


@router.put("/{block_id}", response_model=TimeBlockResponse)
async def update_time_block(
    block_id: UUID,
    update_data: TimeBlockUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Update a time block with ADHD accommodations.
    
    Allows flexible updates while maintaining schedule integrity
    and providing helpful conflict warnings.
    """
    try:
        # This would be implemented in the service
        raise HTTPException(
            status_code=status.HTTP_501_NOT_IMPLEMENTED,
            detail="Update time block not yet implemented"
        )
        
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.delete("/{block_id}")
async def delete_time_block(
    block_id: UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Delete a time block.
    
    ADHD-friendly deletion with confirmation and impact assessment.
    """
    try:
        # This would be implemented in the service
        raise HTTPException(
            status_code=status.HTTP_501_NOT_IMPLEMENTED,
            detail="Delete time block not yet implemented"
        )
        
    except Exception as e:
        logger.error(f"Error deleting time block: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete time block"
        )


@router.get("/suggestions/{date}", response_model=List[TimeSlotSuggestion])
async def get_time_slot_suggestions(
    date: datetime,
    task_id: UUID = Query(..., description="Task to find time slots for"),
    duration: int = Query(..., ge=5, le=480, description="Required duration in minutes"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get ADHD-optimized time slot suggestions for a task.
    
    Provides intelligent suggestions based on energy patterns,
    existing schedule, and task requirements.
    """
    try:
        time_service = TimeBlockingService(db)
        
        # Get the task (this would need to be implemented)
        # For now, create a mock task
        from app.models.task import Task
        task = Task(
            id=task_id,
            title="Mock Task",
            energy_level="medium",
            complexity="medium"
        )
        
        suggestions = await time_service.suggest_optimal_times(
            current_user.id, task, date, duration
        )
        
        return suggestions
        
    except Exception as e:
        logger.error(f"Error getting time slot suggestions: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get time slot suggestions"
        )


@router.get("/views/circular/{date}", response_model=CircularCalendarView)
async def get_circular_view(
    date: datetime,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get circular clock view for ADHD time visualization.
    
    Provides a 24-hour circular representation that makes
    time proportions visually intuitive for time-blind users.
    """
    try:
        time_service = TimeBlockingService(db)
        viz_service = TimeVisualizationService()
        
        time_blocks = await time_service.get_daily_schedule(current_user.id, date)
        circular_view = viz_service.generate_circular_view(time_blocks, date)
        
        return circular_view
        
    except Exception as e:
        logger.error(f"Error generating circular view: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate circular view"
        )


@router.get("/views/timeline/{date}", response_model=TimelineView)
async def get_timeline_view(
    date: datetime,
    hour_height: float = Query(60, ge=30, le=120, description="Height per hour in pixels"),
    start_hour: int = Query(6, ge=0, le=23, description="Timeline start hour"),
    end_hour: int = Query(22, ge=1, le=24, description="Timeline end hour"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get timeline view for ADHD-friendly schedule visualization.
    
    Provides a linear timeline with drag-and-drop positioning
    data and visual accommodations for executive dysfunction.
    """
    try:
        time_service = TimeBlockingService(db)
        viz_service = TimeVisualizationService()
        
        time_blocks = await time_service.get_daily_schedule(current_user.id, date)
        timeline_view = viz_service.generate_timeline_view(
            time_blocks, date, hour_height, start_hour, end_hour
        )
        
        return timeline_view
        
    except Exception as e:
        logger.error(f"Error generating timeline view: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate timeline view"
        )
