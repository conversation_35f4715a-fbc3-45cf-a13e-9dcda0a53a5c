"""
Integration API endpoints for Project Chronos.

This module provides REST API endpoints for managing external service
integrations, OAuth flows, and synchronization operations.
"""

import logging
from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query, Request, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.dependencies import get_current_user, get_db
from app.models.user import User
from app.models.integration import IntegrationType, IntegrationStatus, SyncStatus
from app.schemas.integration import (
    IntegrationCreate,
    IntegrationUpdate,
    IntegrationResponse,
    SyncRequest,
    SyncResponse,
    SyncLogResponse,
    IntegrationStats,
    IntegrationHealth,
    BulkSyncRequest,
    BulkSyncResponse,
    OAuthInitiate,
    OAuthCallback,
    WebhookEventCreate,
    WebhookEventResponse,
)
from app.services.integration_service import IntegrationService
from app.core.exceptions import (
    NotFoundError,
    ValidationError,
    IntegrationError,
    ConflictError,
)

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/", response_model=List[IntegrationResponse])
async def get_user_integrations(
    integration_type: Optional[IntegrationType] = Query(
        None,
        description="Filter by integration type"
    ),
    status: Optional[IntegrationStatus] = Query(
        None,
        description="Filter by integration status"
    ),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get user's external service integrations.
    
    Returns a list of all integrations configured by the user,
    with optional filtering by type and status.
    """
    
    service = IntegrationService(db)
    
    try:
        integrations = await service.get_user_integrations(
            user_id=current_user.id,
            integration_type=integration_type,
            status=status
        )
        
        return integrations
        
    except Exception as e:
        logger.error(f"Error getting integrations for user {current_user.id}: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to retrieve integrations"
        )


@router.post("/", response_model=IntegrationResponse)
async def create_integration(
    integration_data: IntegrationCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Create a new external service integration.
    
    Sets up a new integration with the specified external service.
    OAuth authentication should be completed separately.
    """
    
    service = IntegrationService(db)
    
    try:
        integration = await service.create_integration(
            user_id=current_user.id,
            integration_data=integration_data
        )
        
        logger.info(f"Created integration {integration.id} for user {current_user.id}")
        
        return integration
        
    except ConflictError as e:
        raise HTTPException(status_code=409, detail=str(e))
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error creating integration for user {current_user.id}: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to create integration"
        )


@router.get("/{integration_id}", response_model=IntegrationResponse)
async def get_integration(
    integration_id: UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get details of a specific integration.
    
    Returns comprehensive information about the integration including
    sync history, health status, and configuration.
    """
    
    service = IntegrationService(db)
    
    try:
        integration = await service.get_integration(
            user_id=current_user.id,
            integration_id=integration_id
        )
        
        return integration
        
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error getting integration {integration_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to retrieve integration"
        )


@router.put("/{integration_id}", response_model=IntegrationResponse)
async def update_integration(
    integration_id: UUID,
    update_data: IntegrationUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Update an existing integration.
    
    Allows updating integration configuration, sync settings,
    and other non-authentication related properties.
    """
    
    service = IntegrationService(db)
    
    try:
        integration = await service.update_integration(
            user_id=current_user.id,
            integration_id=integration_id,
            update_data=update_data
        )
        
        logger.info(f"Updated integration {integration_id}")
        
        return integration
        
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error updating integration {integration_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to update integration"
        )


@router.delete("/{integration_id}")
async def delete_integration(
    integration_id: UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Delete an integration.
    
    Removes the integration and all associated sync history.
    This action cannot be undone.
    """
    
    service = IntegrationService(db)
    
    try:
        await service.delete_integration(
            user_id=current_user.id,
            integration_id=integration_id
        )
        
        logger.info(f"Deleted integration {integration_id}")
        
        return {"message": "Integration deleted successfully"}
        
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error deleting integration {integration_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to delete integration"
        )


@router.post("/{integration_id}/sync", response_model=SyncResponse)
async def start_sync(
    integration_id: UUID,
    sync_request: SyncRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Start a synchronization operation.
    
    Initiates data synchronization between Project Chronos and the
    external service. The operation runs in the background.
    """
    
    service = IntegrationService(db)
    
    try:
        sync_response = await service.start_sync(
            user_id=current_user.id,
            integration_id=integration_id,
            sync_request=sync_request
        )
        
        logger.info(f"Started sync {sync_response.sync_id} for integration {integration_id}")
        
        return sync_response
        
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except IntegrationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error starting sync for integration {integration_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to start synchronization"
        )


@router.get("/{integration_id}/sync-logs", response_model=List[SyncLogResponse])
async def get_sync_logs(
    integration_id: UUID,
    operation_type: Optional[str] = Query(None, description="Filter by operation type"),
    status: Optional[SyncStatus] = Query(None, description="Filter by sync status"),
    limit: int = Query(50, ge=1, le=100, description="Maximum number of logs to return"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get synchronization logs for an integration.
    
    Returns the history of sync operations with details about
    what was synchronized and any errors that occurred.
    """
    
    service = IntegrationService(db)
    
    try:
        sync_logs = await service.get_sync_logs(
            user_id=current_user.id,
            integration_id=integration_id,
            operation_type=operation_type,
            status=status,
            limit=limit
        )
        
        return sync_logs
        
    except Exception as e:
        logger.error(f"Error getting sync logs for integration {integration_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to retrieve sync logs"
        )


@router.get("/{integration_id}/health", response_model=IntegrationHealth)
async def check_integration_health(
    integration_id: UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Check the health status of an integration.
    
    Returns comprehensive health information including token status,
    recent sync success rate, and recommendations for improvement.
    """
    
    service = IntegrationService(db)
    
    try:
        health = await service.check_integration_health(
            user_id=current_user.id,
            integration_id=integration_id
        )
        
        return health
        
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error checking health for integration {integration_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to check integration health"
        )


@router.get("/stats/summary", response_model=IntegrationStats)
async def get_integration_stats(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get integration statistics summary.
    
    Returns overview statistics about all user integrations including
    sync success rates, most active integrations, and recent errors.
    """
    
    service = IntegrationService(db)
    
    try:
        stats = await service.get_integration_stats(user_id=current_user.id)
        
        return stats
        
    except Exception as e:
        logger.error(f"Error getting integration stats for user {current_user.id}: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to retrieve integration statistics"
        )


@router.post("/oauth/initiate")
async def initiate_oauth(
    oauth_request: OAuthInitiate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Initiate OAuth flow for external service.
    
    Returns the OAuth authorization URL that the user should visit
    to grant permissions to Project Chronos.
    """
    
    # This is a placeholder - in real implementation, this would:
    # 1. Generate OAuth state parameter
    # 2. Build authorization URL with proper scopes
    # 3. Store state in Redis for verification
    # 4. Return authorization URL to client
    
    try:
        # Placeholder OAuth URL generation
        auth_url = f"https://oauth.{oauth_request.integration_type}.com/authorize"
        
        return {
            "authorization_url": auth_url,
            "state": "generated_state_parameter",
            "expires_in": 600  # 10 minutes
        }
        
    except Exception as e:
        logger.error(f"Error initiating OAuth for {oauth_request.integration_type}: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to initiate OAuth flow"
        )


@router.post("/oauth/callback/{integration_type}")
async def oauth_callback(
    integration_type: IntegrationType,
    callback_data: OAuthCallback,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Handle OAuth callback from external service.
    
    Processes the authorization code and exchanges it for access tokens.
    Updates the corresponding integration with the new credentials.
    """
    
    # This is a placeholder - in real implementation, this would:
    # 1. Verify state parameter
    # 2. Exchange authorization code for tokens
    # 3. Store encrypted tokens in integration
    # 4. Set up webhook if supported
    # 5. Perform initial sync
    
    try:
        if callback_data.error:
            raise HTTPException(
                status_code=400,
                detail=f"OAuth error: {callback_data.error_description or callback_data.error}"
            )
        
        # Placeholder token exchange
        return {
            "message": "OAuth flow completed successfully",
            "integration_type": integration_type,
            "status": "connected"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error handling OAuth callback for {integration_type}: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to complete OAuth flow"
        )


@router.post("/webhooks/{integration_type}")
async def handle_webhook(
    integration_type: IntegrationType,
    request: Request,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db)
):
    """
    Handle incoming webhooks from external services.
    
    Processes real-time notifications from external services about
    data changes that need to be synchronized.
    """
    
    try:
        # Get request body and headers
        body = await request.body()
        headers = dict(request.headers)
        
        # Parse webhook payload
        try:
            import json
            payload = json.loads(body.decode())
        except (json.JSONDecodeError, UnicodeDecodeError):
            payload = {"raw_body": body.decode("utf-8", errors="ignore")}
        
        # Create webhook event record
        webhook_event = WebhookEventCreate(
            integration_type=integration_type,
            event_type=headers.get("x-event-type", "unknown"),
            external_id=payload.get("id"),
            payload=payload,
            headers=headers
        )
        
        # Process webhook in background
        background_tasks.add_task(
            _process_webhook_event,
            db,
            webhook_event
        )
        
        logger.info(f"Received webhook for {integration_type}")
        
        return {"message": "Webhook received and queued for processing"}
        
    except Exception as e:
        logger.error(f"Error handling webhook for {integration_type}: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to process webhook"
        )


async def _process_webhook_event(db: AsyncSession, webhook_event: WebhookEventCreate):
    """Process webhook event in background."""
    
    # This is a placeholder - in real implementation, this would:
    # 1. Verify webhook signature
    # 2. Find corresponding integration
    # 3. Parse event data
    # 4. Trigger appropriate sync operation
    # 5. Update webhook event status
    
    try:
        logger.info(f"Processing webhook event: {webhook_event.event_type}")
        
        # Placeholder processing
        await asyncio.sleep(1)
        
        logger.info("Webhook event processed successfully")
        
    except Exception as e:
        logger.error(f"Error processing webhook event: {e}")


# Additional utility endpoints

@router.get("/sync-logs", response_model=List[SyncLogResponse])
async def get_all_sync_logs(
    operation_type: Optional[str] = Query(None, description="Filter by operation type"),
    status: Optional[SyncStatus] = Query(None, description="Filter by sync status"),
    limit: int = Query(50, ge=1, le=100, description="Maximum number of logs to return"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get all sync logs for user's integrations.
    
    Returns sync history across all integrations with optional filtering.
    """
    
    service = IntegrationService(db)
    
    try:
        sync_logs = await service.get_sync_logs(
            user_id=current_user.id,
            operation_type=operation_type,
            status=status,
            limit=limit
        )
        
        return sync_logs
        
    except Exception as e:
        logger.error(f"Error getting all sync logs for user {current_user.id}: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to retrieve sync logs"
        )
