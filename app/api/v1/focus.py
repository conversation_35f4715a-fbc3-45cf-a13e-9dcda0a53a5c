"""
Focus session API endpoints for Project Chronos.

This module provides REST API endpoints for focus session management,
including Pomodoro technique, deep work sessions, and group synchronization.
"""

from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.auth import get_current_user
from app.models.user import User
from app.schemas.focus import (
    FocusSessionCreate,
    FocusSessionUpdate,
    FocusSessionResponse,
    FocusSessionParticipantCreate,
    FocusSessionParticipantUpdate,
    FocusSessionParticipantResponse,
    GroupFocusSessionCreate,
    FocusSessionCommand,
    FocusSessionStats
)
from app.services.focus_service import FocusSessionService

router = APIRouter(prefix="/focus", tags=["focus"])
service = FocusSessionService()


@router.post("/sessions")
async def create_focus_session(
    session_data: FocusSessionCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Create a new focus session.
    
    Creates a personal or group focus session for structured work time.
    Supports Pomodoro technique, deep work sessions, and ADHD-specific patterns.
    """
    try:
        session = await service.create_session(db, session_data, current_user)
        
        # Calculate computed fields
        response_data = {
            "id": session.id,
            "user_id": session.user_id,
            "task_id": session.task_id,
            "body_doubling_session_id": session.body_doubling_session_id,
            "session_type": session.session_type,
            "title": session.title,
            "planned_duration": session.planned_duration,
            "actual_duration": session.actual_duration,
            "break_duration": session.break_duration,
            "status": session.status,
            "focus_mode_settings": session.focus_mode_settings,
            "started_at": session.started_at,
            "completed_at": session.completed_at,
            "paused_at": session.paused_at,
            "is_group_session": session.is_group_session,
            "group_session_leader": session.group_session_leader,
            "hyperfocus_detected": session.hyperfocus_detected,
            "hyperfocus_duration": session.hyperfocus_duration,
            "distraction_count": session.distraction_count,
            "energy_level_start": session.energy_level_start,
            "energy_level_end": session.energy_level_end,
            "session_notes": session.session_notes,
            "created_at": session.created_at,
            "updated_at": session.updated_at,
            "elapsed_minutes": session.get_elapsed_minutes(),
            "remaining_minutes": session.get_remaining_minutes(),
            "progress_percentage": session.get_progress_percentage()
        }
        
        return response_data
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create focus session: {str(e)}"
        )


@router.get("/sessions")
async def get_focus_sessions(
    limit: int = Query(50, ge=1, le=100),
    offset: int = Query(0, ge=0),
    status: Optional[str] = Query(None),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get user's focus sessions.
    
    Returns a paginated list of the user's focus sessions,
    optionally filtered by status.
    """
    try:
        sessions = await service.get_user_sessions(
            db, current_user, limit, offset, status
        )
        
        # Convert to response format with computed fields
        response_sessions = []
        for session in sessions:
            session_data = {
                "id": session.id,
                "user_id": session.user_id,
                "task_id": session.task_id,
                "body_doubling_session_id": session.body_doubling_session_id,
                "session_type": session.session_type,
                "title": session.title,
                "planned_duration": session.planned_duration,
                "actual_duration": session.actual_duration,
                "break_duration": session.break_duration,
                "status": session.status,
                "focus_mode_settings": session.focus_mode_settings,
                "started_at": session.started_at,
                "completed_at": session.completed_at,
                "paused_at": session.paused_at,
                "is_group_session": session.is_group_session,
                "group_session_leader": session.group_session_leader,
                "hyperfocus_detected": session.hyperfocus_detected,
                "hyperfocus_duration": session.hyperfocus_duration,
                "distraction_count": session.distraction_count,
                "energy_level_start": session.energy_level_start,
                "energy_level_end": session.energy_level_end,
                "session_notes": session.session_notes,
                "created_at": session.created_at,
                "updated_at": session.updated_at,
                "elapsed_minutes": session.get_elapsed_minutes(),
                "remaining_minutes": session.get_remaining_minutes(),
                "progress_percentage": session.get_progress_percentage()
            }
            response_sessions.append(session_data)
        
        return {
            "sessions": response_sessions,
            "total": len(response_sessions),
            "limit": limit,
            "offset": offset
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get focus sessions: {str(e)}"
        )


@router.get("/sessions/stats")
async def get_session_stats(
    days: int = Query(30, ge=1, le=365),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get focus session statistics.

    Returns comprehensive statistics about the user's focus sessions,
    including completion rates, focus time, and ADHD-specific metrics.
    """
    try:
        stats = await service.get_session_stats(db, current_user, days)
        return stats
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get session stats: {str(e)}"
        )


@router.get("/sessions/{session_id}")
async def get_focus_session(
    session_id: UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get a specific focus session.

    Returns detailed information about a focus session,
    including computed progress fields.
    """
    try:
        session = await service.get_session(db, session_id, current_user)
        if not session:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Focus session not found"
            )
        
        return {
            "id": session.id,
            "user_id": session.user_id,
            "task_id": session.task_id,
            "body_doubling_session_id": session.body_doubling_session_id,
            "session_type": session.session_type,
            "title": session.title,
            "planned_duration": session.planned_duration,
            "actual_duration": session.actual_duration,
            "break_duration": session.break_duration,
            "status": session.status,
            "focus_mode_settings": session.focus_mode_settings,
            "started_at": session.started_at,
            "completed_at": session.completed_at,
            "paused_at": session.paused_at,
            "is_group_session": session.is_group_session,
            "group_session_leader": session.group_session_leader,
            "hyperfocus_detected": session.hyperfocus_detected,
            "hyperfocus_duration": session.hyperfocus_duration,
            "distraction_count": session.distraction_count,
            "energy_level_start": session.energy_level_start,
            "energy_level_end": session.energy_level_end,
            "session_notes": session.session_notes,
            "created_at": session.created_at,
            "updated_at": session.updated_at,
            "elapsed_minutes": session.get_elapsed_minutes(),
            "remaining_minutes": session.get_remaining_minutes(),
            "progress_percentage": session.get_progress_percentage()
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get focus session: {str(e)}"
        )


@router.patch("/sessions/{session_id}")
async def update_focus_session(
    session_id: UUID,
    session_data: FocusSessionUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Update a focus session.
    
    Updates session details, progress tracking, and ADHD-specific metrics.
    """
    try:
        session = await service.update_session(db, session_id, session_data, current_user)
        if not session:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Focus session not found"
            )
        
        return {
            "id": session.id,
            "user_id": session.user_id,
            "task_id": session.task_id,
            "body_doubling_session_id": session.body_doubling_session_id,
            "session_type": session.session_type,
            "title": session.title,
            "planned_duration": session.planned_duration,
            "actual_duration": session.actual_duration,
            "break_duration": session.break_duration,
            "status": session.status,
            "focus_mode_settings": session.focus_mode_settings,
            "started_at": session.started_at,
            "completed_at": session.completed_at,
            "paused_at": session.paused_at,
            "is_group_session": session.is_group_session,
            "group_session_leader": session.group_session_leader,
            "hyperfocus_detected": session.hyperfocus_detected,
            "hyperfocus_duration": session.hyperfocus_duration,
            "distraction_count": session.distraction_count,
            "energy_level_start": session.energy_level_start,
            "energy_level_end": session.energy_level_end,
            "session_notes": session.session_notes,
            "created_at": session.created_at,
            "updated_at": session.updated_at,
            "elapsed_minutes": session.get_elapsed_minutes(),
            "remaining_minutes": session.get_remaining_minutes(),
            "progress_percentage": session.get_progress_percentage()
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update focus session: {str(e)}"
        )


@router.post("/sessions/{session_id}/commands")
async def execute_session_command(
    session_id: UUID,
    command_data: FocusSessionCommand,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Execute a focus session command.
    
    Commands: start, pause, resume, complete, cancel
    Handles session state transitions and timing.
    """
    try:
        session = await service.execute_command(db, session_id, command_data, current_user)
        if not session:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Focus session not found"
            )
        
        return {
            "message": f"Command '{command_data.command}' executed successfully",
            "session": {
                "id": session.id,
                "status": session.status,
                "started_at": session.started_at,
                "completed_at": session.completed_at,
                "paused_at": session.paused_at,
                "elapsed_minutes": session.get_elapsed_minutes(),
                "remaining_minutes": session.get_remaining_minutes(),
                "progress_percentage": session.get_progress_percentage()
            }
        }
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to execute command: {str(e)}"
        )





@router.post("/group-sessions")
async def create_group_focus_session(
    session_data: GroupFocusSessionCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Create a group focus session within a body doubling session.

    Creates a synchronized focus session that multiple participants
    can join for collaborative focused work time.
    """
    try:
        session = await service.create_group_session(db, session_data, current_user)

        return {
            "id": session.id,
            "user_id": session.user_id,
            "body_doubling_session_id": session.body_doubling_session_id,
            "session_type": session.session_type,
            "title": session.title,
            "planned_duration": session.planned_duration,
            "break_duration": session.break_duration,
            "status": session.status,
            "is_group_session": session.is_group_session,
            "group_session_leader": session.group_session_leader,
            "created_at": session.created_at,
            "elapsed_minutes": session.get_elapsed_minutes(),
            "remaining_minutes": session.get_remaining_minutes(),
            "progress_percentage": session.get_progress_percentage()
        }
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create group focus session: {str(e)}"
        )


@router.post("/sessions/{session_id}/join")
async def join_group_focus_session(
    session_id: UUID,
    participant_data: FocusSessionParticipantCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Join a group focus session.

    Allows a user to join an existing group focus session
    and optionally specify a personal task to work on.
    """
    try:
        participant = await service.join_group_session(
            db, session_id, participant_data, current_user
        )

        return {
            "message": "Successfully joined group focus session",
            "participant": {
                "id": participant.id,
                "focus_session_id": participant.focus_session_id,
                "user_id": participant.user_id,
                "personal_task_id": participant.personal_task_id,
                "joined_at": participant.joined_at,
                "status": participant.status,
                "progress_notes": participant.progress_notes
            }
        }
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to join group focus session: {str(e)}"
        )
