"""
Task management API endpoints for Project Chronos.

This module provides ADHD-optimized task management endpoints with
AI chunking, adaptive filtering, and task jar functionality.
"""

from typing import List, Optional
from uuid import UUID
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from app.core.database import get_db
from app.schemas.task import (
    TaskCreate, TaskUpdate, TaskResponse, TaskChunkRequest,
    TaskFilterRequest, TaskJarRequest, TaskStatsResponse
)
from app.services.task_service import TaskService
from app.services.filter_service import AdaptiveFilterService
from app.core.exceptions import (
    TaskNotFoundError, TaskValidationError, AuthorizationError,
    AIServiceException
)


router = APIRouter(prefix="/tasks", tags=["tasks"])
task_service = TaskService()
filter_service = AdaptiveFilterService()


# Temporary user dependency - will be replaced with proper auth
async def get_current_user_id() -> UUID:
    """Temporary function to get current user ID."""
    # This will be replaced with proper JWT authentication
    return UUID("550e8400-e29b-41d4-a716-************")


@router.post("/", response_model=TaskResponse)
async def create_task(
    task_data: TaskCreate,
    db: AsyncSession = Depends(get_db),
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Create a new task with ADHD-optimized defaults.
    
    Creates a task with intelligent defaults and validation
    specifically designed for users with ADHD.
    """
    try:
        task = await task_service.create_task(db, task_data, current_user_id)
        return TaskResponse.model_validate(task)
    except TaskValidationError as e:
        raise HTTPException(status_code=400, detail=e.message)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/", response_model=List[TaskResponse])
async def get_tasks(
    energy_level: Optional[str] = Query(None, description="Filter by energy level"),
    max_duration: Optional[int] = Query(None, description="Maximum duration in minutes"),
    context_tags: Optional[str] = Query(None, description="Comma-separated context tags"),
    status: Optional[str] = Query(None, description="Comma-separated status values"),
    priority: Optional[str] = Query(None, description="Comma-separated priority values"),
    include_overdue: bool = Query(True, description="Include overdue tasks"),
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of records"),
    db: AsyncSession = Depends(get_db),
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Get tasks with adaptive filtering.
    
    Retrieves user's tasks with ADHD-optimized filtering options
    including energy level, duration, and context-based selection.
    """
    try:
        # Build filter request
        filters = TaskFilterRequest(
            energy_level=energy_level,
            max_duration=max_duration,
            context_tags=context_tags.split(",") if context_tags else None,
            status=status.split(",") if status else None,
            priority=priority.split(",") if priority else None,
            include_overdue=include_overdue
        )
        
        tasks = await task_service.get_user_tasks(
            db, current_user_id, filters, skip, limit
        )
        
        return [TaskResponse.model_validate(task) for task in tasks]
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{task_id}", response_model=TaskResponse)
async def get_task(
    task_id: UUID,
    db: AsyncSession = Depends(get_db),
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Get a specific task by ID.
    
    Retrieves a single task with authorization check.
    """
    try:
        task = await task_service.get_task_by_id(db, task_id, current_user_id)
        return TaskResponse.model_validate(task)
    except TaskNotFoundError as e:
        raise HTTPException(status_code=404, detail=e.message)
    except AuthorizationError as e:
        raise HTTPException(status_code=403, detail=e.message)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/{task_id}", response_model=TaskResponse)
async def update_task(
    task_id: UUID,
    task_data: TaskUpdate,
    db: AsyncSession = Depends(get_db),
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Update an existing task.
    
    Updates task with validation and ADHD-specific status handling.
    """
    try:
        task = await task_service.update_task(db, task_id, task_data, current_user_id)
        return TaskResponse.model_validate(task)
    except TaskNotFoundError as e:
        raise HTTPException(status_code=404, detail=e.message)
    except AuthorizationError as e:
        raise HTTPException(status_code=403, detail=e.message)
    except TaskValidationError as e:
        raise HTTPException(status_code=400, detail=e.message)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/{task_id}")
async def delete_task(
    task_id: UUID,
    soft_delete: bool = Query(True, description="Use soft delete (ADHD-friendly)"),
    db: AsyncSession = Depends(get_db),
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Delete a task (soft delete by default).
    
    Deletes a task with soft delete option for ADHD users
    who may need to recover accidentally deleted items.
    """
    try:
        await task_service.delete_task(db, task_id, current_user_id, soft_delete)
        return {"message": "Task deleted successfully"}
    except TaskNotFoundError as e:
        raise HTTPException(status_code=404, detail=e.message)
    except AuthorizationError as e:
        raise HTTPException(status_code=403, detail=e.message)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{task_id}/chunk", response_model=List[TaskResponse])
async def chunk_task(
    task_id: UUID,
    chunk_request: TaskChunkRequest,
    db: AsyncSession = Depends(get_db),
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Break down a task using AI chunking.
    
    Uses AI to break down overwhelming tasks into manageable,
    actionable subtasks specifically designed for ADHD users.
    """
    try:
        subtasks = await task_service.chunk_task(
            db=db,
            task_id=task_id,
            user_id=current_user_id,
            chunk_size=chunk_request.chunk_size,
            context=chunk_request.context,
            user_preferences=chunk_request.user_preferences
        )
        
        return [TaskResponse.model_validate(task) for task in subtasks]
    except TaskNotFoundError as e:
        raise HTTPException(status_code=404, detail=e.message)
    except AuthorizationError as e:
        raise HTTPException(status_code=403, detail=e.message)
    except AIServiceException as e:
        raise HTTPException(status_code=503, detail=e.message)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/jar/random", response_model=List[TaskResponse])
async def get_task_jar(
    jar_size: int = Query(5, ge=1, le=10, description="Number of tasks to select"),
    energy_level: Optional[str] = Query(None, description="Filter by energy level"),
    max_duration: Optional[int] = Query(None, description="Maximum duration in minutes"),
    context_tags: Optional[str] = Query(None, description="Comma-separated context tags"),
    db: AsyncSession = Depends(get_db),
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Get random task selection for decision fatigue reduction.
    
    Returns a curated selection of tasks to help ADHD users
    overcome choice paralysis and decision fatigue.
    """
    try:
        # Build filter request
        filters = None
        if energy_level or max_duration or context_tags:
            filters = TaskFilterRequest(
                energy_level=energy_level,
                max_duration=max_duration,
                context_tags=context_tags.split(",") if context_tags else None
            )
        
        tasks = await filter_service.get_task_jar_selection(
            db, current_user_id, jar_size, filters
        )
        
        return [TaskResponse.model_validate(task) for task in tasks]
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/adaptive/filtered", response_model=List[TaskResponse])
async def get_adaptive_filtered_tasks(
    energy_level: Optional[str] = Query(None, description="Current energy level"),
    max_duration: Optional[int] = Query(None, description="Available time in minutes"),
    context_tags: Optional[str] = Query(None, description="Current context tags"),
    db: AsyncSession = Depends(get_db),
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Get tasks with adaptive filtering based on current state.
    
    Uses intelligent filtering to show the most relevant tasks
    based on the user's current energy level, available time,
    and context.
    """
    try:
        context_list = context_tags.split(",") if context_tags else None
        
        tasks = await filter_service.get_filtered_tasks(
            db=db,
            user_id=current_user_id,
            energy_level=energy_level,
            max_duration=max_duration,
            context_tags=context_list
        )
        
        return [TaskResponse.model_validate(task) for task in tasks]
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
