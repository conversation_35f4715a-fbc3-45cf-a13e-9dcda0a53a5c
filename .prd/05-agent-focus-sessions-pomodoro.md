# Agent 5: Focus Sessions & Pomodoro Agent PRD

## Agent Overview

**Agent Name**: Focus Sessions & Pomodoro Agent  
**Primary Responsibility**: Focus session management, pomodoro timers, concentration tracking  
**Dependencies**: Agent 1 (Core Infrastructure), Agent 2 (Authentication), Agent 4 (Time Blocking)  
**Deliverables**: Unobtrusive focus timers, flow state protection, focus mode management, concentration analytics

## Mission Statement

Protect and cultivate flow states for users with ADHD by providing gentle, non-disruptive focus tools that work with the neurodivergent brain's natural patterns. Enable deep work while respecting hyperfocus tendencies and attention fluctuations.

## Technical Specifications

### Technology Stack
- **Real-time Timers**: WebSocket connections for live timer updates
- **Audio Processing**: Web Audio API for subtle, non-jarring sounds
- **State Management**: Redis for session state persistence
- **Analytics**: Time-series data for focus pattern analysis
- **Notifications**: Gentle, customizable focus break reminders

### Core Responsibilities

#### 1. Focus Session Management
```python
# app/services/focus_service.py - Focus session business logic
# app/api/v1/focus.py - Focus session endpoints
# app/schemas/focus.py - Focus session Pydantic schemas
# app/models/focus_session.py - Focus session database models
```

#### 2. Timer Systems
```python
# app/services/timer_service.py - Timer management and state
# app/utils/timer_utils.py - Timer calculation utilities
# app/workers/timer_worker.py - Background timer processing
```

#### 3. Focus Mode Management
```python
# app/services/focus_mode_service.py - Focus mode configuration
# app/utils/focus_utils.py - Focus state utilities
# app/schemas/focus_mode.py - Focus mode schemas
```

## Key Features & User Stories

### Feature 1: Unobtrusive Focus Timer (Pomodoro+)
**User Story**: "As a user trying to get into deep work, I want a built-in timer that helps me focus in sprints but doesn't use loud, startling alarms that break my concentration."

**Technical Requirements**:
- Customizable timer durations (traditional 25min or user-defined)
- Gentle, non-jarring audio cues with volume control
- Visual progress indicators that don't demand attention
- Pause/resume functionality for hyperfocus protection
- Integration with time blocking from Agent 4

### Feature 2: Intelligent Notification Shielding & Focus Modes
**User Story**: "As a neurodivergent user who finds interruptions deeply jarring, I want to protect my focus by creating modes that silence all digital noise, allowing me to enter and stay in a flow state without being derailed."

**Technical Requirements**:
- Custom focus mode profiles (Writing, Studying, Creative, etc.)
- Integration with system-level notification blocking
- Automatic focus mode activation based on scheduled time blocks
- Emergency contact breakthrough options
- Focus session analytics and insights

### Feature 3: Flow State Protection
**User Story**: "As a user who sometimes hyperfocuses for hours without breaks, I want gentle reminders that don't force me to stop but help me make conscious decisions about continuing."

**Technical Requirements**:
- Hyperfocus detection and gentle intervention
- "Continue working" vs "Take break" choice prompts
- Hydration and movement reminders
- Eye strain prevention notifications
- Flexible break scheduling based on user patterns

## Focus Session Implementation

### Focus Service Architecture
```python
# app/services/focus_service.py
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List
from uuid import UUID
from enum import Enum

class FocusSessionType(str, Enum):
    """Types of focus sessions."""
    POMODORO = "pomodoro"
    DEEP_WORK = "deep_work"
    SPRINT = "sprint"
    CUSTOM = "custom"

class FocusSessionStatus(str, Enum):
    """Focus session status states."""
    PLANNED = "planned"
    ACTIVE = "active"
    PAUSED = "paused"
    BREAK = "break"
    COMPLETED = "completed"
    CANCELLED = "cancelled"

class FocusSessionService:
    """Service for managing focus sessions and flow states."""
    
    def __init__(self, db: AsyncSession, timer_service: TimerService):
        self.db = db
        self.timer_service = timer_service
        self.redis = Redis()
    
    async def start_focus_session(
        self,
        user_id: UUID,
        session_type: FocusSessionType,
        planned_duration: int,
        task_id: Optional[UUID] = None,
        focus_mode_settings: Optional[Dict] = None
    ) -> FocusSession:
        """
        Start a new focus session with ADHD-optimized settings.
        
        Args:
            user_id: User identifier
            session_type: Type of focus session
            planned_duration: Planned duration in minutes
            task_id: Associated task (optional)
            focus_mode_settings: Custom focus mode configuration
            
        Returns:
            Started focus session
            
        Raises:
            FocusSessionError: If session cannot be started
        """
    
    async def pause_focus_session(
        self,
        user_id: UUID,
        session_id: UUID,
        pause_reason: Optional[str] = None
    ) -> FocusSession:
        """Pause active focus session with reason tracking."""
    
    async def resume_focus_session(
        self,
        user_id: UUID,
        session_id: UUID
    ) -> FocusSession:
        """Resume paused focus session."""
    
    async def complete_focus_session(
        self,
        user_id: UUID,
        session_id: UUID,
        actual_duration: Optional[int] = None,
        completion_notes: Optional[str] = None
    ) -> FocusSession:
        """Complete focus session with optional notes."""
    
    async def get_focus_analytics(
        self,
        user_id: UUID,
        date_range: Optional[tuple] = None
    ) -> FocusAnalytics:
        """
        Get focus session analytics and patterns.
        
        Returns insights on:
        - Optimal focus times
        - Average session duration
        - Break patterns
        - Productivity trends
        """
```

### Timer Service Implementation
```python
# app/services/timer_service.py
class TimerService:
    """Real-time timer management for focus sessions."""
    
    def __init__(self):
        self.redis = Redis()
        self.active_timers: Dict[str, Timer] = {}
    
    async def create_timer(
        self,
        session_id: UUID,
        duration: int,
        timer_type: str = "focus"
    ) -> Timer:
        """Create and start a new timer."""
    
    async def get_timer_state(
        self,
        session_id: UUID
    ) -> TimerState:
        """Get current timer state with remaining time."""
    
    async def pause_timer(
        self,
        session_id: UUID
    ) -> TimerState:
        """Pause active timer."""
    
    async def resume_timer(
        self,
        session_id: UUID
    ) -> TimerState:
        """Resume paused timer."""
    
    async def extend_timer(
        self,
        session_id: UUID,
        additional_minutes: int
    ) -> TimerState:
        """Extend timer duration (for hyperfocus protection)."""
    
    def _calculate_remaining_time(
        self,
        start_time: datetime,
        duration: int,
        paused_duration: int = 0
    ) -> int:
        """Calculate remaining time accounting for pauses."""
```

### Focus Mode Management
```python
# app/services/focus_mode_service.py
class FocusModeService:
    """Manage focus modes and notification shielding."""
    
    async def create_focus_mode(
        self,
        user_id: UUID,
        mode_data: FocusModeCreate
    ) -> FocusMode:
        """Create custom focus mode profile."""
    
    async def activate_focus_mode(
        self,
        user_id: UUID,
        mode_id: UUID,
        session_id: Optional[UUID] = None
    ) -> FocusModeActivation:
        """
        Activate focus mode with notification shielding.
        
        Features:
        - Block non-essential notifications
        - Set custom ambient sounds
        - Configure break reminders
        - Enable hyperfocus protection
        """
    
    async def deactivate_focus_mode(
        self,
        user_id: UUID,
        mode_id: UUID
    ) -> FocusModeDeactivation:
        """Deactivate focus mode and restore normal notifications."""
    
    def get_default_focus_modes(self) -> List[FocusMode]:
        """
        Get default focus mode templates.
        
        Includes:
        - Deep Work (90min sessions, minimal breaks)
        - Pomodoro (25min work, 5min break)
        - Creative Flow (flexible duration, gentle reminders)
        - Study Session (45min focus, 15min break)
        - Quick Sprint (15min focused bursts)
        """
```

## WebSocket Integration

### Real-time Timer Updates
```python
# app/api/websockets/focus.py
from fastapi import WebSocket, WebSocketDisconnect
from app.services.focus_service import FocusSessionService

class FocusWebSocketManager:
    """Manage WebSocket connections for real-time focus updates."""
    
    def __init__(self):
        self.active_connections: Dict[UUID, WebSocket] = {}
        self.focus_service = FocusSessionService()
    
    async def connect(self, websocket: WebSocket, user_id: UUID):
        """Connect user to focus session WebSocket."""
        await websocket.accept()
        self.active_connections[user_id] = websocket
    
    async def disconnect(self, user_id: UUID):
        """Disconnect user from focus session WebSocket."""
        if user_id in self.active_connections:
            del self.active_connections[user_id]
    
    async def send_timer_update(
        self,
        user_id: UUID,
        timer_state: TimerState
    ):
        """Send real-time timer updates to connected client."""
        if user_id in self.active_connections:
            websocket = self.active_connections[user_id]
            await websocket.send_json({
                "type": "timer_update",
                "data": timer_state.dict()
            })
    
    async def send_break_reminder(
        self,
        user_id: UUID,
        reminder_type: str,
        message: str
    ):
        """Send gentle break reminder to user."""
        if user_id in self.active_connections:
            websocket = self.active_connections[user_id]
            await websocket.send_json({
                "type": "break_reminder",
                "reminder_type": reminder_type,
                "message": message,
                "options": ["continue", "take_break", "extend_5min"]
            })
```

## API Endpoints

### Focus Session Endpoints
```python
# app/api/v1/focus.py
@router.post("/sessions", response_model=FocusSessionResponse)
async def start_focus_session(
    session_data: FocusSessionCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Start a new focus session."""

@router.get("/sessions/active", response_model=Optional[FocusSessionResponse])
async def get_active_session(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get currently active focus session."""

@router.put("/sessions/{session_id}/pause", response_model=FocusSessionResponse)
async def pause_focus_session(
    session_id: UUID,
    pause_data: Optional[PauseRequest] = None,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Pause active focus session."""

@router.put("/sessions/{session_id}/resume", response_model=FocusSessionResponse)
async def resume_focus_session(
    session_id: UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Resume paused focus session."""

@router.post("/sessions/{session_id}/complete", response_model=FocusSessionResponse)
async def complete_focus_session(
    session_id: UUID,
    completion_data: SessionCompletionRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Complete focus session with notes."""

@router.get("/analytics", response_model=FocusAnalyticsResponse)
async def get_focus_analytics(
    days: int = 30,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get focus session analytics and insights."""
```

## Implementation Plan

### Phase 1: Core Focus Sessions (Week 1)
1. Implement basic focus session CRUD operations
2. Create timer service with pause/resume functionality
3. Set up WebSocket connections for real-time updates
4. Implement session state management with Redis

### Phase 2: Focus Modes & Notification Shielding (Week 2)
1. Develop focus mode profiles and management
2. Implement notification blocking integration
3. Create gentle reminder system
4. Add hyperfocus detection and protection

### Phase 3: Advanced Timer Features (Week 3)
1. Implement customizable timer durations and types
2. Add gentle audio cues and visual indicators
3. Create break scheduling and management
4. Develop focus session analytics

### Phase 4: Integration & Testing (Week 4)
1. Integrate with time blocking from Agent 4
2. Add comprehensive testing for all timer functions
3. Implement focus analytics and insights
4. Create documentation and user guides

## Testing Strategy

### Unit Tests
```python
# tests/unit/test_services/test_focus_service.py
class TestFocusSessionService:
    async def test_start_focus_session(self):
        """Test focus session creation and startup."""
    
    async def test_pause_resume_session(self):
        """Test session pause and resume functionality."""
    
    async def test_hyperfocus_detection(self):
        """Test hyperfocus detection and gentle intervention."""

# tests/unit/test_services/test_timer_service.py
class TestTimerService:
    async def test_timer_accuracy(self):
        """Test timer accuracy and state management."""
    
    async def test_timer_extension(self):
        """Test timer extension for hyperfocus protection."""
```

### Integration Tests
```python
# tests/integration/test_focus/test_websocket_timers.py
class TestFocusWebSockets:
    async def test_real_time_timer_updates(self):
        """Test real-time timer updates via WebSocket."""
    
    async def test_break_reminder_delivery(self):
        """Test gentle break reminder delivery."""
```

### BDD Scenarios
```gherkin
Feature: Unobtrusive Focus Timer
  Scenario: Starting a pomodoro session
    Given I want to start a 25-minute focus session
    When I start a pomodoro timer
    Then the timer should begin counting down
    And I should receive gentle progress updates
    And no jarring alarms should interrupt my flow

Feature: Hyperfocus Protection
  Scenario: Extended focus session detection
    Given I have been in a focus session for 2 hours
    When the hyperfocus detection triggers
    Then I should receive a gentle reminder
    And I should have options to continue or take a break
    And the reminder should not force me to stop

Feature: Focus Mode Activation
  Scenario: Activating deep work mode
    Given I have a "Deep Work" focus mode configured
    When I activate the focus mode
    Then non-essential notifications should be blocked
    And my focus environment should be optimized
    And I should be able to work without interruptions
```

## Quality Standards

### Performance Requirements
- Timer accuracy within 1-second precision
- WebSocket message delivery < 100ms
- Focus mode activation < 2 seconds
- Analytics generation < 500ms

### User Experience Requirements
- Gentle, non-jarring audio cues
- Smooth timer transitions without interruption
- Intuitive pause/resume functionality
- Clear visual progress indicators

## Success Metrics

### Feature Adoption
- 70% of users try focus sessions within first week
- 50% of users create custom focus modes
- 60% of users complete focus sessions regularly
- 40% of users use hyperfocus protection features

### User Impact
- 40% increase in sustained focus duration
- 50% reduction in focus interruption incidents
- 60% user satisfaction with gentle reminder system
- 35% improvement in work session completion rates

## Deliverables

1. **Focus Session System**: Complete session management with timer integration
2. **Timer Service**: Real-time, accurate timer with WebSocket updates
3. **Focus Mode Management**: Custom focus profiles with notification shielding
4. **Hyperfocus Protection**: Gentle intervention for extended sessions
5. **Focus Analytics**: Insights and patterns for focus optimization
6. **Test Suite**: Comprehensive testing including real-time functionality
7. **Documentation**: Focus session guides and best practices

## Integration Points

### Provides to Other Agents
- Focus session data and analytics
- Timer services for other features
- Focus mode activation APIs
- Concentration tracking data

### Requires from Other Agents
- User authentication and preferences (Agent 2)
- Database models and operations (Agent 1)
- Time block integration (Agent 4)
- Notification coordination (Agent 7)

## Commit Strategy

Focus-oriented commits with clear timer functionality:
- `feat(focus): Implement core focus session management`
- `feat(timer): Add real-time timer service with WebSocket updates`
- `feat(modes): Create focus mode profiles and notification shielding`
- `feat(hyperfocus): Add gentle hyperfocus protection system`
- `test(focus): Add comprehensive focus session test suite`
- `docs(focus): Add focus session and timer documentation`

This focus session system will provide the gentle, non-disruptive tools needed to help users with ADHD enter and maintain flow states while respecting their natural attention patterns and hyperfocus tendencies.
