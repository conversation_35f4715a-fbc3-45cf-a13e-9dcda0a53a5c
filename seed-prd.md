# Project Chronos: Technical Architecture & Implementation Plan

## 1. System Overview

### 1.1 Technology Stack
- **Backend**: FastAPI (Python 3.11+)
- **Database**: PostgreSQL 15+ with asyncpg
- **Data Validation**: Pydantic v2
- **Testing**: pytest, pytest-asyncio, behave
- **Containerization**: Docker + Docker Compose
- **Real-time**: WebSockets (FastAPI WebSocket support)
- **Cache**: Redis (for sessions, real-time features)
- **Task Queue**: Celery + Redis (for background tasks, notifications)
- **AI Integration**: OpenAI API / Anthropic Claude API

### 1.2 Code Quality Standards
- **PEP 8**: Style guide compliance via black, flake8
- **PEP 257**: Docstring conventions via pydocstyle
- **PEP 484**: Type hints enforced via mypy
- **Coverage**: 100% unit and integration test coverage via pytest-cov
- **BDD**: Gherkin scenarios via behave

## 2. Project Structure

```
chronos/
├── app/
│   ├── __init__.py
│   ├── main.py                 # FastAPI application entry point
│   ├── core/
│   │   ├── __init__.py
│   │   ├── config.py           # Configuration management
│   │   ├── database.py         # Database connection and session management
│   │   ├── security.py         # Authentication and authorization
│   │   └── exceptions.py       # Custom exception classes
│   ├── models/
│   │   ├── __init__.py
│   │   ├── base.py             # SQLAlchemy base model
│   │   ├── user.py             # User database models
│   │   ├── task.py             # Task and time-block models
│   │   ├── focus.py            # Focus session models
│   │   └── notification.py     # Notification models
│   ├── schemas/
│   │   ├── __init__.py
│   │   ├── user.py             # Pydantic user schemas
│   │   ├── task.py             # Pydantic task schemas
│   │   ├── focus.py            # Pydantic focus schemas
│   │   └── notification.py     # Pydantic notification schemas
│   ├── api/
│   │   ├── __init__.py
│   │   ├── dependencies.py     # FastAPI dependencies
│   │   ├── v1/
│   │   │   ├── __init__.py
│   │   │   ├── auth.py         # Authentication endpoints
│   │   │   ├── users.py        # User management endpoints
│   │   │   ├── tasks.py        # Task management endpoints
│   │   │   ├── timeblocks.py   # Time-blocking endpoints
│   │   │   ├── focus.py        # Focus session endpoints
│   │   │   ├── ai.py           # AI task chunking endpoints
│   │   │   ├── gamification.py # Rewards and motivation endpoints
│   │   │   └── websockets.py   # Real-time features (body doubling)
│   ├── services/
│   │   ├── __init__.py
│   │   ├── user_service.py     # User business logic
│   │   ├── task_service.py     # Task management logic
│   │   ├── time_service.py     # Time-blocking and scheduling logic
│   │   ├── ai_service.py       # AI task chunking service
│   │   ├── notification_service.py # Notification management
│   │   ├── focus_service.py    # Focus mode and timer logic
│   │   └── gamification_service.py # Rewards and motivation logic
│   ├── utils/
│   │   ├── __init__.py
│   │   ├── time_utils.py       # Time calculation utilities
│   │   ├── ai_utils.py         # AI integration utilities
│   │   └── validators.py       # Custom validation functions
│   └── workers/
│       ├── __init__.py
│       ├── celery_app.py       # Celery configuration
│       ├── notification_worker.py # Background notification tasks
│       └── ai_worker.py        # Background AI processing tasks
├── tests/
│   ├── __init__.py
│   ├── conftest.py             # pytest configuration and fixtures
│   ├── unit/
│   │   ├── __init__.py
│   │   ├── test_models/
│   │   ├── test_schemas/
│   │   ├── test_services/
│   │   └── test_utils/
│   ├── integration/
│   │   ├── __init__.py
│   │   ├── test_api/
│   │   └── test_database/
│   └── behavior/
│       ├── __init__.py
│       ├── features/
│       │   ├── task_management.feature
│       │   ├── time_blocking.feature
│       │   ├── focus_sessions.feature
│       │   └── ai_chunking.feature
│       └── steps/
│           ├── __init__.py
│           ├── task_steps.py
│           ├── time_steps.py
│           └── focus_steps.py
├── docker/
│   ├── Dockerfile
│   ├── docker-compose.yml
│   ├── docker-compose.dev.yml
│   └── docker-compose.test.yml
├── migrations/
│   └── alembic/               # Database migration files
├── scripts/
│   ├── setup_dev.sh          # Development environment setup
│   ├── run_tests.sh          # Test execution script
│   └── deploy.sh             # Deployment script
├── requirements/
│   ├── base.txt              # Core dependencies
│   ├── dev.txt               # Development dependencies
│   └── test.txt              # Testing dependencies
├── .github/
│   └── workflows/
│       ├── ci.yml            # Continuous Integration
│       └── cd.yml            # Continuous Deployment
├── pyproject.toml            # Project configuration
├── README.md
└── .env.example              # Environment variable template
```

## 3. Database Schema Design

### 3.1 Core Tables

```sql
-- Users table
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    hashed_password VARCHAR(255) NOT NULL,
    full_name VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    is_verified BOOLEAN DEFAULT FALSE,
    adhd_diagnosed BOOLEAN DEFAULT FALSE,
    timezone VARCHAR(50) DEFAULT 'UTC',
    preferences JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tasks table
CREATE TABLE tasks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(500) NOT NULL,
    description TEXT,
    status VARCHAR(20) DEFAULT 'pending', -- pending, in_progress, completed, cancelled
    priority VARCHAR(10) DEFAULT 'medium', -- low, medium, high, urgent
    energy_level VARCHAR(10) DEFAULT 'medium', -- low, medium, high
    estimated_duration INTEGER, -- minutes
    actual_duration INTEGER, -- minutes
    context_tags TEXT[], -- for adaptive filtering
    is_chunked BOOLEAN DEFAULT FALSE,
    parent_task_id UUID REFERENCES tasks(id) ON DELETE CASCADE,
    due_date TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Time blocks table
CREATE TABLE time_blocks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    task_id UUID REFERENCES tasks(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    start_time TIMESTAMP WITH TIME ZONE NOT NULL,
    end_time TIMESTAMP WITH TIME ZONE NOT NULL,
    buffer_before INTEGER DEFAULT 0, -- minutes
    buffer_after INTEGER DEFAULT 0, -- minutes
    is_flexible BOOLEAN DEFAULT FALSE,
    block_type VARCHAR(20) DEFAULT 'task', -- task, break, buffer, event
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Focus sessions table
CREATE TABLE focus_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    task_id UUID REFERENCES tasks(id) ON DELETE SET NULL,
    session_type VARCHAR(20) DEFAULT 'pomodoro', -- pomodoro, deep_work, sprint
    planned_duration INTEGER NOT NULL, -- minutes
    actual_duration INTEGER, -- minutes
    break_duration INTEGER DEFAULT 5, -- minutes
    status VARCHAR(20) DEFAULT 'planned', -- planned, active, paused, completed, cancelled
    focus_mode_settings JSONB DEFAULT '{}',
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Body doubling sessions table
CREATE TABLE body_doubling_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    host_user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    session_name VARCHAR(255),
    max_participants INTEGER DEFAULT 4,
    planned_duration INTEGER NOT NULL, -- minutes
    status VARCHAR(20) DEFAULT 'waiting', -- waiting, active, completed
    is_public BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE
);

-- Body doubling participants table
CREATE TABLE body_doubling_participants (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID REFERENCES body_doubling_sessions(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    left_at TIMESTAMP WITH TIME ZONE,
    UNIQUE(session_id, user_id)
);

-- Notifications table
CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    task_id UUID REFERENCES tasks(id) ON DELETE CASCADE,
    type VARCHAR(50) NOT NULL, -- reminder, deadline, focus_break, etc.
    title VARCHAR(255) NOT NULL,
    message TEXT,
    scheduled_for TIMESTAMP WITH TIME ZONE NOT NULL,
    sent_at TIMESTAMP WITH TIME ZONE,
    acknowledged_at TIMESTAMP WITH TIME ZONE,
    is_persistent BOOLEAN DEFAULT FALSE,
    priority VARCHAR(10) DEFAULT 'normal', -- low, normal, high
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Gamification table
CREATE TABLE user_gamification (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE UNIQUE,
    total_points INTEGER DEFAULT 0,
    level INTEGER DEFAULT 1,
    tasks_completed INTEGER DEFAULT 0,
    focus_minutes INTEGER DEFAULT 0,
    streak_current INTEGER DEFAULT 0,
    streak_longest INTEGER DEFAULT 0,
    achievements JSONB DEFAULT '[]',
    rewards_unlocked JSONB DEFAULT '[]',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## 4. Pydantic Schemas

### 4.1 Core Schema Examples

```python
# app/schemas/user.py
from datetime import datetime
from typing import Optional, Dict, Any
from uuid import UUID
from pydantic import BaseModel, EmailStr, Field


class UserBase(BaseModel):
    """Base user schema with common fields."""
    
    email: EmailStr = Field(..., description="User's email address")
    full_name: Optional[str] = Field(None, max_length=255, description="User's full name")
    adhd_diagnosed: bool = Field(False, description="Whether user is diagnosed with ADHD")
    timezone: str = Field("UTC", description="User's timezone")
    preferences: Dict[str, Any] = Field(default_factory=dict, description="User preferences")


class UserCreate(UserBase):
    """Schema for user creation."""
    
    password: str = Field(..., min_length=8, description="User's password")


class UserUpdate(BaseModel):
    """Schema for user updates."""
    
    full_name: Optional[str] = Field(None, max_length=255)
    adhd_diagnosed: Optional[bool] = None
    timezone: Optional[str] = None
    preferences: Optional[Dict[str, Any]] = None


class UserInDB(UserBase):
    """Schema for user as stored in database."""
    
    id: UUID
    is_active: bool
    is_verified: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class User(UserInDB):
    """Schema for user responses."""
    pass


# app/schemas/task.py
from datetime import datetime
from typing import Optional, List
from uuid import UUID
from pydantic import BaseModel, Field
from enum import Enum


class TaskStatus(str, Enum):
    """Task status enumeration."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    CANCELLED = "cancelled"


class TaskPriority(str, Enum):
    """Task priority enumeration."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"


class EnergyLevel(str, Enum):
    """Energy level enumeration."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"


class TaskBase(BaseModel):
    """Base task schema."""
    
    title: str = Field(..., max_length=500, description="Task title")
    description: Optional[str] = Field(None, description="Task description")
    priority: TaskPriority = Field(TaskPriority.MEDIUM, description="Task priority")
    energy_level: EnergyLevel = Field(EnergyLevel.MEDIUM, description="Required energy level")
    estimated_duration: Optional[int] = Field(None, gt=0, description="Estimated duration in minutes")
    context_tags: List[str] = Field(default_factory=list, description="Context tags for filtering")
    due_date: Optional[datetime] = Field(None, description="Task due date")


class TaskCreate(TaskBase):
    """Schema for task creation."""
    pass


class TaskUpdate(BaseModel):
    """Schema for task updates."""
    
    title: Optional[str] = Field(None, max_length=500)
    description: Optional[str] = None
    status: Optional[TaskStatus] = None
    priority: Optional[TaskPriority] = None
    energy_level: Optional[EnergyLevel] = None
    estimated_duration: Optional[int] = Field(None, gt=0)
    actual_duration: Optional[int] = Field(None, gt=0)
    context_tags: Optional[List[str]] = None
    due_date: Optional[datetime] = None


class TaskInDB(TaskBase):
    """Schema for task as stored in database."""
    
    id: UUID
    user_id: UUID
    status: TaskStatus
    actual_duration: Optional[int]
    is_chunked: bool
    parent_task_id: Optional[UUID]
    completed_at: Optional[datetime]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class Task(TaskInDB):
    """Schema for task responses."""
    subtasks: List["Task"] = Field(default_factory=list, description="Subtasks if chunked")


class TaskChunkRequest(BaseModel):
    """Schema for AI task chunking request."""
    
    task_id: UUID = Field(..., description="ID of task to chunk")
    chunk_size: str = Field("small", description="Preferred chunk size: small, medium, large")
    context: Optional[str] = Field(None, description="Additional context for AI")
```

## 5. Service Layer Architecture

### 5.1 Task Service Example

```python
# app/services/task_service.py
from typing import List, Optional, Dict, Any
from uuid import UUID
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_
from sqlalchemy.orm import selectinload

from app.models.task import Task as TaskModel
from app.schemas.task import TaskCreate, TaskUpdate, Task, TaskChunkRequest
from app.services.ai_service import AIService
from app.core.exceptions import TaskNotFoundError, TaskValidationError


class TaskService:
    """Service class for task management operations.
    
    This service handles all business logic related to task management,
    including CRUD operations, task chunking, and adaptive filtering.
    """

    def __init__(self, db: AsyncSession, ai_service: AIService) -> None:
        """Initialize the task service.
        
        Args:
            db: Database session
            ai_service: AI service for task chunking
        """
        self.db = db
        self.ai_service = ai_service

    async def create_task(self, user_id: UUID, task_data: TaskCreate) -> Task:
        """Create a new task for the user.
        
        Args:
            user_id: ID of the user creating the task
            task_data: Task creation data
            
        Returns:
            Created task
            
        Raises:
            TaskValidationError: If task data is invalid
        """
        # Validate estimated duration if provided
        if task_data.estimated_duration and task_data.estimated_duration > 480:  # 8 hours
            raise TaskValidationError("Estimated duration cannot exceed 8 hours")
        
        db_task = TaskModel(
            user_id=user_id,
            **task_data.dict()
        )
        
        self.db.add(db_task)
        await self.db.commit()
        await self.db.refresh(db_task)
        
        return Task.from_orm(db_task)

    async def get_task(self, user_id: UUID, task_id: UUID) -> Task:
        """Get a specific task by ID.
        
        Args:
            user_id: ID of the user
            task_id: ID of the task
            
        Returns:
            Task object
            
        Raises:
            TaskNotFoundError: If task doesn't exist or doesn't belong to user
        """
        result = await self.db.execute(
            select(TaskModel)
            .options(selectinload(TaskModel.subtasks))
            .where(and_(TaskModel.id == task_id, TaskModel.user_id == user_id))
        )
        
        task = result.scalar_one_or_none()
        if not task:
            raise TaskNotFoundError(f"Task {task_id} not found")
        
        return Task.from_orm(task)

    async def get_adaptive_tasks(
        self, 
        user_id: UUID,
        energy_level: Optional[str] = None,
        max_duration: Optional[int] = None,
        context_tags: Optional[List[str]] = None
    ) -> List[Task]:
        """Get tasks filtered by user's current state (adaptive task selection).
        
        Args:
            user_id: ID of the user
            energy_level: Current energy level filter
            max_duration: Maximum task duration filter
            context_tags: Context tags to match
            
        Returns:
            List of filtered tasks
        """
        query = select(TaskModel).where(
            and_(
                TaskModel.user_id == user_id,
                TaskModel.status == "pending"
            )
        )
        
        if energy_level:
            query = query.where(TaskModel.energy_level == energy_level)
        
        if max_duration:
            query = query.where(
                or_(
                    TaskModel.estimated_duration <= max_duration,
                    TaskModel.estimated_duration.is_(None)
                )
            )
        
        if context_tags:
            for tag in context_tags:
                query = query.where(TaskModel.context_tags.contains([tag]))
        
        result = await self.db.execute(query.order_by(TaskModel.priority.desc(), TaskModel.created_at))
        tasks = result.scalars().all()
        
        return [Task.from_orm(task) for task in tasks]

    async def chunk_task(self, user_id: UUID, chunk_request: TaskChunkRequest) -> List[Task]:
        """Chunk a large task into smaller subtasks using AI.
        
        Args:
            user_id: ID of the user
            chunk_request: Task chunking request data
            
        Returns:
            List of created subtasks
            
        Raises:
            TaskNotFoundError: If parent task doesn't exist
        """
        # Get the parent task
        parent_task = await self.get_task(user_id, chunk_request.task_id)
        
        # Use AI service to generate subtasks
        subtask_suggestions = await self.ai_service.chunk_task(
            title=parent_task.title,
            description=parent_task.description,
            chunk_size=chunk_request.chunk_size,
            context=chunk_request.context
        )
        
        # Create subtasks
        created_subtasks = []
        for suggestion in subtask_suggestions:
            subtask_data = TaskCreate(
                title=suggestion["title"],
                description=suggestion.get("description"),
                estimated_duration=suggestion.get("estimated_duration"),
                priority=parent_task.priority,
                energy_level=suggestion.get("energy_level", parent_task.energy_level),
                context_tags=parent_task.context_tags
            )
            
            db_subtask = TaskModel(
                user_id=user_id,
                parent_task_id=chunk_request.task_id,
                **subtask_data.dict()
            )
            
            self.db.add(db_subtask)
            created_subtasks.append(db_subtask)
        
        # Mark parent task as chunked
        await self.db.execute(
            TaskModel.__table__.update()
            .where(TaskModel.id == chunk_request.task_id)
            .values(is_chunked=True)
        )
        
        await self.db.commit()
        
        # Refresh and return subtasks
        for subtask in created_subtasks:
            await self.db.refresh(subtask)
        
        return [Task.from_orm(subtask) for subtask in created_subtasks]

    async def get_task_jar_selection(self, user_id: UUID, jar_size: int = 5) -> List[Task]:
        """Get a random selection of tasks for the 'task jar' feature.
        
        Args:
            user_id: ID of the user
            jar_size: Number of tasks to include in selection
            
        Returns:
            Random selection of pending tasks
        """
        # Get pending tasks suitable for random selection
        result = await self.db.execute(
            select(TaskModel)
            .where(and_(
                TaskModel.user_id == user_id,
                TaskModel.status == "pending",
                TaskModel.parent_task_id.is_(None)  # Only parent tasks
            ))
            .order_by(func.random())
            .limit(jar_size)
        )
        
        tasks = result.scalars().all()
        return [Task.from_orm(task) for task in tasks]
```

## 6. Testing Strategy

### 6.1 Unit Tests Structure

```python
# tests/unit/test_services/test_task_service.py
import pytest
from unittest.mock import AsyncMock, Mock
from uuid import uuid4
from datetime import datetime, timedelta

from app.services.task_service import TaskService
from app.schemas.task import TaskCreate, TaskChunkRequest
from app.core.exceptions import TaskNotFoundError, TaskValidationError


class TestTaskService:
    """Unit tests for TaskService class."""

    @pytest.fixture
    def mock_db(self):
        """Mock database session."""
        return AsyncMock()

    @pytest.fixture
    def mock_ai_service(self):
        """Mock AI service."""
        return AsyncMock()

    @pytest.fixture
    def task_service(self, mock_db, mock_ai_service):
        """Create TaskService instance with mocked dependencies."""
        return TaskService(db=mock_db, ai_service=mock_ai_service)

    @pytest.mark.asyncio
    async def test_create_task_success(self, task_service, mock_db):
        """Test successful task creation."""
        # Arrange
        user_id = uuid4()
        task_data = TaskCreate(
            title="Test Task",
            description="Test Description",
            estimated_duration=60
        )
        
        # Act
        result = await task_service.create_task(user_id, task_data)
        
        # Assert
        assert result.title == task_data.title
        assert result.description == task_data.description
        assert result.estimated_duration == task_data.estimated_duration
        mock_db.add.assert_called_once()
        mock_db.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_create_task_duration_validation_error(self, task_service):
        """Test task creation with invalid duration raises validation error."""
        # Arrange
        user_id = uuid4()
        task_data = TaskCreate(
            title="Test Task",
            estimated_duration=500  # Over 8 hours
        )
        
        # Act & Assert
        with pytest.raises(TaskValidationError, match="Estimated duration cannot exceed 8 hours"):
            await task_service.create_task(user_id, task_data)

    @pytest.mark.asyncio
    async def test_get_task_not_found(self, task_service, mock_db):
        """Test get_task raises TaskNotFoundError when task doesn't exist."""
        # Arrange
        user_id = uuid4()
        task_id = uuid4()
        mock_db.execute.return_value.scalar_one_or_none.return_value = None
        
        # Act & Assert
        with pytest.raises(TaskNotFoundError, match=f"Task {task_id} not found"):
            await task_service.get_task(user_id, task_id)

    @pytest.mark.asyncio
    async def test_chunk_task_success(self, task_service, mock_db, mock_ai_service):
        """Test successful task chunking."""
        # Arrange
        user_id = uuid4()
        task_id = uuid4()
        
        # Mock parent task
        mock_parent_task = Mock()
        mock_parent_task.title = "Large Project"
        mock_parent_task.description = "Complex project description"
        mock_parent_task.priority = "high"
        mock_parent_task.energy_level = "medium"
        mock_parent_task.context_tags = ["work"]
        
        # Mock AI response
        mock_ai_service.chunk_task.return_value = [
            {
                "title": "Subtask 1",
                "description": "First subtask",
                "estimated_duration": 30,
                "energy_level": "low"
            },
            {
                "title": "Subtask 2", 
                "description": "Second subtask",
                "estimated_duration": 45,
                "energy_level": "medium"
            }
        ]
        
        task_service.get_task = AsyncMock(return_value=mock_parent_task)
        
        chunk_request = TaskChunkRequest(
            task_id=task_id,
            chunk_size="small"
        )
        
        # Act
        result = await task_service.chunk_task(user_id, chunk_request)
        
        # Assert
        assert len(result) == 2
        assert result[0].title == "Subtask 1"
        assert result[1].title == "Subtask 2"
        mock_ai_service.chunk_task.assert_called_once()
        assert mock_db.add.call_count == 2
        mock_db.commit.assert_called_once()
```

### 6.2 BDD Feature Tests

```gherkin
# tests/behavior/features/task_management.feature
Feature: Task Management
  As a user with ADHD
  I want to manage my tasks effectively
  So that I can stay organized and productive

  Background:
    Given I am a registered user
    And I am logged in

  Scenario: Creating a simple task
    When I create a task with title "Buy groceries"
    And I set the estimated duration to 45 minutes
    And I set the energy level to "low"
    And I add context tags "errands", "weekend"
    Then the task should be created successfully
    And the task should appear in my task list
    And the task status should be "pending"

  Scenario: Breaking down a large task with AI
    Given I have a task titled "Prepare quarterly presentation"
    When I request to chunk the task into "small" pieces
    Then the AI should suggest multiple subtasks
    And each subtask should have a clear action
    And the subtasks should have appropriate time estimates
    And the original task should be marked as "chunked"

  Scenario: Adaptive task filtering for low energy
    Given I have multiple tasks with different energy requirements
    And I have tasks tagged with "quick", "admin", and "creative"
    When I filter tasks for "low" energy level
    And I set maximum duration to 30 minutes
    Then I should only see tasks matching those criteria
    And the tasks should be ordered by priority

  Scenario: Task jar selection for decision paralysis
    Given I have 10 pending tasks
    When I request a task jar selection
    Then I should receive 5 randomly selected tasks
    And all tasks should be parent tasks (not subtasks)
    And the selection should help me overcome choice paralysis
```

```python
# tests/behavior/steps/task_steps.py
from behave import given, when, then
from uuid import uuid4
import asyncio

from app.schemas.task import TaskCreate, TaskChunkRequest
from tests.factories import TaskFactory, UserFactory


@given("I am a registered user")
def step_given_registered_user(context):
    """Create a test user."""
    context.user = UserFactory()
    context.user_id = context.user.id


@given("I am logged in")
def step_given_logged_in(context):
    """Set up authentication context."""
    context.auth_token = "test_token"  # Mock token


@when('I create a task with title "{title}"')
def step_when_create_task(context, title):
    """Create a task with given title."""
    context.task_data = TaskCreate(title=title)


@when("I set the estimated duration to {duration:d} minutes")
def step_when_set_duration(context, duration):
    """Set estimated duration for the task."""
    context.task_data.estimated_duration = duration


@when('I set the energy level to "{energy_level}"')
def step_when_set_energy_level(context, energy_level):
    """Set energy level for the task."""
    context.task_data.energy_level = energy_level


@when("I add context tags {tags}")
def step_when_add_context_tags(context, tags):
    """Add context tags to the task."""
    tag_list = [tag.strip('"') for tag in tags.split(", ")]
    context.task_data.context_tags = tag_list


@then("the task should be created successfully")
def step_then_task_created(context):
    """Verify task creation."""
    # This would interact with the actual API in integration tests
    loop = asyncio.get_event_loop()
    context.created_task = loop.run_until_complete(
        context.task_service.create_task(context.user_id, context.task_data)
    )
    assert context.created_task is not None
    assert context.created_task.title == context.task_data.title


@then("the task should appear in my task list")
def step_then_task_in_list(context):
    """Verify task appears in user's task list."""
    loop = asyncio.get_event_loop()
    tasks = loop.run_until_complete(
        context.task_service.get_user_tasks(context.user_id)
    )
    task_titles = [task.title for task in tasks]
    assert context.task_data.title in task_titles


@then('the task status should be "{status}"')
def step_then_task_status(context, status):
    """Verify task status."""
    assert context.created_task.status == status
```

## 7. Docker Configuration

### 7.1 Main Dockerfile

```dockerfile
# docker/Dockerfile
FROM python:3.11-slim

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    POETRY_NO_INTERACTION=1 \
    POETRY_VENV_IN_PROJECT=1 \
    POETRY_CACHE_DIR=/tmp/poetry_cache

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    libpq-dev \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Install Poetry
RUN pip install poetry

# Set work directory
WORKDIR /app

# Copy poetry files
COPY pyproject.toml poetry.lock ./

# Install dependencies
RUN poetry install --only=main && rm -rf $POETRY_CACHE_DIR

# Copy application code
COPY . .

# Create non-root user
RUN groupadd -r appuser && useradd -r -g appuser appuser
RUN chown -R appuser:appuser /app
USER appuser

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Run application
CMD ["poetry", "run", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### 7.2 Docker Compose

```yaml
# docker/docker-compose.yml
version: '3.8'

services:
  chronos-api:
    build:
      context: ..
      dockerfile: docker/Dockerfile
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=postgresql+asyncpg://chronos:chronos_password@db:5432/chronos
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=${SECRET_KEY:-development_secret_key}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ../logs:/app/logs
    networks:
      - chronos-network

  chronos-worker:
    build:
      context: ..
      dockerfile: docker/Dockerfile
    command: ["poetry", "run", "celery", "worker", "-A", "app.workers.celery_app", "--loglevel=info"]
    environment:
      - DATABASE_URL=postgresql+asyncpg://chronos:chronos_password@db:5432/chronos
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=${SECRET_KEY:-development_secret_key}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - chronos-network

  db:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=chronos
      - POSTGRES_USER=chronos
      - POSTGRES_PASSWORD=chronos_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ../scripts/init_db.sql:/docker-entrypoint-initdb.d/init.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U chronos"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - chronos-network

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - chronos-network

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ../nginx/nginx.conf:/etc/nginx/nginx.conf
      - ../nginx/ssl:/etc/nginx/ssl
    depends_on:
      - chronos-api
    networks:
      - chronos-network

volumes:
  postgres_data:
  redis_data:

networks:
  chronos-network:
    driver: bridge
```

## 8. CI/CD Pipeline

### 8.1 GitHub Actions Workflow

```yaml
# .github/workflows/ci.yml
name: CI Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15-alpine
        env:
          POSTGRES_PASSWORD: test_password
          POSTGRES_USER: test_user
          POSTGRES_DB: test_chronos
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      
      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python 3.11
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Install Poetry
      uses: snok/install-poetry@v1
      with:
        version: latest
        virtualenvs-create: true
        virtualenvs-in-project: true
    
    - name: Load cached venv
      id: cached-poetry-dependencies
      uses: actions/cache@v3
      with:
        path: .venv
        key: venv-${{ runner.os }}-${{ steps.setup-python.outputs.python-version }}-${{ hashFiles('**/poetry.lock') }}
    
    - name: Install dependencies
      if: steps.cached-poetry-dependencies.outputs.cache-hit != 'true'
      run: poetry install --no-interaction --no-root
    
    - name: Install project
      run: poetry install --no-interaction
    
    - name: Run code quality checks
      run: |
        poetry run black --check .
        poetry run flake8 .
        poetry run mypy .
        poetry run pydocstyle app/
    
    - name: Run unit tests
      run: |
        poetry run pytest tests/unit/ -v --cov=app --cov-report=xml --cov-report=term-missing --cov-fail-under=100
      env:
        DATABASE_URL: postgresql+asyncpg://test_user:test_password@localhost:5432/test_chronos
        REDIS_URL: redis://localhost:6379/1
    
    - name: Run integration tests
      run: |
        poetry run pytest tests/integration/ -v
      env:
        DATABASE_URL: postgresql+asyncpg://test_user:test_password@localhost:5432/test_chronos
        REDIS_URL: redis://localhost:6379/1
    
    - name: Run behavior tests
      run: |
        poetry run behave tests/behavior/
      env:
        DATABASE_URL: postgresql+asyncpg://test_user:test_password@localhost:5432/test_chronos
        REDIS_URL: redis://localhost:6379/1
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella

  security:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install bandit safety
    
    - name: Run security checks
      run: |
        bandit -r app/
        safety check

  docker:
    runs-on: ubuntu-latest
    needs: [test, security]
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v2
    
    - name: Login to Container Registry
      uses: docker/login-action@v2
      with:
        registry: ghcr.io
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
    
    - name: Build and push
      uses: docker/build-push-action@v4
      with:
        context: .
        file: ./docker/Dockerfile
        push: true
        tags: |
          ghcr.io/${{ github.repository }}:latest
          ghcr.io/${{ github.repository }}:${{ github.sha }}
```

## 9. Development Workflow

### 9.1 Setup Script

```bash
#!/bin/bash
# scripts/setup_dev.sh

set -e

echo "🚀 Setting up Project Chronos development environment..."

# Check if Poetry is installed
if ! command -v poetry &> /dev/null; then
    echo "Poetry not found. Please install Poetry first."
    exit 1
fi

# Install dependencies
echo "📦 Installing dependencies..."
poetry install

# Set up pre-commit hooks
echo "🔧 Setting up pre-commit hooks..."
poetry run pre-commit install

# Copy environment file
if [ ! -f .env ]; then
    echo "📝 Creating .env file..."
    cp .env.example .env
    echo "Please update .env with your configuration"
fi

# Start services
echo "🐳 Starting Docker services..."
docker-compose -f docker/docker-compose.dev.yml up -d db redis

# Wait for services
echo "⏳ Waiting for services to be ready..."
sleep 10

# Run migrations
echo "🗄️ Running database migrations..."
poetry run alembic upgrade head

# Create test data
echo "🌱 Creating test data..."
poetry run python scripts/create_test_data.py

echo "✅ Development environment setup complete!"
echo ""
echo "To start the application:"
echo "  poetry run uvicorn app.main:app --reload"
echo ""
echo "To run tests:"
echo "  poetry run pytest"
echo ""
echo "To run BDD tests:"
echo "  poetry run behave"
```

## 10. Deployment Considerations

### 10.1 Production Environment Variables

```bash
# .env.production
# Database
DATABASE_URL=postgresql+asyncpg://username:password@host:5432/chronos

# Redis
REDIS_URL=redis://redis-host:6379/0

# Security
SECRET_KEY=your-super-secret-key-here
JWT_SECRET_KEY=your-jwt-secret-here
ENCRYPTION_KEY=your-encryption-key-here

# AI Services
OPENAI_API_KEY=your-openai-api-key
ANTHROPIC_API_KEY=your-anthropic-api-key

# External Services
SMTP_SERVER=smtp.mailgun.org
SMTP_PORT=587
SMTP_USERNAME=your-smtp-username
SMTP_PASSWORD=your-smtp-password

# Monitoring
SENTRY_DSN=your-sentry-dsn
LOG_LEVEL=INFO

# Rate Limiting
RATE_LIMIT_PER_MINUTE=100
RATE_LIMIT_PER_HOUR=1000
```

This architecture provides a solid foundation for building Project Chronos with all the specified requirements. The system is designed to be scalable, maintainable, and thoroughly tested while specifically addressing the unique needs of users with ADHD.