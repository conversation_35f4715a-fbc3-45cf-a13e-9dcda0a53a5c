# Agent 8: Gamification & Motivation Agent PRD

## Agent Overview

**Agent Name**: Gamification & Motivation Agent  
**Primary Responsibility**: Rewards system, achievements, streaks, user motivation features  
**Dependencies**: Agent 1 (Core Infrastructure), Agent 2 (Authentication), Agent 3 (Task Management)  
**Deliverables**: Dopamine menu system, achievement tracking, streak management, reward mechanics, motivation analytics

## Mission Statement

Address the dopamine deficit that makes task initiation difficult for users with ADHD by providing intrinsic motivation through gamification, rewards, and achievement systems that work with the interest-based nervous system rather than against it.

## Technical Specifications

### Technology Stack
- **Points System**: Redis for real-time point calculations
- **Achievement Engine**: Rule-based achievement detection
- **Streak Tracking**: Time-series data for consistency monitoring
- **Reward Management**: Flexible reward system with user customization
- **Analytics**: Motivation pattern analysis and insights

### Core Responsibilities

#### 1. Gamification System
```python
# app/services/gamification_service.py - Core gamification logic
# app/models/gamification.py - Gamification database models
# app/schemas/gamification.py - Gamification Pydantic schemas
# app/utils/gamification_utils.py - Point calculations and utilities
```

#### 2. Achievement System
```python
# app/services/achievement_service.py - Achievement tracking and unlocking
# app/models/achievement.py - Achievement definitions and progress
# app/utils/achievement_utils.py - Achievement rule engine
```

#### 3. Motivation System
```python
# app/services/motivation_service.py - Dopamine menu and motivation tools
# app/utils/motivation_utils.py - Motivation pattern analysis
# app/schemas/motivation.py - Motivation system schemas
```

## Key Features & User Stories

### Feature 1: Gamified Motivation System ("Dopamine Menu")
**User Story**: "As a user who can't start boring tasks, I want to gamify my to-do list by earning rewards or getting a prompt to do something fun first, so I can generate the motivation needed to get started."

**Technical Requirements**:
- Customizable "dopamine menu" of quick, enjoyable pre-task activities
- Point-based reward system for task completion
- Virtual rewards (themes, badges, avatars) and real-world reward integration
- Task difficulty multipliers for point calculation
- Seasonal events and special challenges

### Feature 2: Achievement & Progress Tracking
**User Story**: "As a user who needs external validation and progress markers, I want to unlock achievements and see my progress visually, so I feel accomplished and motivated to continue."

**Technical Requirements**:
- Comprehensive achievement system (task completion, streaks, milestones)
- Visual progress indicators and level progression
- Badge collection and showcase system
- Social sharing of achievements (optional)
- Personalized achievement recommendations

### Feature 3: Streak Management & Consistency Rewards
**User Story**: "As a user who struggles with consistency, I want to build and maintain streaks that acknowledge my efforts even on difficult days, so I stay motivated without perfectionist pressure."

**Technical Requirements**:
- Flexible streak definitions (daily tasks, focus sessions, etc.)
- "Streak freeze" options for ADHD-friendly flexibility
- Streak recovery mechanisms for missed days
- Consistency rewards that don't require perfection
- Visual streak tracking with motivational milestones

## Gamification Service Implementation

### Core Gamification Service
```python
# app/services/gamification_service.py
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from uuid import UUID
from enum import Enum

class RewardType(str, Enum):
    """Types of rewards available."""
    POINTS = "points"
    BADGE = "badge"
    THEME = "theme"
    AVATAR = "avatar"
    REAL_WORLD = "real_world"

class AchievementCategory(str, Enum):
    """Categories of achievements."""
    TASK_COMPLETION = "task_completion"
    CONSISTENCY = "consistency"
    FOCUS_TIME = "focus_time"
    SOCIAL = "social"
    MILESTONE = "milestone"

class GamificationService:
    """Service for managing ADHD-optimized gamification features."""
    
    def __init__(self, db: AsyncSession, redis: Redis):
        self.db = db
        self.redis = redis
    
    async def award_points(
        self,
        user_id: UUID,
        points: int,
        reason: str,
        task_id: Optional[UUID] = None,
        multiplier: float = 1.0
    ) -> PointsAward:
        """
        Award points to user with ADHD-optimized feedback.
        
        Args:
            user_id: User receiving points
            points: Base points to award
            reason: Reason for point award
            task_id: Associated task (if applicable)
            multiplier: Difficulty/energy multiplier
            
        Returns:
            Points award record with celebration data
        """
        
        final_points = int(points * multiplier)
        
        # Get current user gamification data
        user_gamification = await self.get_user_gamification(user_id)
        
        # Calculate new totals
        new_total = user_gamification.total_points + final_points
        old_level = user_gamification.level
        new_level = self._calculate_level(new_total)
        
        # Update user gamification record
        user_gamification.total_points = new_total
        user_gamification.level = new_level
        
        # Create points award record
        award = PointsAward(
            user_id=user_id,
            points_awarded=final_points,
            reason=reason,
            task_id=task_id,
            multiplier=multiplier,
            total_points_after=new_total
        )
        
        self.db.add(award)
        await self.db.commit()
        
        # Check for level up celebration
        if new_level > old_level:
            await self._trigger_level_up_celebration(user_id, new_level)
        
        # Check for new achievements
        await self._check_achievements(user_id, "points_awarded", final_points)
        
        return award
    
    async def get_dopamine_menu(
        self,
        user_id: UUID,
        current_energy: str = "medium",
        available_time: int = 5
    ) -> List[DopamineActivity]:
        """
        Get personalized dopamine menu activities.
        
        Args:
            user_id: User requesting menu
            current_energy: Current energy level
            available_time: Available time in minutes
            
        Returns:
            List of suggested dopamine activities
        """
        
        # Get user's preferred activities
        user_prefs = await self.get_user_dopamine_preferences(user_id)
        
        # Filter activities by energy and time
        suitable_activities = []
        for activity in user_prefs.activities:
            if (activity.energy_requirement <= current_energy and 
                activity.duration <= available_time):
                suitable_activities.append(activity)
        
        # Add default activities if user hasn't customized
        if not suitable_activities:
            suitable_activities = self._get_default_dopamine_activities(
                current_energy, available_time
            )
        
        return suitable_activities[:5]  # Return top 5 suggestions
    
    async def update_streak(
        self,
        user_id: UUID,
        streak_type: str,
        action_completed: bool = True
    ) -> StreakUpdate:
        """
        Update user streak with ADHD-friendly flexibility.
        
        Args:
            user_id: User whose streak to update
            streak_type: Type of streak (daily_tasks, focus_sessions, etc.)
            action_completed: Whether the streak action was completed today
            
        Returns:
            Streak update information
        """
        
        streak = await self.get_user_streak(user_id, streak_type)
        today = datetime.utcnow().date()
        
        if action_completed:
            if streak.last_activity_date == today:
                # Already completed today, no change
                return StreakUpdate(streak=streak, changed=False)
            elif streak.last_activity_date == today - timedelta(days=1):
                # Continuing streak
                streak.current_streak += 1
                streak.last_activity_date = today
            else:
                # Streak broken, restart
                streak.current_streak = 1
                streak.last_activity_date = today
            
            # Update longest streak if needed
            if streak.current_streak > streak.longest_streak:
                streak.longest_streak = streak.current_streak
        
        await self.db.commit()
        
        # Check for streak achievements
        await self._check_achievements(user_id, "streak_milestone", streak.current_streak)
        
        return StreakUpdate(streak=streak, changed=True)
    
    def _calculate_level(self, total_points: int) -> int:
        """Calculate user level based on total points with ADHD-friendly progression."""
        # Progressive leveling that doesn't become impossibly difficult
        if total_points < 100:
            return 1
        elif total_points < 300:
            return 2
        elif total_points < 600:
            return 3
        else:
            # Logarithmic progression to prevent level stagnation
            import math
            return min(50, int(3 + math.log10(total_points - 500)))
```

### Achievement System
```python
# app/services/achievement_service.py
class AchievementService:
    """Service for managing achievements and unlockables."""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.achievement_rules = self._load_achievement_rules()
    
    async def check_achievements(
        self,
        user_id: UUID,
        trigger_event: str,
        event_data: Dict
    ) -> List[Achievement]:
        """
        Check if user has unlocked new achievements.
        
        Args:
            user_id: User to check achievements for
            trigger_event: Event that triggered the check
            event_data: Data associated with the event
            
        Returns:
            List of newly unlocked achievements
        """
        
        newly_unlocked = []
        user_achievements = await self.get_user_achievements(user_id)
        unlocked_ids = {ach.achievement_id for ach in user_achievements}
        
        for rule in self.achievement_rules:
            if (rule.trigger_event == trigger_event and 
                rule.id not in unlocked_ids):
                
                if await self._evaluate_achievement_rule(user_id, rule, event_data):
                    achievement = await self._unlock_achievement(user_id, rule)
                    newly_unlocked.append(achievement)
        
        return newly_unlocked
    
    def _load_achievement_rules(self) -> List[AchievementRule]:
        """Load achievement rules with ADHD-friendly milestones."""
        return [
            # Task completion achievements
            AchievementRule(
                id="first_task",
                name="Getting Started",
                description="Complete your first task",
                trigger_event="task_completed",
                condition=lambda user_id, data: data.get("task_count", 0) >= 1,
                reward_points=50,
                badge_icon="🎯"
            ),
            AchievementRule(
                id="task_streak_7",
                name="Week Warrior",
                description="Complete tasks for 7 days in a row",
                trigger_event="streak_milestone",
                condition=lambda user_id, data: data.get("streak_length", 0) >= 7,
                reward_points=200,
                badge_icon="🔥"
            ),
            # Focus session achievements
            AchievementRule(
                id="focus_master",
                name="Focus Master",
                description="Complete 25 focus sessions",
                trigger_event="focus_completed",
                condition=lambda user_id, data: data.get("total_sessions", 0) >= 25,
                reward_points=300,
                badge_icon="🧘"
            ),
            # Social achievements
            AchievementRule(
                id="body_double_buddy",
                name="Body Double Buddy",
                description="Complete 10 body doubling sessions",
                trigger_event="body_doubling_completed",
                condition=lambda user_id, data: data.get("sessions_completed", 0) >= 10,
                reward_points=250,
                badge_icon="👥"
            )
        ]
```

## API Endpoints

### Gamification Endpoints
```python
# app/api/v1/gamification.py
@router.get("/profile", response_model=GamificationProfileResponse)
async def get_gamification_profile(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get user's complete gamification profile."""

@router.get("/dopamine-menu", response_model=List[DopamineActivityResponse])
async def get_dopamine_menu(
    energy_level: str = "medium",
    available_time: int = 5,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get personalized dopamine menu activities."""

@router.post("/dopamine-menu/complete")
async def complete_dopamine_activity(
    activity_data: DopamineActivityCompletion,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Mark dopamine activity as completed."""

@router.get("/achievements", response_model=List[AchievementResponse])
async def get_achievements(
    unlocked_only: bool = False,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get user achievements with progress."""

@router.get("/streaks", response_model=List[StreakResponse])
async def get_streaks(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get user's current streaks."""

@router.post("/rewards/claim/{reward_id}")
async def claim_reward(
    reward_id: UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Claim an earned reward."""
```

## Implementation Plan

### Phase 1: Core Gamification (Week 1)
1. Implement points system with Redis caching
2. Create basic reward mechanics and user levels
3. Set up gamification database models
4. Implement point calculation with multipliers

### Phase 2: Achievement System (Week 2)
1. Develop achievement rule engine
2. Create achievement tracking and unlocking
3. Implement badge system and visual rewards
4. Add achievement notification integration

### Phase 3: Motivation Features (Week 3)
1. Build dopamine menu system
2. Implement streak tracking with flexibility
3. Create motivation analytics and insights
4. Add customizable reward preferences

### Phase 4: Advanced Features & Testing (Week 4)
1. Implement social features and sharing
2. Add seasonal events and special challenges
3. Create comprehensive testing suite
4. Optimize performance and user experience

## Testing Strategy

### Unit Tests
```python
# tests/unit/test_services/test_gamification_service.py
class TestGamificationService:
    async def test_award_points_with_multiplier(self):
        """Test point awarding with difficulty multipliers."""
    
    async def test_level_calculation_progression(self):
        """Test user level calculation and progression."""
    
    async def test_dopamine_menu_personalization(self):
        """Test personalized dopamine menu generation."""

# tests/unit/test_services/test_achievement_service.py
class TestAchievementService:
    async def test_achievement_unlocking(self):
        """Test achievement rule evaluation and unlocking."""
    
    async def test_streak_achievement_triggers(self):
        """Test streak-based achievement triggers."""
```

### BDD Scenarios
```gherkin
Feature: Gamified Motivation System
  Scenario: Earning points for task completion
    Given I complete a difficult task
    When the system calculates my reward
    Then I should receive points with a difficulty multiplier
    And I should see a celebration animation
    And my progress toward the next level should update

Feature: Achievement System
  Scenario: Unlocking first task achievement
    Given I am a new user with no completed tasks
    When I complete my first task
    Then I should unlock the "Getting Started" achievement
    And receive bonus points for the milestone
    And see a badge added to my profile

Feature: Flexible Streak System
  Scenario: Maintaining streak with ADHD flexibility
    Given I have a 5-day task completion streak
    When I miss a day due to ADHD challenges
    And I use a "streak freeze" option
    Then my streak should be preserved
    And I should be encouraged to continue tomorrow
```

## Quality Standards

### Performance Requirements
- Point calculations < 100ms
- Achievement checks < 200ms
- Dopamine menu generation < 150ms
- Real-time gamification updates

### User Experience Requirements
- Immediate positive feedback for actions
- Non-overwhelming celebration animations
- Clear progress visualization
- Flexible, forgiving streak mechanics

## Success Metrics

### Engagement Metrics
- 60% of users engage with gamification features
- 40% increase in task completion rates
- 50% of users customize dopamine menu
- 70% of users unlock at least 5 achievements

### Motivation Impact
- 45% reduction in task initiation time
- 35% increase in consecutive day usage
- 80% user satisfaction with reward system
- 30% improvement in streak maintenance

## Deliverables

1. **Gamification System**: Complete points, levels, and rewards system
2. **Achievement Engine**: Rule-based achievement tracking and unlocking
3. **Dopamine Menu**: Personalized pre-task motivation activities
4. **Streak Management**: Flexible streak tracking with ADHD accommodations
5. **Motivation Analytics**: Insights into user motivation patterns
6. **Test Suite**: Comprehensive gamification feature testing
7. **Documentation**: Gamification system and motivation guides

## Integration Points

### Provides to Other Agents
- Gamification APIs and point awarding
- Achievement tracking and celebration
- Motivation pattern data and insights
- Reward system integration

### Requires from Other Agents
- Task completion data (Agent 3)
- Focus session data (Agent 5)
- User authentication and preferences (Agent 2)
- Notification system for celebrations (Agent 7)

## Commit Strategy

Motivation-focused commits with clear gamification features:
- `feat(gamification): Implement points system with ADHD-friendly progression`
- `feat(achievements): Add comprehensive achievement system with rule engine`
- `feat(dopamine): Create personalized dopamine menu for task motivation`
- `feat(streaks): Implement flexible streak system with freeze options`
- `test(gamification): Add comprehensive gamification test suite`
- `docs(motivation): Add gamification and motivation system documentation`

This gamification system will provide the dopamine-driven motivation that users with ADHD need to overcome task initiation barriers while maintaining engagement through flexible, forgiving reward mechanics.
