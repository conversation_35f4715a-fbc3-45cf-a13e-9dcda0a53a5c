#!/usr/bin/env python3
"""
Initialize gamification system for Project Chronos.

This script initializes the gamification system by creating default
achievements and dopamine activities.
"""

import asyncio
import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker

from app.core.config import settings
from app.models.base import Base
from app.services.achievement_service import AchievementService
from app.services.motivation_service import MotivationService


async def create_tables(engine):
    """Create all database tables."""
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    print("✓ Database tables created")


async def initialize_achievements(db: AsyncSession):
    """Initialize default achievements."""
    achievement_service = AchievementService(db)
    await achievement_service.initialize_achievements()
    print("✓ Default achievements initialized")


async def initialize_dopamine_activities(db: AsyncSession):
    """Initialize default dopamine activities."""
    motivation_service = MotivationService(db)
    await motivation_service.initialize_default_activities()
    print("✓ Default dopamine activities initialized")


async def main():
    """Main initialization function."""
    print("🚀 Initializing Project Chronos Gamification System...")
    
    # Create database engine
    engine = create_async_engine(
        settings.DATABASE_URL,
        echo=True if settings.DEBUG else False
    )
    
    # Create session factory
    async_session = sessionmaker(
        engine, class_=AsyncSession, expire_on_commit=False
    )
    
    try:
        # Create tables
        await create_tables(engine)
        
        # Initialize data
        async with async_session() as db:
            await initialize_achievements(db)
            await initialize_dopamine_activities(db)
        
        print("\n🎉 Gamification system initialized successfully!")
        print("\nAvailable features:")
        print("  • Points and leveling system")
        print("  • Achievement tracking")
        print("  • Streak management")
        print("  • Dopamine menu activities")
        print("  • Motivation insights")
        
    except Exception as e:
        print(f"\n❌ Error initializing gamification system: {e}")
        sys.exit(1)
    
    finally:
        await engine.dispose()


if __name__ == "__main__":
    asyncio.run(main())
