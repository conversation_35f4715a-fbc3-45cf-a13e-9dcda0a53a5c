#!/usr/bin/env python3
"""
Comprehensive test runner for Project Chronos ADHD-focused features.

This script coordinates test execution across unit, integration, and BDD tests
with special attention to ADHD-specific functionality validation.
"""

import subprocess
import sys
import json
import os
from pathlib import Path
from typing import Dict, List, Any, Optional
import argparse
import time
from datetime import datetime


class TestRunner:
    """
    Comprehensive test runner for ADHD-focused application.
    
    Coordinates execution of all test types with detailed reporting
    and ADHD-specific feature validation.
    """
    
    def __init__(self, project_root: Path):
        """Initialize test runner."""
        self.project_root = project_root
        self.results: Dict[str, Any] = {}
        self.start_time = time.time()
        
        # Test environment setup
        self.test_env = {
            "DATABASE_URL": "postgresql+asyncpg://test:test@localhost:5432/test_chronos",
            "REDIS_URL": "redis://localhost:6379/1",
            "ENVIRONMENT": "testing",
            "PYTHONPATH": str(project_root),
            **os.environ
        }
    
    def run_command(self, command: List[str], test_type: str, timeout: int = 600) -> Dict[str, Any]:
        """
        Run a test command and capture detailed results.
        
        Args:
            command: Command to run
            test_type: Type of test for reporting
            timeout: Command timeout in seconds
            
        Returns:
            Test result dictionary
        """
        print(f"🧪 Running {test_type} tests...")
        start_time = time.time()
        
        try:
            result = subprocess.run(
                command,
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=timeout,
                env=self.test_env
            )
            
            duration = time.time() - start_time
            success = result.returncode == 0
            
            test_result = {
                "test_type": test_type,
                "success": success,
                "return_code": result.returncode,
                "duration": duration,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "command": " ".join(command)
            }
            
            # Parse test output for additional metrics
            if "pytest" in command[0] or "pytest" in command[1:]:
                test_result.update(self._parse_pytest_output(result.stdout))
            elif "behave" in command:
                test_result.update(self._parse_behave_output(result.stdout))
            
            self.results[test_type] = test_result
            
            if success:
                print(f"✅ {test_type} tests passed in {duration:.2f}s")
            else:
                print(f"❌ {test_type} tests failed in {duration:.2f}s")
                if result.stderr:
                    print(f"Error output:\n{result.stderr}")
            
            return test_result
            
        except subprocess.TimeoutExpired:
            duration = time.time() - start_time
            error_msg = f"{test_type} tests timed out after {timeout}s"
            
            test_result = {
                "test_type": test_type,
                "success": False,
                "return_code": -1,
                "duration": duration,
                "error": error_msg,
                "command": " ".join(command)
            }
            
            self.results[test_type] = test_result
            print(f"⏰ {error_msg}")
            return test_result
            
        except Exception as e:
            duration = time.time() - start_time
            error_msg = f"{test_type} tests failed with exception: {str(e)}"
            
            test_result = {
                "test_type": test_type,
                "success": False,
                "return_code": -1,
                "duration": duration,
                "error": error_msg,
                "command": " ".join(command)
            }
            
            self.results[test_type] = test_result
            print(f"💥 {error_msg}")
            return test_result
    
    def _parse_pytest_output(self, output: str) -> Dict[str, Any]:
        """Parse pytest output for test metrics."""
        metrics = {}
        
        # Look for test summary line
        lines = output.split('\n')
        for line in lines:
            if 'passed' in line and ('failed' in line or 'error' in line or 'skipped' in line):
                # Parse line like "5 passed, 2 failed, 1 skipped in 10.23s"
                parts = line.split()
                for i, part in enumerate(parts):
                    if part == 'passed' and i > 0:
                        metrics['passed'] = int(parts[i-1])
                    elif part == 'failed' and i > 0:
                        metrics['failed'] = int(parts[i-1])
                    elif part == 'skipped' and i > 0:
                        metrics['skipped'] = int(parts[i-1])
                    elif part == 'error' and i > 0:
                        metrics['errors'] = int(parts[i-1])
                break
        
        # Look for coverage information
        for line in lines:
            if 'TOTAL' in line and '%' in line:
                # Parse coverage line
                parts = line.split()
                for part in parts:
                    if '%' in part:
                        try:
                            coverage = int(part.replace('%', ''))
                            metrics['coverage'] = coverage
                        except ValueError:
                            pass
                break
        
        return metrics
    
    def _parse_behave_output(self, output: str) -> Dict[str, Any]:
        """Parse behave output for BDD test metrics."""
        metrics = {}
        
        lines = output.split('\n')
        for line in lines:
            if 'scenario' in line.lower() and ('passed' in line or 'failed' in line):
                # Parse BDD summary
                if 'passed' in line:
                    parts = line.split()
                    for i, part in enumerate(parts):
                        if 'passed' in part and i > 0:
                            try:
                                metrics['scenarios_passed'] = int(parts[i-1])
                            except (ValueError, IndexError):
                                pass
                if 'failed' in line:
                    parts = line.split()
                    for i, part in enumerate(parts):
                        if 'failed' in part and i > 0:
                            try:
                                metrics['scenarios_failed'] = int(parts[i-1])
                            except (ValueError, IndexError):
                                pass
        
        return metrics
    
    def run_unit_tests(self, coverage: bool = True, fail_under: int = 90) -> bool:
        """Run unit tests with coverage."""
        command = [
            "poetry", "run", "pytest", "tests/unit/", 
            "-v", "--tb=short"
        ]
        
        if coverage:
            command.extend([
                "--cov=app",
                "--cov-report=term-missing",
                "--cov-report=xml",
                "--cov-report=html",
                f"--cov-fail-under={fail_under}"
            ])
        
        result = self.run_command(command, "unit_tests")
        return result["success"]
    
    def run_integration_tests(self) -> bool:
        """Run integration tests."""
        command = [
            "poetry", "run", "pytest", "tests/integration/", 
            "-v", "--tb=short", "-m", "integration"
        ]
        
        result = self.run_command(command, "integration_tests")
        return result["success"]
    
    def run_adhd_specific_tests(self) -> bool:
        """Run ADHD-specific feature tests."""
        command = [
            "poetry", "run", "pytest", "tests/", 
            "-v", "--tb=short", "-m", "adhd",
            "--cov=app", "--cov-report=xml:adhd-coverage.xml"
        ]
        
        result = self.run_command(command, "adhd_tests")
        return result["success"]
    
    def run_bdd_tests(self) -> bool:
        """Run behavior-driven development tests."""
        command = [
            "poetry", "run", "behave", "tests/behavior/",
            "--format=pretty", "--format=json",
            "--outfile=bdd-results.json"
        ]
        
        result = self.run_command(command, "bdd_tests")
        return result["success"]
    
    def run_performance_tests(self) -> bool:
        """Run performance tests (if available)."""
        perf_test_dir = self.project_root / "tests" / "performance"
        if not perf_test_dir.exists():
            print("⏭️  No performance tests found, skipping...")
            return True
        
        command = [
            "poetry", "run", "pytest", "tests/performance/", 
            "-v", "--tb=short", "-m", "performance"
        ]
        
        result = self.run_command(command, "performance_tests")
        return result["success"]
    
    def validate_adhd_features(self) -> bool:
        """Validate ADHD-specific features are working."""
        print("🧠 Validating ADHD-specific features...")
        
        validation_results = []
        
        # Check if ADHD test markers exist
        adhd_test_files = list(self.project_root.glob("tests/**/test_*adhd*.py"))
        if adhd_test_files:
            validation_results.append("✅ ADHD-specific test files found")
        else:
            validation_results.append("❌ No ADHD-specific test files found")
        
        # Check if BDD features exist
        bdd_features = list(self.project_root.glob("tests/behavior/features/*.feature"))
        if bdd_features:
            validation_results.append("✅ BDD feature files found")
        else:
            validation_results.append("❌ No BDD feature files found")
        
        # Check test results for ADHD markers
        if "adhd_tests" in self.results:
            adhd_result = self.results["adhd_tests"]
            if adhd_result["success"]:
                validation_results.append("✅ ADHD-specific tests passed")
            else:
                validation_results.append("❌ ADHD-specific tests failed")
        
        for result in validation_results:
            print(f"  {result}")
        
        success_count = sum(1 for r in validation_results if r.startswith("✅"))
        total_count = len(validation_results)
        success = success_count >= (total_count * 0.8)  # 80% must pass
        
        self.results["adhd_validation"] = {
            "success": success,
            "results": validation_results,
            "success_rate": success_count / total_count if total_count > 0 else 0
        }
        
        return success
    
    def run_all_tests(
        self, 
        include_performance: bool = False,
        coverage_threshold: int = 90
    ) -> bool:
        """
        Run all test suites.
        
        Args:
            include_performance: Whether to run performance tests
            coverage_threshold: Minimum coverage percentage required
            
        Returns:
            True if all tests pass
        """
        print("🚀 Starting comprehensive test suite for Project Chronos...")
        print("=" * 60)
        
        test_suites = [
            ("Unit Tests", lambda: self.run_unit_tests(coverage=True, fail_under=coverage_threshold)),
            ("Integration Tests", self.run_integration_tests),
            ("ADHD Feature Tests", self.run_adhd_specific_tests),
            ("BDD Tests", self.run_bdd_tests),
        ]
        
        if include_performance:
            test_suites.append(("Performance Tests", self.run_performance_tests))
        
        all_passed = True
        
        for suite_name, suite_func in test_suites:
            print(f"\n📋 {suite_name}")
            print("-" * 40)
            
            try:
                passed = suite_func()
                if not passed:
                    all_passed = False
            except Exception as e:
                print(f"❌ {suite_name} failed with exception: {e}")
                all_passed = False
        
        # Validate ADHD features
        print(f"\n📋 ADHD Feature Validation")
        print("-" * 40)
        adhd_validation_passed = self.validate_adhd_features()
        if not adhd_validation_passed:
            all_passed = False
        
        # Generate summary
        total_duration = time.time() - self.start_time
        print("\n" + "=" * 60)
        print(f"🏁 Test execution completed in {total_duration:.2f}s")
        
        if all_passed:
            print("🎉 All tests passed!")
        else:
            print("💥 Some tests failed!")
        
        return all_passed
    
    def generate_report(self, output_file: Optional[Path] = None) -> Dict[str, Any]:
        """Generate comprehensive test report."""
        total_duration = time.time() - self.start_time
        
        report = {
            "timestamp": datetime.now().isoformat(),
            "project_root": str(self.project_root),
            "total_duration": total_duration,
            "test_results": self.results,
            "summary": {
                "total_suites": len(self.results),
                "passed_suites": sum(1 for r in self.results.values() if r.get("success", False)),
                "failed_suites": sum(1 for r in self.results.values() if not r.get("success", True)),
                "overall_success": all(r.get("success", False) for r in self.results.values())
            }
        }
        
        # Add test metrics summary
        total_tests = 0
        total_passed = 0
        total_failed = 0
        
        for result in self.results.values():
            if "passed" in result:
                total_tests += result.get("passed", 0)
                total_passed += result.get("passed", 0)
            if "failed" in result:
                total_tests += result.get("failed", 0)
                total_failed += result.get("failed", 0)
        
        report["test_metrics"] = {
            "total_tests": total_tests,
            "total_passed": total_passed,
            "total_failed": total_failed,
            "pass_rate": (total_passed / total_tests * 100) if total_tests > 0 else 0
        }
        
        if output_file:
            with open(output_file, 'w') as f:
                json.dump(report, f, indent=2)
            print(f"📄 Test report written to {output_file}")
        
        return report


def main():
    """Main entry point for test runner."""
    parser = argparse.ArgumentParser(
        description="Run comprehensive tests for Project Chronos ADHD features"
    )
    parser.add_argument(
        "--unit-only",
        action="store_true",
        help="Run only unit tests"
    )
    parser.add_argument(
        "--integration-only",
        action="store_true",
        help="Run only integration tests"
    )
    parser.add_argument(
        "--adhd-only",
        action="store_true",
        help="Run only ADHD-specific tests"
    )
    parser.add_argument(
        "--bdd-only",
        action="store_true",
        help="Run only BDD tests"
    )
    parser.add_argument(
        "--include-performance",
        action="store_true",
        help="Include performance tests"
    )
    parser.add_argument(
        "--coverage-threshold",
        type=int,
        default=90,
        help="Minimum coverage percentage required"
    )
    parser.add_argument(
        "--report",
        type=Path,
        help="Output file for detailed test report"
    )
    parser.add_argument(
        "--project-root",
        type=Path,
        default=Path.cwd(),
        help="Project root directory"
    )
    
    args = parser.parse_args()
    
    # Ensure we're in a Poetry project
    if not (args.project_root / "pyproject.toml").exists():
        print("❌ No pyproject.toml found. Please run from project root.")
        sys.exit(1)
    
    runner = TestRunner(args.project_root)
    
    try:
        # Run specific test types if requested
        if args.unit_only:
            success = runner.run_unit_tests(coverage=True, fail_under=args.coverage_threshold)
        elif args.integration_only:
            success = runner.run_integration_tests()
        elif args.adhd_only:
            success = runner.run_adhd_specific_tests()
        elif args.bdd_only:
            success = runner.run_bdd_tests()
        else:
            # Run all tests
            success = runner.run_all_tests(
                include_performance=args.include_performance,
                coverage_threshold=args.coverage_threshold
            )
        
        if args.report:
            runner.generate_report(args.report)
        
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        print("\n🛑 Test execution interrupted by user")
        sys.exit(130)
    except Exception as e:
        print(f"💥 Test execution failed with unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
