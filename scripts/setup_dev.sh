#!/bin/bash
# Development setup script for Project Chronos
# This script sets up the development environment for ADHD-focused productivity app

set -e

echo "🧠 Setting up Project Chronos development environment..."

# Check if Poetry is installed
if ! command -v poetry &> /dev/null; then
    echo "❌ Poetry is not installed. Please install Poetry first:"
    echo "   curl -sSL https://install.python-poetry.org | python3 -"
    exit 1
fi

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is available
if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
    echo "❌ Docker Compose is not available. Please install Docker Compose."
    exit 1
fi

echo "✅ Prerequisites check passed"

# Install Python dependencies
echo "📦 Installing Python dependencies..."
poetry install

# Copy environment file if it doesn't exist
if [ ! -f .env ]; then
    echo "📝 Creating .env file from template..."
    cp .env.example .env
    echo "⚠️  Please update .env file with your configuration"
fi

# Start development services
echo "🐳 Starting development services..."
docker-compose -f docker/docker-compose.dev.yml up -d

# Wait for services to be ready
echo "⏳ Waiting for services to be ready..."
sleep 10

# Check if PostgreSQL is ready
echo "🔍 Checking PostgreSQL connection..."
max_attempts=30
attempt=1

while [ $attempt -le $max_attempts ]; do
    if docker exec chronos_postgres_dev pg_isready -U chronos -d chronos > /dev/null 2>&1; then
        echo "✅ PostgreSQL is ready"
        break
    fi
    
    if [ $attempt -eq $max_attempts ]; then
        echo "❌ PostgreSQL failed to start after $max_attempts attempts"
        exit 1
    fi
    
    echo "⏳ Waiting for PostgreSQL... (attempt $attempt/$max_attempts)"
    sleep 2
    ((attempt++))
done

# Check if Redis is ready
echo "🔍 Checking Redis connection..."
if docker exec chronos_redis_dev redis-cli ping > /dev/null 2>&1; then
    echo "✅ Redis is ready"
else
    echo "❌ Redis is not responding"
    exit 1
fi

# Run database migrations
echo "🗄️  Running database migrations..."
poetry run alembic upgrade head

echo "🎉 Development environment setup complete!"
echo ""
echo "📋 Next steps:"
echo "   1. Update .env file with your configuration"
echo "   2. Start the development server: poetry run uvicorn chronos.app.main:app --reload"
echo "   3. Access the API docs at: http://localhost:8000/docs"
echo "   4. Access Adminer (DB admin) at: http://localhost:8080"
echo "   5. Access Redis Commander at: http://localhost:8081"
echo ""
echo "🧪 To run tests:"
echo "   poetry run pytest"
echo ""
echo "🛑 To stop services:"
echo "   docker-compose -f docker/docker-compose.dev.yml down"
