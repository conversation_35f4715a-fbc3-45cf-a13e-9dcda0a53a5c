#!/usr/bin/env python3
"""
Comprehensive test runner for Project Chronos.

This script provides various test execution modes optimized for ADHD development
workflows, including quick feedback loops and comprehensive validation.
"""

import argparse
import subprocess
import sys
import os
from pathlib import Path
from typing import List, Dict, Any
import time


class ChronosTestRunner:
    """Test runner with ADHD-developer-friendly features."""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.test_dir = self.project_root / "tests"
        
    def run_quick_tests(self) -> int:
        """Run quick tests for immediate feedback (ADHD-friendly)."""
        print("🚀 Running quick tests for immediate feedback...")
        
        cmd = [
            "python", "-m", "pytest",
            str(self.test_dir / "unit"),
            "-v",
            "--tb=short",
            "-x",  # Stop on first failure
            "--durations=10",
            "-m", "not slow",
            "--disable-warnings"
        ]
        
        return self._run_command(cmd, "Quick Tests")
    
    def run_adhd_critical_tests(self) -> int:
        """Run ADHD-critical feature tests."""
        print("🧠 Running ADHD-critical feature tests...")
        
        cmd = [
            "python", "-m", "pytest",
            str(self.test_dir),
            "-v",
            "--tb=short",
            "-m", "adhd_critical",
            "--durations=10"
        ]
        
        return self._run_command(cmd, "ADHD Critical Tests")
    
    def run_performance_tests(self) -> int:
        """Run performance tests for ADHD response time requirements."""
        print("⚡ Running performance tests...")
        
        cmd = [
            "python", "-m", "pytest",
            str(self.test_dir / "performance"),
            "-v",
            "--tb=short",
            "-m", "performance",
            "--durations=20"
        ]
        
        return self._run_command(cmd, "Performance Tests")
    
    def run_integration_tests(self) -> int:
        """Run integration tests for ADHD workflows."""
        print("🔗 Running integration tests...")
        
        cmd = [
            "python", "-m", "pytest",
            str(self.test_dir / "integration"),
            "-v",
            "--tb=short",
            "--durations=15"
        ]
        
        return self._run_command(cmd, "Integration Tests")
    
    def run_behavior_tests(self) -> int:
        """Run behavior-driven tests for ADHD user scenarios."""
        print("🎭 Running behavior-driven tests...")
        
        cmd = [
            "python", "-m", "pytest",
            str(self.test_dir / "behavior"),
            "-v",
            "--tb=short",
            "--gherkin-terminal-reporter"
        ]
        
        return self._run_command(cmd, "Behavior Tests")
    
    def run_api_tests(self) -> int:
        """Run API tests for ADHD-specific endpoints."""
        print("🌐 Running API tests...")
        
        cmd = [
            "python", "-m", "pytest",
            str(self.test_dir),
            "-v",
            "--tb=short",
            "-m", "api",
            "--durations=10"
        ]
        
        return self._run_command(cmd, "API Tests")
    
    def run_comprehensive_tests(self) -> int:
        """Run comprehensive test suite."""
        print("🔍 Running comprehensive test suite...")
        
        cmd = [
            "python", "-m", "pytest",
            str(self.test_dir),
            "-v",
            "--tb=short",
            "--cov=app",
            "--cov-report=html",
            "--cov-report=term-missing",
            "--durations=20"
        ]
        
        return self._run_command(cmd, "Comprehensive Tests")
    
    def run_specific_test(self, test_path: str) -> int:
        """Run specific test file or function."""
        print(f"🎯 Running specific test: {test_path}")
        
        cmd = [
            "python", "-m", "pytest",
            test_path,
            "-v",
            "--tb=short",
            "-s"  # Don't capture output for debugging
        ]
        
        return self._run_command(cmd, f"Specific Test: {test_path}")
    
    def run_failed_tests(self) -> int:
        """Re-run only failed tests (ADHD-friendly for fixing issues)."""
        print("🔄 Re-running failed tests...")
        
        cmd = [
            "python", "-m", "pytest",
            "--lf",  # Last failed
            "-v",
            "--tb=short",
            "-x"  # Stop on first failure
        ]
        
        return self._run_command(cmd, "Failed Tests")
    
    def run_coverage_report(self) -> int:
        """Generate detailed coverage report."""
        print("📊 Generating coverage report...")
        
        cmd = [
            "python", "-m", "pytest",
            str(self.test_dir),
            "--cov=app",
            "--cov-report=html:htmlcov",
            "--cov-report=xml",
            "--cov-report=term-missing",
            "--cov-fail-under=80"
        ]
        
        result = self._run_command(cmd, "Coverage Report")
        
        if result == 0:
            print("📈 Coverage report generated in htmlcov/index.html")
        
        return result
    
    def run_lint_and_format(self) -> int:
        """Run linting and formatting checks."""
        print("🧹 Running linting and formatting checks...")
        
        # Run black formatting check
        black_cmd = ["python", "-m", "black", "--check", "--diff", "app", "tests"]
        black_result = self._run_command(black_cmd, "Black Formatting Check")
        
        # Run isort import sorting check
        isort_cmd = ["python", "-m", "isort", "--check-only", "--diff", "app", "tests"]
        isort_result = self._run_command(isort_cmd, "Import Sorting Check")
        
        # Run flake8 linting
        flake8_cmd = ["python", "-m", "flake8", "app", "tests"]
        flake8_result = self._run_command(flake8_cmd, "Flake8 Linting")
        
        # Run mypy type checking
        mypy_cmd = ["python", "-m", "mypy", "app"]
        mypy_result = self._run_command(mypy_cmd, "MyPy Type Checking")
        
        return max(black_result, isort_result, flake8_result, mypy_result)
    
    def run_security_tests(self) -> int:
        """Run security vulnerability tests."""
        print("🔒 Running security tests...")
        
        # Run bandit security linting
        bandit_cmd = ["python", "-m", "bandit", "-r", "app", "-f", "json"]
        bandit_result = self._run_command(bandit_cmd, "Bandit Security Check")
        
        # Run safety dependency check
        safety_cmd = ["python", "-m", "safety", "check"]
        safety_result = self._run_command(safety_cmd, "Safety Dependency Check")
        
        return max(bandit_result, safety_result)
    
    def _run_command(self, cmd: List[str], test_name: str) -> int:
        """Run command and provide ADHD-friendly feedback."""
        start_time = time.time()
        
        try:
            result = subprocess.run(
                cmd,
                cwd=self.project_root,
                capture_output=False,
                text=True
            )
            
            end_time = time.time()
            duration = end_time - start_time
            
            if result.returncode == 0:
                print(f"✅ {test_name} passed in {duration:.2f}s")
            else:
                print(f"❌ {test_name} failed in {duration:.2f}s")
            
            return result.returncode
            
        except Exception as e:
            print(f"💥 Error running {test_name}: {e}")
            return 1
    
    def print_help(self):
        """Print ADHD-friendly help information."""
        print("""
🧠 Project Chronos Test Runner - ADHD Developer Friendly

Quick Commands (for immediate feedback):
  quick       - Run fast unit tests only
  adhd        - Run ADHD-critical feature tests
  failed      - Re-run only failed tests
  specific    - Run specific test file/function

Comprehensive Commands:
  all         - Run complete test suite with coverage
  performance - Run performance tests
  integration - Run integration tests
  behavior    - Run behavior-driven tests
  api         - Run API endpoint tests

Quality Commands:
  lint        - Run linting and formatting checks
  security    - Run security vulnerability tests
  coverage    - Generate detailed coverage report

Examples:
  python scripts/run_tests.py quick
  python scripts/run_tests.py specific tests/unit/test_task_service.py
  python scripts/run_tests.py specific tests/unit/test_task_service.py::test_create_task
  python scripts/run_tests.py adhd
        """)


def main():
    """Main entry point for test runner."""
    parser = argparse.ArgumentParser(
        description="ADHD-friendly test runner for Project Chronos",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    parser.add_argument(
        "command",
        choices=[
            "quick", "adhd", "performance", "integration", "behavior", 
            "api", "all", "failed", "specific", "lint", "security", "coverage"
        ],
        help="Test command to run"
    )
    
    parser.add_argument(
        "test_path",
        nargs="?",
        help="Specific test path (for 'specific' command)"
    )
    
    args = parser.parse_args()
    
    runner = ChronosTestRunner()
    
    if args.command == "quick":
        return runner.run_quick_tests()
    elif args.command == "adhd":
        return runner.run_adhd_critical_tests()
    elif args.command == "performance":
        return runner.run_performance_tests()
    elif args.command == "integration":
        return runner.run_integration_tests()
    elif args.command == "behavior":
        return runner.run_behavior_tests()
    elif args.command == "api":
        return runner.run_api_tests()
    elif args.command == "all":
        return runner.run_comprehensive_tests()
    elif args.command == "failed":
        return runner.run_failed_tests()
    elif args.command == "specific":
        if not args.test_path:
            print("❌ Please provide test path for specific command")
            return 1
        return runner.run_specific_test(args.test_path)
    elif args.command == "lint":
        return runner.run_lint_and_format()
    elif args.command == "security":
        return runner.run_security_tests()
    elif args.command == "coverage":
        return runner.run_coverage_report()
    else:
        runner.print_help()
        return 0


if __name__ == "__main__":
    sys.exit(main())
