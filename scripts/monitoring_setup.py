#!/usr/bin/env python3
"""
Monitoring and alerting setup for Project Chronos ADHD-focused features.

This script sets up comprehensive monitoring for ADHD-specific functionality
including performance metrics, error rates, and user experience indicators.
"""

import json
import yaml
from pathlib import Path
from typing import Dict, List, Any
import argparse


class MonitoringSetup:
    """
    Setup monitoring and alerting for ADHD-focused application.
    
    Configures monitoring for ADHD-specific metrics including:
    - Response times for critical ADHD features
    - AI chunking service availability
    - Task jar performance
    - User experience indicators
    """
    
    def __init__(self, project_root: Path):
        """Initialize monitoring setup."""
        self.project_root = project_root
        self.monitoring_dir = project_root / "monitoring"
        self.monitoring_dir.mkdir(exist_ok=True)
    
    def generate_prometheus_config(self) -> Dict[str, Any]:
        """Generate Prometheus configuration for ADHD metrics."""
        config = {
            "global": {
                "scrape_interval": "15s",
                "evaluation_interval": "15s"
            },
            "rule_files": [
                "adhd_alerts.yml"
            ],
            "scrape_configs": [
                {
                    "job_name": "chronos-api",
                    "static_configs": [
                        {
                            "targets": ["localhost:8000"]
                        }
                    ],
                    "metrics_path": "/metrics",
                    "scrape_interval": "10s"
                },
                {
                    "job_name": "chronos-adhd-features",
                    "static_configs": [
                        {
                            "targets": ["localhost:8000"]
                        }
                    ],
                    "metrics_path": "/metrics/adhd",
                    "scrape_interval": "5s"  # More frequent for ADHD-critical metrics
                }
            ],
            "alerting": {
                "alertmanagers": [
                    {
                        "static_configs": [
                            {
                                "targets": ["localhost:9093"]
                            }
                        ]
                    }
                ]
            }
        }
        
        return config
    
    def generate_adhd_alert_rules(self) -> Dict[str, Any]:
        """Generate alert rules for ADHD-specific issues."""
        rules = {
            "groups": [
                {
                    "name": "adhd_critical_features",
                    "rules": [
                        {
                            "alert": "ADHDTaskCreationSlow",
                            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{endpoint=\"/api/v1/tasks/\", method=\"POST\"}[5m])) > 0.5",
                            "for": "2m",
                            "labels": {
                                "severity": "warning",
                                "feature": "task_creation",
                                "adhd_impact": "high"
                            },
                            "annotations": {
                                "summary": "ADHD task creation is too slow",
                                "description": "Task creation response time is {{ $value }}s, which may frustrate ADHD users who need quick interactions."
                            }
                        },
                        {
                            "alert": "ADHDTaskJarUnavailable",
                            "expr": "rate(http_requests_total{endpoint=\"/api/v1/tasks/jar\", status=~\"5..\"}[5m]) > 0.01",
                            "for": "1m",
                            "labels": {
                                "severity": "critical",
                                "feature": "task_jar",
                                "adhd_impact": "critical"
                            },
                            "annotations": {
                                "summary": "ADHD task jar feature failing",
                                "description": "Task jar is failing at {{ $value }} requests/sec, breaking decision fatigue reduction for ADHD users."
                            }
                        },
                        {
                            "alert": "AIChunkingServiceDown",
                            "expr": "rate(http_requests_total{endpoint=\"/api/v1/tasks/*/chunk\", status=~\"5..\"}[5m]) > 0.1",
                            "for": "3m",
                            "labels": {
                                "severity": "warning",
                                "feature": "ai_chunking",
                                "adhd_impact": "high"
                            },
                            "annotations": {
                                "summary": "AI chunking service degraded",
                                "description": "AI chunking is failing at {{ $value }} requests/sec, preventing task paralysis solutions for ADHD users."
                            }
                        },
                        {
                            "alert": "ADHDFilteringPerformance",
                            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{endpoint=~\"/api/v1/tasks/.*energy_level.*\"}[5m])) > 1.0",
                            "for": "5m",
                            "labels": {
                                "severity": "warning",
                                "feature": "energy_filtering",
                                "adhd_impact": "medium"
                            },
                            "annotations": {
                                "summary": "ADHD energy filtering is slow",
                                "description": "Energy level filtering taking {{ $value }}s, may impact ADHD user workflow."
                            }
                        },
                        {
                            "alert": "ADHDUserExperienceIssues",
                            "expr": "rate(http_requests_total{status=~\"4..|5..\"}[5m]) / rate(http_requests_total[5m]) > 0.05",
                            "for": "2m",
                            "labels": {
                                "severity": "warning",
                                "feature": "overall_ux",
                                "adhd_impact": "high"
                            },
                            "annotations": {
                                "summary": "High error rate affecting ADHD users",
                                "description": "Error rate is {{ $value | humanizePercentage }}, which can be particularly disruptive for ADHD users."
                            }
                        },
                        {
                            "alert": "ADHDDatabasePerformance",
                            "expr": "histogram_quantile(0.95, rate(db_query_duration_seconds_bucket{table=~\"tasks|users\"}[5m])) > 0.1",
                            "for": "3m",
                            "labels": {
                                "severity": "warning",
                                "feature": "database",
                                "adhd_impact": "medium"
                            },
                            "annotations": {
                                "summary": "Database queries slow for ADHD features",
                                "description": "Database queries taking {{ $value }}s, may impact ADHD feature responsiveness."
                            }
                        }
                    ]
                },
                {
                    "name": "adhd_user_behavior",
                    "rules": [
                        {
                            "alert": "ADHDHighTaskCreationRate",
                            "expr": "rate(adhd_tasks_created_total[5m]) > 10",
                            "for": "5m",
                            "labels": {
                                "severity": "info",
                                "feature": "user_behavior",
                                "adhd_impact": "low"
                            },
                            "annotations": {
                                "summary": "High ADHD task creation rate detected",
                                "description": "ADHD users creating {{ $value }} tasks/sec, may indicate hyperfocus period or system issue."
                            }
                        },
                        {
                            "alert": "ADHDLowEngagementPattern",
                            "expr": "rate(adhd_user_interactions_total[1h]) < 0.1",
                            "for": "30m",
                            "labels": {
                                "severity": "info",
                                "feature": "user_engagement",
                                "adhd_impact": "low"
                            },
                            "annotations": {
                                "summary": "Low ADHD user engagement detected",
                                "description": "ADHD user interactions at {{ $value }}/hour, may indicate usability issues."
                            }
                        }
                    ]
                }
            ]
        }
        
        return rules
    
    def generate_grafana_dashboard(self) -> Dict[str, Any]:
        """Generate Grafana dashboard for ADHD metrics."""
        dashboard = {
            "dashboard": {
                "id": None,
                "title": "Project Chronos - ADHD Features Monitoring",
                "tags": ["chronos", "adhd", "performance"],
                "timezone": "browser",
                "panels": [
                    {
                        "id": 1,
                        "title": "ADHD Task Creation Performance",
                        "type": "graph",
                        "targets": [
                            {
                                "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{endpoint=\"/api/v1/tasks/\", method=\"POST\"}[5m]))",
                                "legendFormat": "95th Percentile",
                                "refId": "A"
                            },
                            {
                                "expr": "histogram_quantile(0.50, rate(http_request_duration_seconds_bucket{endpoint=\"/api/v1/tasks/\", method=\"POST\"}[5m]))",
                                "legendFormat": "Median",
                                "refId": "B"
                            }
                        ],
                        "yAxes": [
                            {
                                "label": "Response Time (seconds)",
                                "max": 2,
                                "min": 0
                            }
                        ],
                        "alert": {
                            "conditions": [
                                {
                                    "query": {"params": ["A", "5m", "now"]},
                                    "reducer": {"params": [], "type": "last"},
                                    "evaluator": {"params": [0.5], "type": "gt"}
                                }
                            ],
                            "executionErrorState": "alerting",
                            "for": "2m",
                            "frequency": "10s",
                            "handler": 1,
                            "name": "ADHD Task Creation Alert",
                            "noDataState": "no_data",
                            "notifications": []
                        },
                        "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}
                    },
                    {
                        "id": 2,
                        "title": "ADHD Feature Availability",
                        "type": "stat",
                        "targets": [
                            {
                                "expr": "up{job=\"chronos-adhd-features\"}",
                                "legendFormat": "ADHD Features",
                                "refId": "A"
                            }
                        ],
                        "fieldConfig": {
                            "defaults": {
                                "color": {"mode": "thresholds"},
                                "thresholds": {
                                    "steps": [
                                        {"color": "red", "value": 0},
                                        {"color": "green", "value": 1}
                                    ]
                                }
                            }
                        },
                        "gridPos": {"h": 4, "w": 6, "x": 12, "y": 0}
                    },
                    {
                        "id": 3,
                        "title": "AI Chunking Success Rate",
                        "type": "graph",
                        "targets": [
                            {
                                "expr": "rate(http_requests_total{endpoint=~\"/api/v1/tasks/.*/chunk\", status=~\"2..\"}[5m]) / rate(http_requests_total{endpoint=~\"/api/v1/tasks/.*/chunk\"}[5m])",
                                "legendFormat": "Success Rate",
                                "refId": "A"
                            }
                        ],
                        "yAxes": [
                            {
                                "label": "Success Rate",
                                "max": 1,
                                "min": 0
                            }
                        ],
                        "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}
                    },
                    {
                        "id": 4,
                        "title": "ADHD User Interaction Patterns",
                        "type": "graph",
                        "targets": [
                            {
                                "expr": "rate(adhd_tasks_created_total[5m])",
                                "legendFormat": "Task Creation Rate",
                                "refId": "A"
                            },
                            {
                                "expr": "rate(adhd_task_jar_requests_total[5m])",
                                "legendFormat": "Task Jar Requests",
                                "refId": "B"
                            },
                            {
                                "expr": "rate(adhd_energy_filter_requests_total[5m])",
                                "legendFormat": "Energy Filtering",
                                "refId": "C"
                            }
                        ],
                        "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}
                    }
                ],
                "time": {"from": "now-1h", "to": "now"},
                "timepicker": {},
                "timezone": "",
                "title": "Project Chronos - ADHD Features",
                "version": 1
            }
        }
        
        return dashboard
    
    def generate_alertmanager_config(self) -> Dict[str, Any]:
        """Generate Alertmanager configuration for ADHD alerts."""
        config = {
            "global": {
                "smtp_smarthost": "localhost:587",
                "smtp_from": "<EMAIL>"
            },
            "route": {
                "group_by": ["alertname", "feature"],
                "group_wait": "10s",
                "group_interval": "10s",
                "repeat_interval": "1h",
                "receiver": "web.hook",
                "routes": [
                    {
                        "match": {"adhd_impact": "critical"},
                        "receiver": "adhd-critical",
                        "group_wait": "5s",
                        "repeat_interval": "15m"
                    },
                    {
                        "match": {"adhd_impact": "high"},
                        "receiver": "adhd-high",
                        "group_wait": "10s",
                        "repeat_interval": "30m"
                    }
                ]
            },
            "receivers": [
                {
                    "name": "web.hook",
                    "webhook_configs": [
                        {
                            "url": "http://localhost:5001/webhook",
                            "send_resolved": True
                        }
                    ]
                },
                {
                    "name": "adhd-critical",
                    "email_configs": [
                        {
                            "to": "<EMAIL>",
                            "subject": "🚨 CRITICAL: ADHD Feature Issue - {{ .GroupLabels.alertname }}",
                            "body": """
CRITICAL ADHD Feature Alert

Alert: {{ .GroupLabels.alertname }}
Feature: {{ .GroupLabels.feature }}
ADHD Impact: {{ .GroupLabels.adhd_impact }}

{{ range .Alerts }}
Description: {{ .Annotations.description }}
{{ end }}

This alert indicates a critical issue affecting ADHD users.
Immediate attention required to maintain accessibility.
                            """
                        }
                    ],
                    "slack_configs": [
                        {
                            "api_url": "YOUR_SLACK_WEBHOOK_URL",
                            "channel": "#adhd-alerts",
                            "title": "🚨 Critical ADHD Feature Issue",
                            "text": "{{ .GroupLabels.alertname }} - {{ .GroupLabels.feature }}"
                        }
                    ]
                },
                {
                    "name": "adhd-high",
                    "email_configs": [
                        {
                            "to": "<EMAIL>",
                            "subject": "⚠️ HIGH: ADHD Feature Issue - {{ .GroupLabels.alertname }}",
                            "body": """
High Priority ADHD Feature Alert

Alert: {{ .GroupLabels.alertname }}
Feature: {{ .GroupLabels.feature }}
ADHD Impact: {{ .GroupLabels.adhd_impact }}

{{ range .Alerts }}
Description: {{ .Annotations.description }}
{{ end }}

This alert indicates an issue that may impact ADHD user experience.
Please investigate and resolve promptly.
                            """
                        }
                    ]
                }
            ]
        }
        
        return config
    
    def setup_monitoring(self) -> None:
        """Set up all monitoring configurations."""
        print("🔧 Setting up ADHD-focused monitoring...")
        
        # Generate Prometheus config
        prometheus_config = self.generate_prometheus_config()
        with open(self.monitoring_dir / "prometheus.yml", 'w') as f:
            yaml.dump(prometheus_config, f, default_flow_style=False)
        print("✅ Prometheus configuration generated")
        
        # Generate alert rules
        alert_rules = self.generate_adhd_alert_rules()
        with open(self.monitoring_dir / "adhd_alerts.yml", 'w') as f:
            yaml.dump(alert_rules, f, default_flow_style=False)
        print("✅ ADHD alert rules generated")
        
        # Generate Grafana dashboard
        dashboard = self.generate_grafana_dashboard()
        with open(self.monitoring_dir / "adhd_dashboard.json", 'w') as f:
            json.dump(dashboard, f, indent=2)
        print("✅ Grafana dashboard generated")
        
        # Generate Alertmanager config
        alertmanager_config = self.generate_alertmanager_config()
        with open(self.monitoring_dir / "alertmanager.yml", 'w') as f:
            yaml.dump(alertmanager_config, f, default_flow_style=False)
        print("✅ Alertmanager configuration generated")
        
        # Generate docker-compose for monitoring stack
        self.generate_monitoring_docker_compose()
        print("✅ Monitoring Docker Compose generated")
        
        print("\n🎉 ADHD-focused monitoring setup complete!")
        print(f"📁 Configuration files saved to: {self.monitoring_dir}")
        print("\n📋 Next steps:")
        print("1. Review and customize alert thresholds")
        print("2. Configure notification channels (email, Slack)")
        print("3. Start monitoring stack: docker-compose -f monitoring/docker-compose.yml up")
        print("4. Import Grafana dashboard from adhd_dashboard.json")
    
    def generate_monitoring_docker_compose(self) -> None:
        """Generate Docker Compose for monitoring stack."""
        compose_config = {
            "version": "3.8",
            "services": {
                "prometheus": {
                    "image": "prom/prometheus:latest",
                    "container_name": "chronos-prometheus",
                    "ports": ["9090:9090"],
                    "volumes": [
                        "./prometheus.yml:/etc/prometheus/prometheus.yml",
                        "./adhd_alerts.yml:/etc/prometheus/adhd_alerts.yml"
                    ],
                    "command": [
                        "--config.file=/etc/prometheus/prometheus.yml",
                        "--storage.tsdb.path=/prometheus",
                        "--web.console.libraries=/etc/prometheus/console_libraries",
                        "--web.console.templates=/etc/prometheus/consoles",
                        "--storage.tsdb.retention.time=200h",
                        "--web.enable-lifecycle"
                    ]
                },
                "alertmanager": {
                    "image": "prom/alertmanager:latest",
                    "container_name": "chronos-alertmanager",
                    "ports": ["9093:9093"],
                    "volumes": ["./alertmanager.yml:/etc/alertmanager/alertmanager.yml"],
                    "command": [
                        "--config.file=/etc/alertmanager/alertmanager.yml",
                        "--storage.path=/alertmanager",
                        "--web.external-url=http://localhost:9093"
                    ]
                },
                "grafana": {
                    "image": "grafana/grafana:latest",
                    "container_name": "chronos-grafana",
                    "ports": ["3000:3000"],
                    "environment": [
                        "GF_SECURITY_ADMIN_PASSWORD=admin"
                    ],
                    "volumes": [
                        "grafana-storage:/var/lib/grafana"
                    ]
                }
            },
            "volumes": {
                "grafana-storage": {}
            }
        }
        
        with open(self.monitoring_dir / "docker-compose.yml", 'w') as f:
            yaml.dump(compose_config, f, default_flow_style=False)


def main():
    """Main entry point for monitoring setup."""
    parser = argparse.ArgumentParser(
        description="Set up monitoring for Project Chronos ADHD features"
    )
    parser.add_argument(
        "--project-root",
        type=Path,
        default=Path.cwd(),
        help="Project root directory"
    )
    
    args = parser.parse_args()
    
    setup = MonitoringSetup(args.project_root)
    setup.setup_monitoring()


if __name__ == "__main__":
    main()
