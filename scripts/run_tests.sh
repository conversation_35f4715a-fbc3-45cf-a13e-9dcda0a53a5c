#!/bin/bash
# Test runner script for Project Chronos
# Runs comprehensive test suite for ADHD-focused features

set -e

echo "🧪 Running Project Chronos test suite..."

# Ensure test database is running
echo "🔍 Checking test database..."
if ! docker ps | grep chronos_postgres_test > /dev/null; then
    echo "🐳 Starting test database..."
    docker-compose -f docker/docker-compose.dev.yml up -d postgres_test
    sleep 5
fi

# Check if test database is ready
max_attempts=15
attempt=1

while [ $attempt -le $max_attempts ]; do
    if docker exec chronos_postgres_test pg_isready -U chronos -d chronos_test > /dev/null 2>&1; then
        echo "✅ Test database is ready"
        break
    fi
    
    if [ $attempt -eq $max_attempts ]; then
        echo "❌ Test database failed to start"
        exit 1
    fi
    
    echo "⏳ Waiting for test database... (attempt $attempt/$max_attempts)"
    sleep 2
    ((attempt++))
done

# Set test environment
export CHRONOS_TESTING=true
export CHRONOS_DATABASE_URL="postgresql+asyncpg://chronos:chronos_password@localhost:5433/chronos_test"

# Run different test suites based on arguments
case "${1:-all}" in
    "unit")
        echo "🔬 Running unit tests..."
        poetry run pytest tests/unit/ -v --cov=chronos --cov-report=term-missing
        ;;
    "integration")
        echo "🔗 Running integration tests..."
        poetry run pytest tests/integration/ -v
        ;;
    "models")
        echo "🗄️  Running model tests..."
        poetry run pytest tests/unit/test_models/ -v --cov=chronos.app.models --cov-report=term-missing
        ;;
    "adhd")
        echo "🧠 Running ADHD-specific tests..."
        poetry run pytest -m adhd -v
        ;;
    "coverage")
        echo "📊 Running tests with detailed coverage..."
        poetry run pytest --cov=chronos --cov-report=html --cov-report=term-missing --cov-fail-under=90
        echo "📋 Coverage report generated in htmlcov/index.html"
        ;;
    "quick")
        echo "⚡ Running quick test suite..."
        poetry run pytest tests/unit/test_models/ -v --tb=short
        ;;
    "all"|*)
        echo "🎯 Running complete test suite..."
        poetry run pytest -v --cov=chronos --cov-report=term-missing --cov-fail-under=80
        ;;
esac

echo "✅ Tests completed!"

# Optional: Run BDD tests if behave is available
if command -v behave &> /dev/null && [ -d "tests/behavior/features" ]; then
    echo "🎭 Running BDD tests..."
    poetry run behave tests/behavior/
fi
