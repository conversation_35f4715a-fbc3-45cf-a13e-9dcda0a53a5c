-- Create PostgreSQL extensions for Project Chronos
-- This script runs during database initialization

-- Enable UUID generation
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Enable full-text search capabilities
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Enable additional text search features
CREATE EXTENSION IF NOT EXISTS "unaccent";

-- Create indexes for better performance on ADHD-specific queries
-- These will be created after tables are set up via Alembic
