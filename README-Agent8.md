# Agent 8: Gamification & Motivation System

## Overview

Agent 8 implements a comprehensive gamification and motivation system specifically designed for ADHD users. It addresses the dopamine deficit that makes task initiation difficult by providing intrinsic motivation through points, achievements, streaks, and a personalized "dopamine menu" system.

## Key Features

### 🎯 ADHD-Optimized Gamification
- **Points System**: Earn points for task completion with ADHD-friendly multipliers
- **Level Progression**: Logarithmic leveling that prevents stagnation
- **Flexible Streaks**: Streak system with "freeze" options for ADHD flexibility
- **Achievement System**: Rule-based achievements with meaningful milestones

### 🌟 Dopamine Menu System
- **Personalized Activities**: Curated list of quick, enjoyable pre-task activities
- **Energy-Based Filtering**: Activities matched to current energy levels
- **Context Awareness**: Suggestions based on location, time, and available equipment
- **Custom Activities**: Users can create their own dopamine-boosting activities

### 📊 Motivation Analytics
- **Pattern Recognition**: Identify what activities work best for each user
- **Effectiveness Tracking**: Monitor mood and energy improvements
- **Personalized Insights**: AI-driven recommendations for motivation strategies
- **Progress Visualization**: Clear progress indicators and celebration animations

## Architecture

### Database Models

#### Gamification Models
- `UserGamification`: User's overall gamification profile
- `PointsAward`: Individual point awards with metadata
- `Achievement`: Achievement definitions and requirements
- `UserAchievement`: User progress toward achievements
- `UserStreak`: Flexible streak tracking

#### Motivation Models
- `DopamineActivity`: Available dopamine menu activities
- `UserDopaminePreference`: User preferences and custom activities
- `DopamineActivityCompletion`: Activity completion tracking
- `MotivationInsight`: AI-generated motivation insights

### Services

#### GamificationService
```python
# Award points with ADHD-optimized multipliers
await gamification_service.award_points(
    user_id=user_id,
    points=50,
    reason="Completed difficult task",
    multiplier=1.5,  # Difficulty bonus
    metadata={"energy_level": "low"}
)

# Update streaks with flexibility
streak, changed = await gamification_service.update_streak(
    user_id=user_id,
    streak_type="daily_tasks",
    action_completed=True
)
```

#### AchievementService
```python
# Check for new achievements
achievements = await achievement_service.check_achievements(
    user_id=user_id,
    trigger_event="task_completed",
    event_data={"task_count": 10}
)
```

#### MotivationService
```python
# Get personalized dopamine menu
menu = await motivation_service.get_dopamine_menu(
    user_id=user_id,
    request=DopamineMenuRequest(
        energy_level="low",
        available_time=5,
        context="pre_task"
    )
)
```

### API Endpoints

#### Gamification Endpoints
- `GET /gamification/profile` - Get user's gamification profile
- `GET /gamification/stats` - Get comprehensive statistics
- `GET /gamification/achievements` - Get user achievements
- `POST /gamification/streaks/update` - Update streak status
- `GET /gamification/dashboard` - Get dashboard data

#### Motivation Endpoints
- `GET /motivation/dopamine-menu` - Get personalized activity suggestions
- `POST /motivation/dopamine-menu/complete` - Record activity completion
- `POST /motivation/activities/custom` - Create custom activity
- `GET /motivation/analytics` - Get motivation analytics
- `GET /motivation/dashboard` - Get motivation dashboard

## ADHD-Specific Features

### 1. Dopamine-Driven Design
- **Immediate Rewards**: Points awarded instantly for any progress
- **Variable Rewards**: Multipliers and bonuses create excitement
- **Choice Architecture**: Multiple paths to success and rewards

### 2. Flexible Streak System
- **Streak Freezes**: Allow missed days without breaking streaks
- **Recovery Options**: Ways to rebuild broken streaks
- **Multiple Streak Types**: Different streaks for different activities

### 3. Energy-Aware Multipliers
- **Low Energy Bonus**: Extra points for pushing through low energy
- **Time-Based Rewards**: Bonuses for difficult times (morning, evening)
- **Context Sensitivity**: Rewards adapted to user's current state

### 4. Overwhelming Task Support
- **Chunked Task Bonuses**: Extra points for completing subtasks
- **Initiation Rewards**: Points just for starting difficult tasks
- **Progress Celebration**: Frequent positive feedback

## Integration with Other Agents

### Task Management Integration
```python
# Automatic gamification on task completion
results = await gamification_integration.handle_task_completion(
    task=completed_task,
    user_id=user_id,
    completion_context={
        "user_energy_level": "low",
        "first_task_of_day": True
    }
)
```

### Focus Session Integration (Future)
- Points for completing focus sessions
- Achievements for focus milestones
- Dopamine activities for focus breaks

### Notification Integration (Future)
- Achievement unlock notifications
- Level up celebrations
- Streak reminders

## Usage Examples

### Basic Gamification Flow
```python
# User completes a task
task.mark_completed()

# System automatically:
# 1. Awards points based on task difficulty
# 2. Applies ADHD-friendly multipliers
# 3. Updates daily task streak
# 4. Checks for achievement unlocks
# 5. Triggers level up if applicable

# User sees immediate feedback:
# - Points earned: 45 (30 base + 1.5x multiplier)
# - Achievement unlocked: "Week Warrior"
# - Level up: Welcome to Level 3!
```

### Dopamine Menu Usage
```python
# User needs motivation before starting work
menu_request = DopamineMenuRequest(
    energy_level="low",
    available_time=5,
    context="pre_task"
)

menu = await motivation_service.get_dopamine_menu(user_id, menu_request)

# System suggests:
# - 5-minute walk (outdoor, energy boost)
# - Deep breathing (quick, calming)
# - Favorite song (instant mood lift)

# User completes activity and gets:
# - Small point reward
# - Energy/mood tracking
# - Improved future suggestions
```

## Testing

### Running Tests
```bash
# Run gamification tests
pytest tests/test_gamification_service.py -v

# Run motivation tests
pytest tests/test_motivation_service.py -v

# Run integration tests
pytest tests/test_gamification_integration.py -v
```

### Test Coverage
- Unit tests for all services
- Integration tests for task completion flow
- API endpoint tests
- Database model tests

## Initialization

### Setup Gamification System
```bash
# Initialize achievements and dopamine activities
python scripts/init_gamification.py
```

### Manual Initialization
```python
# Initialize achievements
achievement_service = AchievementService(db)
await achievement_service.initialize_achievements()

# Initialize dopamine activities
motivation_service = MotivationService(db)
await motivation_service.initialize_default_activities()
```

## Configuration

### Environment Variables
```env
# Gamification settings
GAMIFICATION_ENABLED=true
CELEBRATION_STYLE=moderate  # minimal, moderate, enthusiastic
ACHIEVEMENT_NOTIFICATIONS=true

# Motivation settings
DOPAMINE_MENU_SIZE=5
PERSONALIZATION_LEARNING=true
```

### User Preferences
```python
# Update user gamification preferences
await gamification_service.update_profile(
    user_id=user_id,
    preferences={
        "celebration_style": "enthusiastic",
        "preferred_rewards": ["badges", "themes"],
        "gamification_enabled": True
    }
)
```

## Future Enhancements

### Planned Features
- Social achievements and leaderboards
- Seasonal events and challenges
- Real-world reward integration
- Advanced AI-driven insights
- Customizable celebration animations

### Integration Roadmap
- Focus session gamification
- Body doubling achievements
- Notification system integration
- Mobile app gamification features

## Contributing

When contributing to Agent 8:

1. **Follow ADHD-First Design**: Always consider the ADHD user experience
2. **Test Thoroughly**: Include both unit and integration tests
3. **Document Clearly**: Explain the motivation behind gamification choices
4. **Consider Edge Cases**: Handle low motivation and difficult days gracefully

## Support

For questions about the gamification system:
- Check the test files for usage examples
- Review the PRD document for design rationale
- Test with the initialization script for setup issues
